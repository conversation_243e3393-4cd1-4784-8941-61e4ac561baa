package com.kuaikan.role.game.admin.battle.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleMonsterLevelGrowthConfig;

public interface MonsterLevelGrowthConfigDao {

    int batchInsert(@Param("list") List<CardBattleMonsterLevelGrowthConfig> params);

    List<CardBattleMonsterLevelGrowthConfig> selectAll();

    CardBattleMonsterLevelGrowthConfig getMaxLevelByType(int type);
}
