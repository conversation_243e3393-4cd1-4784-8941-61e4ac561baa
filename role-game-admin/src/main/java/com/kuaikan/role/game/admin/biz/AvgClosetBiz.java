package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.admin.component.QiniuComponent.PAY_BUCKET_NAME;
import static com.kuaikan.role.game.admin.config.ThreadPoolConfig.BIZ_EXECUTOR;
import static com.kuaikan.role.game.api.rpc.result.AvgChapterModelV2.PLAYER_REPLACE;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qiniu.util.UrlSafeBase64;

import com.kuaikan.audit.common.bean.avg.TopicRoleInfo;
import com.kuaikan.cdn.core.CdnHandler;
import com.kuaikan.comic.avg.bean.AvgChapterOptionConfig;
import com.kuaikan.comic.avg.model.dto.TopicRoleDTO;
import com.kuaikan.comic.avg.service.TopicRoleService;
import com.kuaikan.comic.bean.Topic;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.service.TopicService;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.model.param.AvgClosetActionSaveParam;
import com.kuaikan.role.game.admin.model.param.AvgClosetDressSaveParam;
import com.kuaikan.role.game.admin.model.param.AvgClosetIconParam;
import com.kuaikan.role.game.admin.model.param.AvgClosetSaveParam;
import com.kuaikan.role.game.admin.model.param.AvgClosetTabSaveParam;
import com.kuaikan.role.game.admin.model.param.AvgFileQueryParam;
import com.kuaikan.role.game.admin.model.view.AvgClosetActionListView;
import com.kuaikan.role.game.admin.model.view.AvgClosetArtView;
import com.kuaikan.role.game.admin.model.view.AvgClosetDetailView;
import com.kuaikan.role.game.admin.model.view.AvgClosetDressDetailView;
import com.kuaikan.role.game.admin.model.view.AvgClosetListView;
import com.kuaikan.role.game.admin.model.view.AvgClosetSaveView;
import com.kuaikan.role.game.admin.model.view.AvgClosetTabView;
import com.kuaikan.role.game.admin.model.view.AvgTopicClosetView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.remote.AvgChapterClient;
import com.kuaikan.role.game.admin.repository.AvgClosetActionRepository;
import com.kuaikan.role.game.admin.repository.AvgClosetArtImageRepository;
import com.kuaikan.role.game.admin.repository.AvgClosetDressRepository;
import com.kuaikan.role.game.admin.repository.AvgClosetRepository;
import com.kuaikan.role.game.admin.repository.AvgClosetTabRepository;
import com.kuaikan.role.game.admin.repository.AvgRepository;
import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.AvgCloset;
import com.kuaikan.role.game.api.bean.AvgClosetAction;
import com.kuaikan.role.game.api.bean.AvgClosetArtImage;
import com.kuaikan.role.game.api.bean.AvgClosetDress;
import com.kuaikan.role.game.api.bean.AvgClosetTab;
import com.kuaikan.role.game.api.enums.AvgClosetDressAcceptType;
import com.kuaikan.role.game.api.enums.AvgFileStatusType;
import com.kuaikan.role.game.api.enums.CdnPayType;
import com.kuaikan.role.game.api.rpc.result.AvgChapterModelV2;

/**
 * <AUTHOR>
 * @date 2025/6/3
 */
@Service
@Slf4j
public class AvgClosetBiz {

    @Resource
    private AvgRepository avgRepository;
    @Resource
    private AvgClosetRepository avgClosetRepository;
    @Resource
    private AvgClosetDressRepository avgClosetDressRepository;
    @Resource
    private AvgClosetTabRepository avgClosetTabRepository;
    @Resource
    private AvgClosetActionRepository avgClosetActionRepository;
    @Resource
    private TopicService topicService;
    @Resource
    private TopicRoleService topicRoleService;
    @Resource
    private QiniuComponent qiniuComponent;
    @Resource
    private AvgClosetArtImageRepository avgClosetArtImageRepository;
    @Resource
    private AvgChapterClient avgChapterClient;

    private static final String AVG_FILE_PATH = "avg/";

    private static final String MASK_KODO_PATH = "kodo://%s/%s";

    private static final String ADD_WATERMARK_PARAM = "watermark/1/image/%s/dissolve/100/gravity/NorthWest/dx/%s/dy/%s/ws/%s/wst/0";

    private static final String ADD_CROP_PARAM = "imageMogr2/crop/!%sx%sa%sa%s";

    public BizResult<PageResult<AvgClosetListView>> avgClosetList(AvgFileQueryParam param) {
        int page = param.getPage();
        int pageSize = param.getPageSize();
        String orderByAndSort = param.getOrderByAndSort();
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();

        PageInfo<AvgCloset> avgClosets = avgClosetRepository.queryAvgCloset(page, pageSize, orderByAndSort, param.getName(), param.getTopicId(),
                param.getStatus());
        if (CollectionUtils.isEmpty(avgClosets.getList())) {
            return BizResult.success(PageResult.emptyPageResult());
        }
        Set<Integer> topicIds = avgClosets.getList().stream().map(AvgCloset::getRelatedTopicId).collect(Collectors.toSet());
        List<Topic> topicResult = topicService.getTopicsByIds(topicIds);
        if (CollectionUtils.isEmpty(topicResult)) {
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "获取专题信息失败");
        }
        Map<Integer, Topic> topicMap = topicResult.stream().collect(Collectors.toMap(Topic::getId, Function.identity()));
        Set<Integer> topicRoleIds = avgClosets.getList().stream().map(AvgCloset::getRelatedRoleId).collect(Collectors.toSet());
        RpcResult<Map<Integer, TopicRoleDTO>> roleMapResult = topicRoleService.getRoleByIds(topicRoleIds);
        if (roleMapResult == null || !roleMapResult.isSuccess()) {
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "获取角色信息失败");
        }
        Set<Integer> closetIds = avgClosets.getList().stream().map(AvgCloset::getId).collect(Collectors.toSet());
        Map<Integer, List<AvgClosetDress>> closetId2Dress = avgClosetDressRepository.getDressByClosetIds(closetIds);
        Set<Integer> defaultDressIds = closetId2Dress.values()
                .stream()
                .flatMap(List::stream)
                .filter(AvgClosetDress::isDefaultDress)
                .map(AvgClosetDress::getId)
                .collect(Collectors.toSet());
        Map<Integer, AvgClosetArtImage> dressId2Art = avgClosetArtImageRepository.getDefaultDressArtsByIds(defaultDressIds)
                .stream()
                .collect(Collectors.toMap(AvgClosetArtImage::getDressId, Function.identity()));

        return BizResult.success(PageResult.from(avgClosets,
                (closet) -> AvgClosetListView.valueOf(closet, topicMap, roleMapResult.getData(), closetId2Dress, dressId2Art, domain)));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> saveCloset(AvgClosetSaveParam param) {
        if (!param.isValid()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "必填参数请求填写完整");
        }
        BizResult<Void> checkRes = checkClosetSaveParam(param);
        if (!checkRes.isSuccess()) {
            return checkRes;
        }
        AvgCloset closet;
        if (param.getId() > 0) {
            closet = avgClosetRepository.getAvgClosetById(param.getId());
            if (closet == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "衣柜不存在");
            }
            closet.setName(param.getRoleName());
            closet.setRelatedRoleId(param.getRelatedRoleId());
            closet.setRelatedTopicId(param.getRelatedTopicId());
            closet.setMaterialType(param.getMaterialType());
            if (CollectionUtils.isNotEmpty(param.getExpressionConf())) {
                List<AvgCloset.ExpressionConf> expresses = param.getExpressionConf()
                        .stream().map(conf -> new AvgCloset.ExpressionConf().setDefaultExpress(conf.isDefaultExpress())
                                .setFileKey(conf.getFileKey())
                                .setName(conf.getName())
                                .setWidth(conf.getWidth())
                                .setHeight(conf.getHeight()))
                        .sorted(Comparator.comparing(AvgCloset.ExpressionConf::isDefaultExpress, Comparator.reverseOrder())
                                .thenComparing(AvgCloset.ExpressionConf::getName))
                        .collect(Collectors.toList());
                closet.setExpressionConf(expresses);
            }
            closet.setUpdatedAt(new Date());
            avgClosetRepository.updateByPrimaryKeySelective(closet);
        } else {
            closet = new AvgCloset();
            closet.setName(param.getRoleName());
            closet.setRelatedRoleId(param.getRelatedRoleId());
            closet.setRelatedTopicId(param.getRelatedTopicId());
            closet.setMaterialType(param.getMaterialType());
            closet.setStatus(AvgFileStatusType.ONLINE.getCode());
            if (CollectionUtils.isNotEmpty(param.getExpressionConf())) {
                List<AvgCloset.ExpressionConf> expresses = param.getExpressionConf()
                        .stream().map(conf -> new AvgCloset.ExpressionConf()
                                .setDefaultExpress(conf.isDefaultExpress())
                                .setFileKey(conf.getFileKey())
                                .setName(conf.getName())
                                .setWidth(conf.getWidth())
                                .setHeight(conf.getHeight()))
                        .sorted(Comparator.comparing(AvgCloset.ExpressionConf::isDefaultExpress, Comparator.reverseOrder())
                                .thenComparing(AvgCloset.ExpressionConf::getName))
                        .collect(Collectors.toList());
                closet.setExpressionConf(expresses);
            }
            avgClosetRepository.insertSelective(closet);
            List<AvgClosetTab> avgClosetTabs = avgClosetTabRepository.getTabByIds(param.getTabIds());
            for (AvgClosetTab tab : avgClosetTabs) {
                tab.setClosetId(closet.getId());
                avgClosetTabRepository.updateByPrimaryKeySelective(tab);
            }
            List<AvgClosetDress> dressList = avgClosetDressRepository.getDressByTabIds(param.getTabIds());
            for (AvgClosetDress dress : dressList) {
                dress.setClosetId(closet.getId());
                avgClosetDressRepository.updateByPrimaryKeySelective(dress);
            }
            List<AvgClosetAction> actionList = avgClosetActionRepository.getActionByIds(param.getActionIds());
            for (AvgClosetAction action : actionList) {
                action.setClosetId(closet.getId());
                avgClosetActionRepository.updateByPrimaryKeySelective(action);
            }
        }
        syncClosetExpressArt(closet.getId());
        return BizResult.success();
    }

    public BizResult<Integer> saveTab(AvgClosetTabSaveParam param) {
        if (!param.isValid()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "必填参数请求填写完整");
        }
        AvgClosetTab closetTab;
        List<AvgClosetTab> closetTabs = avgClosetTabRepository.getClosetTabs(param.getClosetId());
        if (param.getId() > 0) {
            closetTab = closetTabs.stream().filter(ele -> ele.getId() == param.getId()).findFirst().orElse(null);
            if (closetTab == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "tab不存在");
            }
            closetTab.setName(param.getTabName());
            closetTab.setOrder(param.getOrder());
            avgClosetTabRepository.updateByPrimaryKeySelective(closetTab);
        } else {
            closetTab = new AvgClosetTab();
            closetTab.setClosetId(param.getClosetId());
            closetTab.setName(param.getTabName());
            closetTab.setOrder(param.getOrder());
            avgClosetTabRepository.insertSelective(closetTab);
        }
        return BizResult.success(closetTab.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> delTab(int id) {
        AvgClosetTab closetTab = avgClosetTabRepository.getTabById(id);
        if (closetTab == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "tab不存在");
        }
        avgClosetTabRepository.deleteByPrimaryKey(id);
        List<AvgClosetDress> relatedDresses = avgClosetDressRepository.getDressByTabId(id);
        if (CollectionUtils.isNotEmpty(relatedDresses)) {
            Set<Integer> relatedDressIds = relatedDresses.stream().map(AvgClosetDress::getId).collect(Collectors.toSet());
            avgClosetArtImageRepository.deleteByDressIds(relatedDressIds);
            avgClosetDressRepository.deleteByPrimaryKeys(relatedDressIds);
        }
        return BizResult.success();
    }

    public BizResult<Integer> saveAction(AvgClosetActionSaveParam param) {
        if (!param.isValid()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "必填参数请求填写完整");
        }
        List<AvgClosetAction> closetActions = avgClosetActionRepository.getClosetActions(param.getClosetId());
        AvgClosetAction closetAction;
        if (param.getId() > 0) {
            closetAction = closetActions.stream().filter(ele -> ele.getId() == param.getId()).findFirst().orElse(null);
            if (closetAction == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "动作不存在");
            }
            AvgClosetAction existNameAction = closetActions.stream()
                    .filter(ele -> ele.getId() != param.getId() && ele.getName().equals(param.getActionName()))
                    .findFirst().orElse(null);
            if (existNameAction != null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "动作名称 "+param.getActionName()+" 已存在");
            }
            closetAction.setName(param.getActionName());
            closetAction.setOrder(param.getOrder());
            avgClosetActionRepository.updateByPrimaryKeySelective(closetAction);
        } else {
            AvgClosetAction existNameAction = closetActions.stream()
                    .filter(ele -> ele.getName().equals(param.getActionName()))
                    .findFirst().orElse(null);
            if (existNameAction != null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "动作名称 "+param.getActionName()+" 已存在");
            }
            closetAction = new AvgClosetAction();
            closetAction.setClosetId(param.getClosetId());
            closetAction.setName(param.getActionName());
            closetAction.setOrder(param.getOrder());
            avgClosetActionRepository.insertSelective(closetAction);
        }
        return BizResult.success(closetAction.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> delAction(int id) {
        AvgClosetAction closetAction = avgClosetActionRepository.getActionById(id);
        if (closetAction == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "动作不存在");
        }
        avgClosetActionRepository.deleteByPrimaryKey(id);
        avgClosetArtImageRepository.deleteByActionId(id);
        return BizResult.success();
    }

    public BizResult<List<AvgClosetActionListView>> avgClosetActionList(int closetId, String dressName) {
        if (closetId <= 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "衣橱ID不能为空");
        }
        List<AvgClosetAction> closetActions = avgClosetActionRepository.getClosetActions(closetId);
        List<AvgClosetDress> closetDress = avgClosetDressRepository.getDressByClosetIdAndName(closetId, dressName);
        if (CollectionUtils.isEmpty(closetActions) || CollectionUtils.isEmpty(closetDress)) {
            log.info("closet has no actions or dress, closetId: {} dressName:{}", closetId, dressName);
            return BizResult.success(Lists.newArrayList());
        }
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        Map<Integer, List<AvgClosetArtImage>> actionArtMap = avgClosetArtImageRepository.getByClosetId(closetId, "")
                .stream()
                .collect(Collectors.groupingBy(AvgClosetArtImage::getActionId));
        List<AvgClosetActionListView> resultViews = Lists.newArrayListWithExpectedSize(closetActions.size());
        for (AvgClosetAction action : closetActions) {
            List<AvgClosetArtImage> actionArts = actionArtMap.getOrDefault(action.getId(), Lists.newArrayList());
            AvgClosetActionListView listView = new AvgClosetActionListView();
            listView.setActionId(action.getId());
            listView.setName(action.getName());
            listView.setOrder(action.getOrder());
            Map<Integer, AvgClosetArtImage> dressId2Art = actionArts.stream().collect(Collectors.toMap(AvgClosetArtImage::getDressId, Function.identity()));
            List<AvgClosetActionListView.AvgDressArtView> dressArtList = Lists.newArrayListWithExpectedSize(actionArts.size());
            for (AvgClosetDress dress : closetDress) {
                AvgClosetArtImage art = dressId2Art.get(dress.getId());
                AvgClosetActionListView.AvgDressArtView artView = new AvgClosetActionListView.AvgDressArtView().setDressId(dress.getId());
                if (dress.isDefaultDress()) {
                    artView.setDressTitle("初始装扮");
                } else {
                    artView.setDressTitle(dress.getTitle()).setDressName(dress.getName());
                }
                if (art != null) {
                    artView.setDressArt(new AvgClosetArtView().setArtKey(art.getArtImage()).setArtUrl(
                            CdnHandler.getEncryptionUrl(domain + art.getArtImage(), CdnPayType.PAY.getCode())));
                }
                dressArtList.add(artView);
            }
            listView.setDressArtList(dressArtList);
            resultViews.add(listView);
        }
        return BizResult.success(resultViews);
    }

    public BizResult<AvgClosetDetailView> closetDetail(int id) {
        AvgCloset avgCloset = avgClosetRepository.getAvgClosetById(id);
        if (avgCloset == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "衣橱不存在");
        }
        List<AvgClosetTab> closetTabs = avgClosetTabRepository.getClosetTabs(id);
        if (CollectionUtils.isEmpty(closetTabs)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "衣橱不存在tab");
        }
        Map<Integer, List<AvgClosetDress>> tabId2DressList = avgClosetDressRepository.getDressByClosetIdAndName(id, null)
                .stream()
                .collect(Collectors.groupingBy(AvgClosetDress::getTabId));
        if (MapUtils.isEmpty(tabId2DressList)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "衣橱不存在装扮");
        }
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        AvgClosetDetailView detailView = new AvgClosetDetailView().setId(avgCloset.getId())
                .setRoleName(avgCloset.getName())
                .setRelatedRoleId(avgCloset.getRelatedRoleId())
                .setRelatedTopicId(avgCloset.getRelatedTopicId())
                .setMaterialType(avgCloset.getMaterialType());
        List<Integer> tabIds = avgClosetTabRepository.getClosetTabs(id).stream().map(AvgClosetTab::getId).collect(Collectors.toList());
        detailView.setTabIds(tabIds);
        List<AvgCloset.ExpressionConf> expressionConf = avgCloset.getExpressionConf();
        if (CollectionUtils.isNotEmpty(expressionConf)) {
            detailView.setExpressionConf(expressionConf.stream()
                    .map(conf -> new AvgClosetDetailView.ExpressionView().setName(conf.getName())
                            .setFileKey(conf.getFileKey())
                            .setFileUrl(CdnHandler.getEncryptionUrl(domain + conf.getFileKey(), CdnPayType.PAY.getCode()))
                            .setDefaultExpress(conf.isDefaultExpress())
                            .setWidth(conf.getWidth())
                            .setHeight(conf.getHeight()))
                    .collect(Collectors.toList()));
        }
        return BizResult.success(detailView);
    }

    public BizResult<List<AvgClosetTabView>> closetDressList(List<Integer> tabIds) {
        List<AvgClosetTab> closetTabs = avgClosetTabRepository.getTabByIds(tabIds);
        if (CollectionUtils.isEmpty(closetTabs)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), String.format("tabIds:%s 不存在", tabIds));
        }
        Map<Integer, List<AvgClosetDress>> tabId2DressList = avgClosetDressRepository.getDressByTabIds(tabIds)
                .stream()
                .collect(Collectors.groupingBy(AvgClosetDress::getTabId));
        List<AvgClosetTabView> tabList = Lists.newArrayListWithExpectedSize(closetTabs.size());
        Set<Integer> dressIds = tabId2DressList.values().stream().flatMap(List::stream).map(AvgClosetDress::getId).collect(Collectors.toSet());
        Map<Integer, AvgClosetArtImage> dressId2Art = avgClosetArtImageRepository.getDefaultDressArtsByIds(dressIds)
                .stream()
                .collect(Collectors.toMap(AvgClosetArtImage::getDressId, Function.identity()));
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        for (AvgClosetTab tab : closetTabs) {
            List<AvgClosetDress> dressList = tabId2DressList.getOrDefault(tab.getId(), Lists.newArrayList());
            List<AvgClosetTabView.DressView> dressViews = Lists.newArrayListWithExpectedSize(dressList.size());
            for (AvgClosetDress dress : dressList) {
                AvgClosetTabView.DressView dressView = new AvgClosetTabView.DressView();
                dressView.setId(dress.getId())
                        .setDefaultDress(dress.isDefaultDress())
                        .setOrder(dress.getOrder())
                        .setName(dress.getName())
                        .setTitle(dress.getTitle())
                        .setClosetShow(dress.isClosetShow())
                        .setSupportWear(dress.isSupportWear())
                        .setPrice(buildDressPrice(dress))
                        .setCondition(buildDressCondition(dress));
                if (dressId2Art.containsKey(dress.getId())) {
                    AvgClosetArtImage artImage = dressId2Art.get(dress.getId());
                    dressView.setDefaultArt(new AvgClosetArtView().setArtKey(artImage.getArtImage())
                            .setArtUrl(CdnHandler.getEncryptionUrl(domain + artImage.getArtImage(), CdnPayType.PAY.getCode())));
                }
                if (dress.getExpressionIcon() != null) {
                    dressView.setExpress(AvgClosetTabView.AxisView.valueOf(dress.getExpressionIcon()));
                }
                if (dress.getAvatarIcon() != null) {
                    dressView.setAvatar(AvgClosetTabView.AxisView.valueOf(dress.getAvatarIcon()));
                }
                dressViews.add(dressView);
            }
            AvgClosetTabView tabView = new AvgClosetTabView().setDressList(dressViews).setId(tab.getId())
                    .setName(tab.getName()).setOrder(tab.getOrder());
            tabList.add(tabView);
        }
        return BizResult.success(tabList);
    }

    public BizResult<AvgClosetDressDetailView> closetDressDetail(int id) {
        AvgClosetDress dress = avgClosetDressRepository.getById(id);
        if (dress == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        AvgClosetArtImage artImage = avgClosetArtImageRepository.getDressActionExpressArt(id, 0, "");
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        AvgClosetDressDetailView detailView = AvgClosetDressDetailView.valueOf(dress, artImage, domain);
        return BizResult.success(detailView);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<AvgClosetSaveView> saveClosetDress(AvgClosetDressSaveParam saveParam) {
        BizResult<AvgClosetSaveView> checkRes = checkDressSaveParam(saveParam);
        if (!checkRes.isSuccess()) {
            return checkRes;
        }
        if (saveParam.isDefaultDress() && saveParam.getId() <= 0) {
            if (saveParam.getClosetId() > 0) {
                // 如果初始装扮没创建，这时应该没有默认装扮tab
                List<AvgClosetTab> closetTabs = avgClosetTabRepository.getClosetTabs(saveParam.getClosetId());
                if (CollectionUtils.isNotEmpty(closetTabs)) {
                    log.warn("default dress not exist, but tab already exist, closetId: {}, tabId: {}",
                            saveParam.getClosetId(), closetTabs.get(0).getId());
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "默认装扮tab已存在");
                }
            }
            AvgClosetTab defaultTab = new AvgClosetTab().setName("默认tab").setClosetId(saveParam.getClosetId())
                    .setOrder(0);
            avgClosetTabRepository.insertSelective(defaultTab);
            saveParam.setTabId(defaultTab.getId());
        }
        AvgClosetDress dress;
        if (saveParam.getId() > 0) {
            dress = avgClosetDressRepository.getById(saveParam.getId());
            dress = AvgClosetDressSaveParam.toBean(saveParam, dress);
            dress.setUpdatedAt(new Date());
            avgClosetDressRepository.updateByPrimaryKeySelective(dress);
        } else {
            dress = AvgClosetDressSaveParam.toBean(saveParam, null);
            avgClosetDressRepository.insertSelective(dress);
        }
        if (saveParam.getDefaultArt() != null) {
            AvgClosetArtImage artImage = avgClosetArtImageRepository.getDressActionExpressArt(dress.getId(), 0 , "");
            if (artImage == null) {
                artImage = new AvgClosetArtImage().setClosetId(dress.getClosetId())
                        .setDressId(dress.getId())
                        .setArtImage(saveParam.getDefaultArt().getArtKey());
                avgClosetArtImageRepository.insertSelective(artImage);
            } else {
                artImage.setArtImage(saveParam.getDefaultArt().getArtKey());
                avgClosetArtImageRepository.updateByPrimaryKeySelective(artImage);
            }
        }
        AvgClosetSaveView saveView = new AvgClosetSaveView().setTabId(saveParam.getTabId());
        return BizResult.success(saveView);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> deleteClosetDress(int id) {
        AvgClosetDress dress = avgClosetDressRepository.getById(id);
        if (dress == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        avgClosetDressRepository.deleteByPrimaryKey(id);
        avgClosetArtImageRepository.deleteByDressIds(Sets.newHashSet(id));
        return BizResult.success();
    }

    public BizResult<Void> dressExpress(AvgClosetIconParam param) {
        if (!param.isValid()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "必填参数请求填写完整");
        }
        AvgClosetDress dress = avgClosetDressRepository.getById(param.getId());
        if (dress == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        AvgClosetDress.DressIcon icon =  dress.getExpressionIcon() != null ? dress.getExpressionIcon() : new AvgClosetDress.DressIcon();
        icon.setLeftUpAxis(new AvgClosetDress.PointAxis().setX(param.getLeftUpAxis().getX()).setY(param.getLeftUpAxis().getY()))
                .setRightDownAxis(new AvgClosetDress.PointAxis().setX(param.getRightDownAxis().getX()).setY(param.getRightDownAxis().getY()));
        dress.setExpressionIcon(icon);
        dress.setUpdatedAt(new Date());
        avgClosetDressRepository.updateByPrimaryKeySelective(dress);
        return BizResult.success();
    }

    public BizResult<Void> dressAvatar(AvgClosetIconParam param) {
        if (!param.isValid()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "必填参数请求填写完整");
        }
        AvgClosetDress dress = avgClosetDressRepository.getById(param.getId());
        if (dress == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        AvgClosetDress.DressIcon icon =  dress.getAvatarIcon() != null ? dress.getAvatarIcon() : new AvgClosetDress.DressIcon();
        icon.setLeftUpAxis(new AvgClosetDress.PointAxis().setX(param.getLeftUpAxis().getX()).setY(param.getLeftUpAxis().getY()))
                .setRightDownAxis(new AvgClosetDress.PointAxis().setX(param.getRightDownAxis().getX()).setY(param.getRightDownAxis().getY()));
        dress.setAvatarIcon(icon);
        dress.setUpdatedAt(new Date());
        avgClosetDressRepository.updateByPrimaryKeySelective(dress);
        return BizResult.success();
    }

    public BizResult<Void> dressOrder(int id, int order) {
        AvgClosetDress dress = avgClosetDressRepository.getById(id);
        if (dress == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        dress.setOrder(order);
        avgClosetDressRepository.updateByPrimaryKeySelective(dress);
        return BizResult.success();
    }

    public BizResult<Void> dressShow(int id, boolean show) {
        AvgClosetDress dress = avgClosetDressRepository.getById(id);
        if (dress == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        dress.setClosetShow(show);
        avgClosetDressRepository.updateByPrimaryKeySelective(dress);
        return BizResult.success();
    }

    public BizResult<Void> dressArtChange(int id, String image) {
        AvgClosetDress dress = avgClosetDressRepository.getById(id);
        if (dress == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        AvgClosetArtImage artImage = avgClosetArtImageRepository.getDressActionExpressArt(id, 0, "");
        if (artImage != null) {
            if (StringUtils.isBlank(image)) {
                avgClosetArtImageRepository.deleteById(artImage.getId());
            } else {
                artImage.setArtImage(image);
                avgClosetArtImageRepository.updateByPrimaryKeySelective(artImage);
            }
        } else if (StringUtils.isNotBlank(image)) {
            artImage = new AvgClosetArtImage().setClosetId(dress.getClosetId())
                    .setDressId(dress.getId())
                    .setArtImage(image);
            avgClosetArtImageRepository.insertSelective(artImage);
        }
        return BizResult.success();
    }

    public BizResult<Void> dressActionArt(int dressId, int actionId, String artImage) {
        AvgClosetDress dress = avgClosetDressRepository.getById(dressId);
        if (dress == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        AvgClosetAction avgClosetAction = avgClosetActionRepository.getActionById(actionId);
        if (avgClosetAction == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "动作不存在");
        }
        AvgClosetArtImage actionArt = avgClosetArtImageRepository.getDressActionExpressArt(dressId, actionId, "");
        if (actionArt != null) {
            if (StringUtils.isBlank(artImage)) {
                avgClosetArtImageRepository.deleteById(actionArt.getId());
            } else {
                actionArt.setArtImage(artImage);
                avgClosetArtImageRepository.updateByPrimaryKeySelective(actionArt);
            }
        } else if (StringUtils.isNotBlank(artImage)) {
            actionArt = new AvgClosetArtImage().setArtImage(artImage)
                    .setActionId(actionId)
                    .setDressId(dressId).setClosetId(dress.getClosetId());
            avgClosetArtImageRepository.insertSelective(actionArt);
        }
        return BizResult.success();
    }

    public BizResult<List<AvgTopicClosetView>> getTopicCloset(int chapterId, int topicId, String searchName) {
        AvgChapter chapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (chapter == null || CollectionUtils.isEmpty(chapter.getTextList())) {
            log.warn("getTopicCloset chapter not exist or text list empty, chapterId: {}", chapterId);
            return BizResult.success(Lists.newArrayList());
        }
        List<AvgChapter.Text> textList = chapter.getTextList();
        Set<String> chapterRelatedRoleNames = Sets.newHashSet();
        for (AvgChapter.Text text : textList) {
            String dialogue = text.getDialogue();
            if (StringUtils.isNotBlank(dialogue) && dialogue.contains("##")) {
                Pair<String, String> dialogPair = AvgChapterModelV2.parseDialog(dialogue);
                String roleName = dialogPair.getLeft();
                if (StringUtils.isNotBlank(searchName) && !roleName.contains(searchName)) {
                    continue;
                }
                if (!roleName.equals(PLAYER_REPLACE)) {
                    chapterRelatedRoleNames.add(roleName);
                }
            }
        }
        if (CollectionUtils.isEmpty(chapterRelatedRoleNames)) {
            return BizResult.success(Lists.newArrayList());
        }
        List<TopicRoleInfo> topicRoles = avgChapterClient.getRoleByNames(chapterRelatedRoleNames, topicId);
        if (CollectionUtils.isEmpty(topicRoles)) {
            log.warn("getTopicCloset topicRoleService.getByRoleName failed, chapterRelatedRoleNames:{}, topicId:{}, result: {}", chapterRelatedRoleNames,
                    topicId, topicRoles);
            return BizResult.success(Lists.newArrayList());
        }
        Set<Integer> roleIds = topicRoles.stream().map(TopicRoleInfo::getId).collect(Collectors.toSet());
        Map<Integer, TopicRoleInfo> topicRoleMap = topicRoles.stream().collect(Collectors.toMap(TopicRoleInfo::getId, Function.identity()));
        List<AvgCloset> roleClosets = avgClosetRepository.getAvgClosetByRoleIds(roleIds);
        Set<Integer> roleClosetTopicIds = roleClosets.stream().map(AvgCloset::getRelatedTopicId).collect(Collectors.toSet());
        Set<Integer> roleClosetIds = roleClosets.stream().map(AvgCloset::getId).collect(Collectors.toSet());
        Map<Integer, Topic> topicMap = topicService.getTopicsByIds(roleClosetTopicIds).stream().collect(Collectors.toMap(Topic::getId, Function.identity()));
        Map<Integer, List<AvgClosetAction>> closetActionMap = avgClosetActionRepository.getByClosetIds(roleClosetIds)
                .stream()
                .collect(Collectors.groupingBy(AvgClosetAction::getClosetId));
        String domain = CdnUtil.getDefaultNewDomainWithBackSlash();
        List<AvgTopicClosetView> closetViews = roleClosets.stream().map(closet -> {
            int closetRoleId = closet.getRelatedRoleId();
            int closetTopicId = closet.getRelatedTopicId();
            TopicRoleInfo relatedRole = topicRoleMap.get(closetRoleId);
            Topic relatedTopic = topicMap.get(closetTopicId);
            if (relatedRole == null || relatedTopic == null) {
                return null;
            }
            List<AvgClosetAction> closetActions = closetActionMap.getOrDefault(closet.getId(), Lists.newArrayList());
            List<AvgCloset.ExpressionConf> expressions = Optional.ofNullable(closet.getExpressionConf()).orElse(Lists.newArrayList());
            AvgTopicClosetView closetView = new AvgTopicClosetView();
            closetView.setTopicId(relatedTopic.getId());
            closetView.setShowRoleName(String.format("%s - %s", relatedRole.getName(), relatedTopic.getTitle()));
            closetView.setRoleId(relatedRole.getId());
            closetView.setRoleName(relatedRole.getName());
            List<AvgTopicClosetView.AvgTopicClosetActionView> actions = closetActions.stream().map(action -> {
                return new AvgTopicClosetView.AvgTopicClosetActionView().setId(action.getId()).setName(action.getName());
            }).collect(Collectors.toList());
            List<AvgTopicClosetView.AvgTopicClosetExpressView> expresses = expressions.stream().map(express -> {
                return new AvgTopicClosetView.AvgTopicClosetExpressView().setDefaultExpress(express.isDefaultExpress())
                        .setName(express.getName())
                        .setKey(express.getFileKey())
                        .setUrl(CdnHandler.getEncryptionUrl(domain + express.getFileKey(), CdnPayType.PAY.getCode()));
            }).collect(Collectors.toList());
            closetView.setActions(actions).setExpresses(expresses);
            return closetView;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return BizResult.success(closetViews);
    }


    private String buildDressPrice(AvgClosetDress dress) {
        if (CollectionUtils.isNotEmpty(dress.getAcceptTypes()) && dress.getAcceptTypes().contains(AvgClosetDressAcceptType.CLOSET.getCode())) {
            return String.format("原价%sKK币 \n 现价%sKK币", dress.getOriginalPrice(), dress.getFinalPrice());
        }
        return null;
    }

    private String buildDressCondition(AvgClosetDress dress) {
        List<AvgChapterOptionConfig.Condition> conditions = dress.getAcceptCondition();
        if (CollectionUtils.isEmpty(conditions)) {
            return null;
        }
        return String.format("已解锁条件 %s 个", conditions.size());
    }

    private BizResult<Void> checkClosetSaveParam(AvgClosetSaveParam param) {
        List<AvgClosetDress> dressList;
        AvgCloset existCloset = avgClosetRepository.getAvgClosetByName(param.getRoleName());
        if (existCloset != null && (param.getId() <= 0 || existCloset.getId() != param.getId())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色名称已存在");
        }
        AvgCloset existRoleCloset = avgClosetRepository.getAvgClosetByRoleId(param.getRelatedRoleId());
        if (existRoleCloset != null && (param.getId() <= 0 || existRoleCloset.getId() != param.getId())
                && existRoleCloset.getRelatedTopicId() == param.getRelatedTopicId()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "已存在角色衣橱，关联此专题此角色");
        }
        if (param.getId() <= 0) {
            if (CollectionUtils.isEmpty(param.getTabIds())) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "需要新建tab才能创建衣橱");
            }
            dressList = avgClosetDressRepository.getDressByTabIds(param.getTabIds());
        } else {
            List<AvgClosetTab> closetTabs = avgClosetTabRepository.getClosetTabs(param.getId());
            if (CollectionUtils.isEmpty(closetTabs)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "需要新建tab才能创建衣橱");
            }
            dressList = avgClosetDressRepository.getDressByClosetIdAndName(param.getId(), null);
        }
        if (CollectionUtils.isEmpty(dressList)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "衣柜没有装扮，请先创建装扮");
        }
        long supportWearDressCnt = dressList.stream().filter(AvgClosetDress::isSupportWear).count();
        List<AvgClosetSaveParam.ExpressionConfParam> expressionConf = param.getExpressionConf();
        long defaultExpressCount = expressionConf.stream().filter(AvgClosetSaveParam.ExpressionConfParam::isDefaultExpress).count();
        if (supportWearDressCnt > 0 && (CollectionUtils.isEmpty(expressionConf) || defaultExpressCount == 0)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "请至少添加1个默认表情");
        }
        if (defaultExpressCount > 1) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "只能添加一个默认表情");
        }
        if (CollectionUtils.isNotEmpty(param.getExpressionConf())) {
            long expressCount = param.getExpressionConf().stream().map(AvgClosetSaveParam.ExpressionConfParam::getName).distinct().count();
            if (expressCount < param.getExpressionConf().size()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "表情名不能重复");
            }
        }
        return BizResult.success();
    }

    private BizResult<AvgClosetSaveView> checkDressSaveParam(AvgClosetDressSaveParam param) {
        if (param.getOriginalPrice() < param.getFinalPrice()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "现价不可大于原价");
        }
        Map<String, Object> noWearAction = JsonUtils.findObject(param.getNoWearAction(), Map.class);
        if ((StringUtils.isNotBlank(param.getNoWearText()) && MapUtils.isEmpty(noWearAction)) ||
                (StringUtils.isBlank(param.getNoWearText()) && MapUtils.isNotEmpty(noWearAction))) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "跳转按钮和按钮跳转目标需一起配置");
        }
        List<AvgClosetDress> existClosetDress = Lists.newArrayList();
        if (param.getClosetId() > 0) {
            existClosetDress = avgClosetDressRepository.getDressByClosetId(param.getClosetId());
        }
        if (param.getId() > 0) {
            AvgClosetDress dress = avgClosetDressRepository.getById(param.getId());
            if (dress == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
            }
            if (param.isDefaultDress()) {
                if (!dress.isDefaultDress()) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "修改的不是默认装扮");
                }
                existClosetDress = existClosetDress.stream().filter(ele -> ele.isDefaultDress() && ele.getId() != dress.getId()).collect(Collectors.toList());
                if (existClosetDress.size() > 0) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "衣橱下只能有一个默认装扮");
                }
            } else {
                existClosetDress = existClosetDress.stream()
                        .filter(ele -> ele.getName().equals(param.getName()) && ele.getId() != param.getId())
                        .collect(Collectors.toList());
                if (existClosetDress.size() > 0) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮名称已存在");
                }
            }
        } else {
            if (param.isDefaultDress()) {
                AvgClosetDress defaultDress = existClosetDress.stream().filter(AvgClosetDress::isDefaultDress).findFirst().orElse(null);
                if (defaultDress != null) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "衣橱下只能有一个默认装扮");
                }
            } else {
                existClosetDress = existClosetDress.stream().filter(ele -> ele.getName().equals(param.getName())).collect(Collectors.toList());
                if (existClosetDress.size() > 0) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮名称已存在");
                }
            }
        }
        return BizResult.success();
    }

    private void syncClosetExpressArt(int closetId) {
        BIZ_EXECUTOR.submit(() -> {
            AvgCloset avgCloset = avgClosetRepository.getAvgClosetById(closetId);
            if (avgCloset == null) {
                log.warn("syncDressImage avgCloset not exist, closetId: {}", closetId);
                return;
            }
            if (CollectionUtils.isEmpty(avgCloset.getExpressionConf())) {
                return;
            }
            List<AvgClosetDress> dressList = avgClosetDressRepository.getDressByClosetIdAndName(closetId, null);
            if (CollectionUtils.isEmpty(dressList)) {
                log.warn("syncDressImage dressList is empty, closetId: {}", closetId);
                return;
            }
            List<AvgClosetAction> actionList = avgClosetActionRepository.getClosetActions(avgCloset.getId());
            Map<Integer, List<AvgClosetArtImage>> dressId2ArtImages = avgClosetArtImageRepository.getByClosetId(closetId, null)
                    .stream()
                    .collect(Collectors.groupingBy(AvgClosetArtImage::getDressId));
            for (AvgClosetDress dress : dressList) {
                List<AvgClosetArtImage> dressArts = dressId2ArtImages.getOrDefault(dress.getId(), Lists.newArrayList());
                Map<Integer, List<AvgClosetArtImage>> actionId2ArtImages = dressArts.stream()
                        .collect(Collectors.groupingBy(AvgClosetArtImage::getActionId));
                Map<String, AvgClosetArtImage> defaultArtMap = actionId2ArtImages.getOrDefault(0, Lists.newArrayList())
                        .stream()
                        .collect(Collectors.toMap(AvgClosetArtImage::getExpressName, Function.identity()));
                generateDressActionArts(avgCloset, dress, 0, defaultArtMap);

                for (AvgClosetAction action : actionList) {
                    Map<String, AvgClosetArtImage> actionArtMap = actionId2ArtImages.getOrDefault(action.getId(), Lists.newArrayList())
                            .stream()
                            .collect(Collectors.toMap(AvgClosetArtImage::getExpressName, Function.identity()));
                    generateDressActionArts(avgCloset, dress, action.getId(), actionArtMap);
                }
            }
        });
    }

    private void generateDressActionArts(AvgCloset avgCloset, AvgClosetDress dress, int actionId, Map<String, AvgClosetArtImage> expressArtMap) {
        // 表情为空的立绘就是装扮默认立绘或者动作默认立绘
        AvgClosetArtImage actionArtImage = expressArtMap.get(StringUtils.EMPTY);
        Set<Integer> delIds = Sets.newHashSet();
        List<AvgClosetArtImage> modifyArts = Lists.newArrayList();
        List<AvgClosetArtImage> addArts = Lists.newArrayList();
        if (actionArtImage == null || StringUtils.isBlank(actionArtImage.getArtImage())) {
            // 如果默认立绘不存在，则删除所有相关立绘
            delIds.addAll(expressArtMap.values().stream().map(AvgClosetArtImage::getId).collect(Collectors.toSet()));
        } else {
            if (addDressArtCorp(actionArtImage, dress)) {
                modifyArts.add(actionArtImage);
            }
            String artImagePic = actionArtImage.getArtImage();
            for (AvgCloset.ExpressionConf conf : avgCloset.getExpressionConf()) {
                if (StringUtils.isBlank(conf.getName())) {
                    continue;
                }
                AvgClosetArtImage expressArt = expressArtMap.get(conf.getName());
                if (expressArt == null) {
                    String targetImage = addMask(artImagePic, conf.getFileKey(), conf.getWidth(), dress.getExpressionIcon());
                    AvgClosetArtImage newArt = new AvgClosetArtImage().setDressId(dress.getId())
                            .setClosetId(avgCloset.getId())
                            .setActionId(actionId)
                            .setExpressName(conf.getName())
                            .setArtImage(targetImage)
                            .setArtMd5(DigestUtils.md5Hex(artImagePic + conf.getFileKey()));
                    addDressArtCorp(newArt, dress);
                    addArts.add(newArt);
                    continue;
                }
                String mergeMd5 = DigestUtils.md5Hex(expressArt + conf.getFileKey() + dress.getExpressionIcon());
                if (!mergeMd5.equals(expressArt.getArtMd5())) {
                    String targetImage = addMask(artImagePic, conf.getFileKey(), conf.getWidth(), dress.getExpressionIcon());
                    expressArt.setExpressName(conf.getName())
                            .setArtImage(targetImage)
                            .setArtMd5(DigestUtils.md5Hex(artImagePic + conf.getFileKey() + dress.getExpressionIcon()));
                    addDressArtCorp(expressArt, dress);
                    modifyArts.add(expressArt);
                }
                expressArtMap.remove(conf.getName());
            }
            if (MapUtils.isNotEmpty(expressArtMap)) {
                // 删除未使用的表情
                delIds.addAll(expressArtMap.values()
                        .stream()
                        .filter(ele -> StringUtils.isNotBlank(ele.getExpressName()))
                        .map(AvgClosetArtImage::getId)
                        .collect(Collectors.toSet()));
            }
        }
        if (CollectionUtils.isNotEmpty(addArts)) {
            avgClosetArtImageRepository.batchInsert(addArts);
        }
        for (AvgClosetArtImage modifyArt : modifyArts) {
            avgClosetArtImageRepository.updateByPrimaryKeySelective(modifyArt);
        }
        if (CollectionUtils.isNotEmpty(delIds)) {
            avgClosetArtImageRepository.deleteByIds(delIds);
        }
    }

    private String addMask(String coverImage, String maskImage, int maskWidth, AvgClosetDress.DressIcon iconPosition) {
        if (iconPosition == null) {
            return StringUtils.EMPTY;
        }
        AvgClosetDress.PointAxis rightDownAxis = iconPosition.getRightDownAxis();
        AvgClosetDress.PointAxis leftUpAxis = iconPosition.getLeftUpAxis();
        if (leftUpAxis == null || rightDownAxis == null) {
            log.warn("Add mask failed, leftUpAxis or rightDownAxis is null. coverImage={}, maskImage={}, iconPosition={}", coverImage, maskImage, iconPosition);
            return StringUtils.EMPTY;
        }
        String targetImage = StringUtils.EMPTY;
        try {
            float scale = (float) maskWidth / (rightDownAxis.getX() - leftUpAxis.getX());
            long start = System.currentTimeMillis();
            String coverImageName = coverImage.substring(coverImage.lastIndexOf("/") + 1);
            targetImage = AVG_FILE_PATH + start + "/" + coverImageName;
            String encodedMaskImage = UrlSafeBase64.encodeToString(String.format(MASK_KODO_PATH, PAY_BUCKET_NAME, maskImage));
            String fops = String.format(ADD_WATERMARK_PARAM, encodedMaskImage, leftUpAxis.getX(), leftUpAxis.getY(), scale);
            qiniuComponent.syncPfop(PAY_BUCKET_NAME, coverImage, targetImage, fops);
            log.info("Add mask end. source={}, target={}, mask={}, cost={}", coverImage, targetImage, maskImage,
                    System.currentTimeMillis() - start);
        } catch (Exception ex) {
            log.warn("Add mask failed, coverImage={}, maskImage={}, iconPosition={}", coverImage, maskImage, iconPosition, ex);
        }
        return targetImage;
    }

    private boolean addDressArtCorp(AvgClosetArtImage dressArtImage, AvgClosetDress dress) {
        if (dress == null || dress.getAvatarIcon() == null) {
            dressArtImage.setAvatarImage(StringUtils.EMPTY);
            dressArtImage.setAvatarMd5(StringUtils.EMPTY);
            return false;
        }
        String art = dressArtImage.getArtImage();
        String newAvatarMd5 = DigestUtils.md5Hex(art + dress.getAvatarIcon());
        if (newAvatarMd5.equals(dressArtImage.getAvatarMd5())) {
            // 头像图片配置没有变化
            return false;
        }
        String targetImage = addCorp(dressArtImage.getArtImage(), dress);
        dressArtImage.setArtImage(targetImage);
        dressArtImage.setAvatarMd5(newAvatarMd5);
        return true;
    }

    private String addCorp(String coverImage, AvgClosetDress dress) {
        AvgClosetDress.PointAxis leftUpAxis = dress.getAvatarIcon().getLeftUpAxis();
        AvgClosetDress.PointAxis rightDownAxis = dress.getAvatarIcon().getRightDownAxis();
        if (leftUpAxis == null || rightDownAxis == null) {
            log.warn("addCorp failed, leftUpAxis or rightDownAxis is null. dressId: {}, coverImage: {}", dress.getId(), coverImage);
            return StringUtils.EMPTY;
        }
        String targetImage = StringUtils.EMPTY;
        try {
            int width = rightDownAxis.getX() - leftUpAxis.getX();
            int height = rightDownAxis.getY() - leftUpAxis.getY();
            long start = System.currentTimeMillis();
            String coverImageName = coverImage.substring(coverImage.lastIndexOf("/") + 1);
            targetImage = AVG_FILE_PATH + start + "/" + coverImageName;
            String fops = String.format(ADD_CROP_PARAM, width, height, leftUpAxis.getX(), leftUpAxis.getY());
            qiniuComponent.syncPfop(PAY_BUCKET_NAME, coverImage, targetImage, fops);
            log.info("Add corp end. source={}, target={}, cost={}", coverImage, targetImage, System.currentTimeMillis() - start);
        } catch (Exception ex) {
            log.warn("Add corp failed, coverImage={}, dress={}", coverImage, dress, ex);
        }
        return targetImage;
    }
}
