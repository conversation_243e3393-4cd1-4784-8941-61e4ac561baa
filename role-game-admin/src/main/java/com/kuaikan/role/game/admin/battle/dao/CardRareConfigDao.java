package com.kuaikan.role.game.admin.battle.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardRareConfig;

/**
 * 卡牌战斗-稀有度相关属性配置
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
public interface CardRareConfigDao {

    // 返回所有稀有度的配置
    List<CardBattleCardRareConfig> getAll();

    CardBattleCardRareConfig getByRareCode(@Param("rareCode") int rareCode);

    int insert(CardBattleCardRareConfig cardBattleCardRareConfig);

    void updateByRareCode(CardBattleCardRareConfig cardBattleCardRareConfig);
}
