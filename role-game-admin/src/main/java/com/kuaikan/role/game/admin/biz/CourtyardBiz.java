package com.kuaikan.role.game.admin.biz;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.model.param.CourtyardAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.CourtyardQueryParam;
import com.kuaikan.role.game.admin.model.param.CourtyardUpdateStatusParam;
import com.kuaikan.role.game.admin.model.view.CourtyardView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.repository.CourtyardConfigRepository;
import com.kuaikan.role.game.admin.repository.CourtyardRoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.utils.FunctionUtils;
import com.kuaikan.role.game.api.bean.CourtyardConfig;
import com.kuaikan.role.game.api.bean.CourtyardRoleGroupRelation;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;

/**
 * 庭院业务处理类
 * <AUTHOR>
 * @version 2025/6/19
 */
@Slf4j
@Service
public class CourtyardBiz {

    @Resource
    private CourtyardConfigRepository courtyardConfigRepository;
    @Resource
    private CourtyardRoleGroupRelationRepository courtyardRoleGroupRelationRepository;
    @Resource
    private RoleGroupRepository roleGroupRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<CourtyardView> addOrUpdate(CourtyardAddOrUpdateParam param) {
        // 参数基础校验
        if (param == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "参数不能为空");
        }
        try {
            // 名称、图片、角色组校验
            if (StringUtils.isEmpty(param.getName()) || param.getBackgroundImage() == null || param.getFloorGroundImage() == null || CollectionUtil.isEmpty(
                    param.getRoleGroupIds()) || CollectionUtil.isEmpty(param.getFloorGroundImageParts()) || CollectionUtil.isEmpty(
                    param.getBackgroundImageParts())) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "名称、背景图、地面图、角色组不能为空");
            }
            Operation operation;
            CourtyardConfig courtyard;
            // 新增逻辑
            if (param.getId() == null) {
                courtyard = new CourtyardConfig();
                // 设置基本信息
                courtyard.setName(param.getName());
                courtyard.setOperator(AuthContext.getCurrentUser().getName());
                courtyard.setStatus(CommonStatus.NOT_ONLINE.getCode());
                // 设置配置信息
                CourtyardConfig.Config config = new CourtyardConfig.Config();
                config.setBackgroundImage(param.getBackgroundImage());
                config.setFloorGroundImage(param.getFloorGroundImage());
                config.setBackgroundImageParts(param.getBackgroundImageParts());
                config.setFloorGroundImageParts(param.getFloorGroundImageParts());
                courtyard.setConfig(config);
                // 插入数据
                int id = courtyardConfigRepository.insert(courtyard);
                if (id <= 0) {
                    log.error("Insert courtyard failed, param: {}", param);
                    return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "新增庭院失败");
                }
                // 处理角色组关联
                for (Integer roleGroupId : param.getRoleGroupIds()) {
                    if (roleGroupId == null) {
                        continue;
                    }
                    CourtyardRoleGroupRelation relation = new CourtyardRoleGroupRelation();
                    relation.setCourtyardId(id);
                    relation.setRoleGroupId(roleGroupId);
                    courtyardRoleGroupRelationRepository.insert(relation);
                }
                courtyard = courtyardConfigRepository.queryById(id);
                if (courtyard == null) {
                    return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "新增庭院失败");
                }
                operation = Operation.of(OperationConstants.OperateSubType.COURTYARD_ADD_OR_UPDATE).add("id", id).add("newData", GsonUtils.toJson(courtyard));
            } else {
                // 更新逻辑
                CourtyardConfig oldCourtyard = courtyardConfigRepository.queryById(param.getId());
                if (oldCourtyard == null) {
                    log.warn("Courtyard not found, id: {}", param.getId());
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "庭院不存在或更新失败");
                }
                // 更新基本信息
                courtyard = GsonUtils.tryParseObject(GsonUtils.toJson(oldCourtyard), CourtyardConfig.class);
                courtyard.setName(param.getName());
                courtyard.setOperator(AuthContext.getCurrentUser().getName());
                CourtyardConfig.Config config = courtyard.getConfig();
                if (config == null) {
                    config = new CourtyardConfig.Config();
                }
                config.setBackgroundImage(param.getBackgroundImage());
                config.setFloorGroundImage(param.getFloorGroundImage());
                config.setBackgroundImageParts(param.getBackgroundImageParts());
                config.setFloorGroundImageParts(param.getFloorGroundImageParts());
                courtyard.setConfig(config);
                // 更新数据
                courtyardConfigRepository.update(courtyard);
                // 处理角色组关联
                courtyardRoleGroupRelationRepository.deleteByCourtyardId(param.getId());
                for (Integer roleGroupId : param.getRoleGroupIds()) {
                    if (roleGroupId == null) {
                        continue;
                    }
                    CourtyardRoleGroupRelation relation = new CourtyardRoleGroupRelation();
                    relation.setCourtyardId(param.getId());
                    relation.setRoleGroupId(roleGroupId);
                    courtyardRoleGroupRelationRepository.insert(relation);
                }
                courtyard = courtyardConfigRepository.queryById(param.getId());
                operation = Operation.of(OperationConstants.OperateSubType.COURTYARD_ADD_OR_UPDATE)
                        .add("id", param.getId())
                        .add("oldData", GsonUtils.toJson(oldCourtyard))
                        .add("newData", GsonUtils.toJson(courtyard));
            }
            // 异步记录日志
            if (operation != null) {
                OperateLogUtils.asyncRecord(operation);
            }
            return BizResult.success(CourtyardView.of(courtyard));
        } catch (Exception e) {
            log.error("Add or update courtyard error, param: {}", param, e);
            throw e;
        }
    }

    /**
     * 检查角色组是否有其他上架的庭院
     */
    private List<Integer> getGroupsWithoutOnlineCourtyard(Collection<Integer> roleGroupIds, Integer excludeCourtyardId) {
        if (CollectionUtil.isEmpty(roleGroupIds)) {
            return new ArrayList<>();
        }
        // 1. 批量查询所有角色组关联的庭院关系
        List<CourtyardRoleGroupRelation> allRelations = courtyardRoleGroupRelationRepository.queryByRoleGroupIds(roleGroupIds);
        if (CollectionUtil.isEmpty(allRelations)) {
            return new ArrayList<>(roleGroupIds);
        }
        // 2. 获取所有相关的庭院ID（排除当前要操作的庭院）
        Set<Integer> courtyardIds = allRelations.stream()
                .map(CourtyardRoleGroupRelation::getCourtyardId)
                .filter(id -> !Objects.equals(id, excludeCourtyardId))
                .collect(Collectors.toSet());
        // 3. 批量查询所有庭院状态
        List<CourtyardConfig> courtyards = CollectionUtil.isEmpty(courtyardIds)
                ? new ArrayList<>()
                : courtyardConfigRepository.queryByIds(new ArrayList<>(courtyardIds));
        // 4. 构建在线庭院ID集合
        Set<Integer> onlineCourtyardIds = courtyards.stream()
                .filter(c -> CommonStatus.ONLINE.getCode() == (c.getStatus()))
                .map(CourtyardConfig::getId)
                .collect(Collectors.toSet());
        // 5. 按角色组ID分组庭院关系
        Map<Integer, List<CourtyardRoleGroupRelation>> groupRelationsMap = allRelations.stream()
                .collect(Collectors.groupingBy(CourtyardRoleGroupRelation::getRoleGroupId));
        // 6. 找出没有其他上架庭院的角色组
        return roleGroupIds.stream().filter(groupId -> {
            List<CourtyardRoleGroupRelation> relations = groupRelationsMap.get(groupId);
            if (CollectionUtil.isEmpty(relations)) {
                return true;
            }
            // 检查该角色组是否有其他上架的庭院
            return relations.stream()
                    .map(CourtyardRoleGroupRelation::getCourtyardId)
                    .filter(id -> !Objects.equals(id, excludeCourtyardId))
                    .noneMatch(onlineCourtyardIds::contains);
        }).collect(Collectors.toList());
    }

    public BizResult<Void> updateStatus(CourtyardUpdateStatusParam param) {
        // 参数校验
        if (param == null || param.getId() == null || param.getStatus() == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "庭院ID和状态不能为空");
        }
        try {
            Integer id = param.getId();
            Integer status = param.getStatus();
            // 状态值校验,如果不属于上架或下架状态，则返回错误
            if (!CommonStatus.isValidCode(status)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "无效的状态值");
            }
            // 查询庭院
            CourtyardConfig courtyard = courtyardConfigRepository.queryById(id);
            if (courtyard == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "庭院不存在");
            }
            // 如果是下架状态，需要确保角色组中有一个是上架的
            if (CommonStatus.NOT_ONLINE.getCode() == (status)) {
                // 获取当前庭院关联的所有角色组
                List<CourtyardRoleGroupRelation> relations = Optional.ofNullable(courtyardRoleGroupRelationRepository.queryByCourtyardId(id))
                        .orElse(new ArrayList<>());
                // 提取所有角色组ID
                Set<Integer> roleGroupIds = relations.stream()
                        .map(CourtyardRoleGroupRelation::getRoleGroupId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                // 批量检查没有其他上架庭院的角色组
                List<Integer> groupsWithoutOnline = getGroupsWithoutOnlineCourtyard(roleGroupIds, id);
                if (!CollectionUtil.isEmpty(groupsWithoutOnline)) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), String.format("角色组%s下必须保持至少一个上架的庭院", groupsWithoutOnline));
                }
            }

            // 记录旧状态
            Integer oldStatus = courtyard.getStatus();
            // 更新状态
            courtyard.setStatus(status);
            courtyardConfigRepository.update(courtyard);
            // 记录操作日志
            Operation operation = Operation.of(OperationConstants.OperateSubType.COURTYARD_STATUS_UPDATE)
                    .add("id", id)
                    .add("oldStatus", oldStatus)
                    .add("newStatus", status);
            OperateLogUtils.asyncRecord(operation);
            return BizResult.success();
        } catch (Exception e) {
            log.error("Update status error, param: {}", param, e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "系统错误");
        }
    }

    /**
     * 查询庭院详情
     */
    public BizResult<CourtyardView> detail(Integer id) {
        if (id == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "庭院ID不能为空");
        }
        try {
            // 查询庭院基本信息
            CourtyardConfig courtyard = courtyardConfigRepository.queryById(id);
            if (courtyard == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "庭院不存在");
            }
            // 查询庭院关联的角色组关系
            List<CourtyardRoleGroupRelation> relations = courtyardRoleGroupRelationRepository.queryByCourtyardId(id);
            // 即使没有关联关系，也使用valueOf方法转换，保持一致性
            Map<Integer, List<CourtyardRoleGroupRelation>> relationMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(relations)) {
                relationMap.put(id, relations);
            }
            // 获取所有相关的角色组ID
            Set<Integer> roleGroupIds = relations == null
                    ? new HashSet<>()
                    : relations.stream()
                            .filter(Objects::nonNull)
                            .map(CourtyardRoleGroupRelation::getRoleGroupId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            // 根据角色组Id，查询角色Id
            List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIds(roleGroupIds);
            // 获取所有角色ID
            Set<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toSet());
            // 查询角色信息
            Map<Integer, Role> roleMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(roleIds)) {
                List<Role> roles = roleRepository.queryByIds(roleIds);
                roleMap = FunctionUtils.toMap(roles, Role::getId);
            }
            // 查询角色组信息
            Map<Integer, RoleGroup> roleGroupMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(roleGroupIds)) {
                List<RoleGroup> roleGroups = roleGroupRepository.queryByIds(new ArrayList<>(roleGroupIds));
                if (!CollectionUtil.isEmpty(roleGroups)) {
                    roleGroupMap = roleGroups.stream().collect(Collectors.toMap(RoleGroup::getId, roleGroup -> roleGroup, (v1, v2) -> v1));
                }
            }
            return BizResult.success(CourtyardView.valueOf(courtyard, relationMap, roleGroupMap, roleGroupRelations, roleMap));
        } catch (Exception e) {
            log.error("Query courtyard detail error, id: {}", id, e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "系统错误");
        }
    }

    public BizResult<List<CourtyardView>> list() {
        try {
            List<CourtyardConfig> courtyards = courtyardConfigRepository.queryAll();
            if (CollectionUtil.isEmpty(courtyards)) {
                return BizResult.success(new ArrayList<>());
            }
            // 批量获取所有庭院的角色组关系
            List<Integer> courtyardIds = courtyards.stream().map(CourtyardConfig::getId).filter(Objects::nonNull).collect(Collectors.toList());

            // 获取所有关联的角色组关系
            Map<Integer, List<CourtyardRoleGroupRelation>> relationMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(courtyardIds)) {
                List<CourtyardRoleGroupRelation> allRelations = courtyardRoleGroupRelationRepository.queryByCourtyardIds(courtyardIds);
                if (!CollectionUtil.isEmpty(allRelations)) {
                    relationMap = allRelations.stream().collect(Collectors.groupingBy(CourtyardRoleGroupRelation::getCourtyardId));
                }
            }
            // 获取所有相关的角色组ID
            Set<Integer> roleGroupIds = relationMap.values()
                    .stream()
                    .flatMap(Collection::stream)
                    .map(CourtyardRoleGroupRelation::getRoleGroupId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            // 根据角色组Id，查询角色Id
            List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIds(roleGroupIds);
            // 获取所有角色ID
            Set<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toSet());
            Map<Integer, Role> roleMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(roleIds)) {
                List<Role> roles = roleRepository.queryByIds(roleIds);
                roleMap = FunctionUtils.toMap(roles, Role::getId);
            }
            // 批量查询角色组信息
            Map<Integer, RoleGroup> roleGroupMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(roleGroupIds)) {
                List<RoleGroup> roleGroups = roleGroupRepository.queryByIds(new ArrayList<>(roleGroupIds));
                if (!CollectionUtil.isEmpty(roleGroups)) {
                    roleGroupMap = roleGroups.stream().collect(Collectors.toMap(RoleGroup::getId, roleGroup -> roleGroup, (v1, v2) -> v1));
                }
            }
            // 转换为视图对象
            Map<Integer, List<CourtyardRoleGroupRelation>> finalRelationMap = relationMap;
            Map<Integer, RoleGroup> finalRoleGroupMap = roleGroupMap;
            Map<Integer, Role> finalRoleMap = roleMap;
            List<CourtyardView> views = courtyards.stream()
                    .map(courtyard -> CourtyardView.valueOf(courtyard, finalRelationMap, finalRoleGroupMap, roleGroupRelations, finalRoleMap))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return BizResult.success(views);
        } catch (Exception e) {
            log.error("Query courtyard list error", e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "系统错误");
        }
    }

    public BizResult<PageResult<CourtyardView>> page(CourtyardQueryParam param) {
        try {
            if (param == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "参数不能为空");
            }

            // 查询总数
            int totalCount = courtyardConfigRepository.count(Optional.ofNullable(param.getName()).orElse(""),
                    Optional.ofNullable(param.getOperator()).orElse(""), param.getStatus(), param.getCreatedAt(), param.getUpdatedAt());
            if (totalCount == 0) {
                return BizResult.success(new PageResult<>());
            }
            // 分页查询数据
            List<CourtyardConfig> courtyards = courtyardConfigRepository.queryByPage(param.getPageSize(), param.getOffset(), param.getId(),
                    Optional.ofNullable(param.getName()).orElse(""), Optional.ofNullable(param.getOperator()).orElse(""), param.getStatus(),
                    param.getCreatedAt(), param.getUpdatedAt(), param.getOrderColumn(), param.getOrderType(), param.getRoleGroupId());

            // 获取所有庭院ID
            List<Integer> courtyardIds = courtyards.stream().map(CourtyardConfig::getId).filter(Objects::nonNull).collect(Collectors.toList());

            // 批量获取角色组关系
            Map<Integer, List<CourtyardRoleGroupRelation>> relationMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(courtyardIds)) {
                List<CourtyardRoleGroupRelation> allRelations = courtyardRoleGroupRelationRepository.queryByCourtyardIds(courtyardIds);
                if (!CollectionUtil.isEmpty(allRelations)) {
                    relationMap = allRelations.stream().collect(Collectors.groupingBy(CourtyardRoleGroupRelation::getCourtyardId));
                }
            }
            // 获取所有相关的角色组ID
            Set<Integer> roleGroupIds = relationMap.values()
                    .stream()
                    .flatMap(Collection::stream)
                    .map(CourtyardRoleGroupRelation::getRoleGroupId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 根据角色组Id，查询角色Id
            List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIds(roleGroupIds);
            // 获取所有角色ID
            Set<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toSet());
            Map<Integer, Role> roleMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(roleIds)) {
                List<Role> roles = roleRepository.queryByIds(roleIds);
                roleMap = FunctionUtils.toMap(roles, Role::getId);
            }

            // 批量查询角色组信息
            Map<Integer, RoleGroup> roleGroupMap = new HashMap<>();
            if (!CollectionUtil.isEmpty(roleGroupIds)) {
                List<RoleGroup> roleGroups = roleGroupRepository.queryByIds(new ArrayList<>(roleGroupIds));
                if (!CollectionUtil.isEmpty(roleGroups)) {
                    roleGroupMap = roleGroups.stream().collect(Collectors.toMap(RoleGroup::getId, roleGroup -> roleGroup, (v1, v2) -> v1));
                }
            }
            // 转换为视图对象
            Map<Integer, List<CourtyardRoleGroupRelation>> finalRelationMap = relationMap;
            Map<Integer, RoleGroup> finalRoleGroupMap = roleGroupMap;
            Map<Integer, Role> finalRoleMap = roleMap;
            List<CourtyardView> views = courtyards.stream()
                    .map(courtyard -> CourtyardView.valueOf(courtyard, finalRelationMap, finalRoleGroupMap, roleGroupRelations, finalRoleMap))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<CourtyardView> pageResult = new PageResult<>();
            pageResult.setList(views);
            pageResult.setPageNum(param.getPage());
            pageResult.setPageSize(param.getPageSize());
            pageResult.setTotalCount(totalCount);

            return BizResult.success(pageResult);
        } catch (Exception e) {
            log.error("Page query error, param: {}", param, e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "系统错误");
        }
    }
}