package com.kuaikan.role.game.admin.biz;

import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.business.enums.GenderType;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.fox.service.UserClassifyService;
import com.kuaikan.paybanner.bean.UserClassify;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.model.param.ResourceAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.ResourceConfigView;
import com.kuaikan.role.game.admin.model.view.TopicView;
import com.kuaikan.role.game.admin.repository.ResourceConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.api.bean.ResourceConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.enums.ResourceConfigStatus;
import com.kuaikan.role.game.api.enums.TopicSelectType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.kuaikan.role.game.common.enums.CacheConfig.ALL_PUBLISH_RESOURCE_INFO;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
@Service
public class ResourceConfigBiz {

    @Resource
    private ResourceConfigRepository resourceConfigRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private UserClassifyService userClassifyService;

    public BizResult<Void> addOrUpdate(ResourceAddOrUpdateParam resourceAddOrUpdateParam) {
        ResourceConfig resourceConfig = resourceAddOrUpdateParam.toBean();
        Operation operation = Operation.of(OperationConstants.OperateSubType.INSERT_OR_UPDATE_RESOURCE_CONFIG)
                .add("newData", JsonUtils.toJson(resourceAddOrUpdateParam));
        if (resourceAddOrUpdateParam.getId() > 0) {
            ResourceConfig dbResourceConfig = resourceConfigRepository.queryByPrimaryKey(resourceAddOrUpdateParam.getId());
            operation.add("oldData", JsonUtils.toJson(dbResourceConfig));
            resourceConfigRepository.updateByPrimaryKeySelective(resourceConfig);
        } else {
            resourceConfigRepository.insert(resourceConfig);
        }
        deleteRoleCache();
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<PageView<ResourceConfigView>> list(int pageNum, int pageSize) {
        int total = resourceConfigRepository.countAll();
        if (total == 0) {
            return BizResult.success(PageView.empty());
        }
        int offset = (pageNum - 1) * pageSize;
        List<ResourceConfig> resourceConfigs = resourceConfigRepository.queryByPage(offset, pageSize);

        List<Integer> payUserClassifyList = resourceConfigs.stream().map(ResourceConfig::getConfig)
                .filter(Objects::nonNull)
                .map(ResourceConfig.Config::getPayUserClassify)
                .filter(payUserClassify -> Objects.nonNull(payUserClassify) && payUserClassify != -1)
                .collect(Collectors.toList());
        Map<Integer, String> payUserClassifyMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(payUserClassifyList)) {
            payUserClassifyMap.putAll(userClassifyService.getByIds(payUserClassifyList).stream().collect(Collectors.toMap(UserClassify::getId, UserClassify::getClassifyName)));
        }

        Map<Integer, String> topicMap = getAllTopics().stream().collect(Collectors.toMap(TopicView::getTopicId, TopicView::getTopicName));
        List<ResourceConfigView> result = resourceConfigs.stream()
                .map(ResourceConfigView::valueOf)
                .peek(view -> {
                    List<TopicView> deliveryTopicList = view.getDeliveryTopicList();
                    for (TopicView topicView : deliveryTopicList) {
                        topicView.setTopicName(topicMap.get(topicView.getTopicId()));
                    }
                    if (Objects.isNull(view.getPayUserClassify()) || view.getPayUserClassify() == -1) {
                        view.setPayUserClassifyName("全体用户");
                    } else {
                        view.setPayUserClassifyName(payUserClassifyMap.get(view.getPayUserClassify()));
                    }

                    if (Objects.isNull(view.getDeliveryTopicType())) {
                        view.setDeliveryTopicType(TopicSelectType.All_IP.getCode());
                    }

                    if (Objects.isNull(view.getCrowdPackageDeliveryType())) {
                        view.setCrowdPackageDeliveryType(0);
                    }

                    if (Objects.isNull(view.getGenderType())) {
                        view.setGenderType(GenderType.ALL.getCode());
                    }

                    if (Objects.isNull(view.getPayUserClassify())) {
                        view.setPayUserClassify(-1);
                    }
                })
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(total, result));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateSort(int configId, int order) {
        List<ResourceConfig> resourceConfigs = resourceConfigRepository.queryAll();
        ResourceConfig resourceConfig = resourceConfigs.stream().filter(item -> item.getId() == configId).findAny().orElse(null);
        if (resourceConfig == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "资源位配置不存在");
        }
        if (order == 0) {
            resourceConfig.setOrderNum(order);
            resourceConfigRepository.updateByPrimaryKeySelective(resourceConfig);
            deleteRoleCache();
            return BizResult.success();
        }
        int oldOrder = resourceConfig.getOrderNum();
        if (order > oldOrder) {
            resourceConfigRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(order);
            resourceConfig.setOrderNum(order);
            resourceConfigRepository.updateByPrimaryKeySelective(resourceConfig);
        } else if (order < oldOrder) {
            ResourceConfig sameOrderConfig = resourceConfigs.stream().filter(item -> item.getOrderNum() == order).findAny().orElse(null);
            if (sameOrderConfig != null) {
                sameOrderConfig.setOrderNum(oldOrder);
                resourceConfigRepository.updateByPrimaryKeySelective(sameOrderConfig);
            }
            resourceConfig.setOrderNum(order);
            resourceConfigRepository.updateByPrimaryKeySelective(resourceConfig);
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.CHANGE_RESOURCE_CONFIG_ORDER)
                .add("id", configId)
                .add("oldOrder", oldOrder)
                .add("newOrder", order);
        OperateLogUtils.asyncRecord(operation);
        deleteRoleCache();
        return BizResult.success();
    }

    public BizResult<Void> publish(int configId) {
        ResourceConfig resourceConfig = resourceConfigRepository.queryByPrimaryKey(configId);
        if (resourceConfig == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "资源位配置不存在");
        }
        resourceConfigRepository.updateByPrimaryKeySelective(resourceConfig.setStatus(ResourceConfigStatus.ONLINE.getCode()));
        deleteRoleCache();
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.CHANGE_PUBLISH_STATUS).add("id", configId).add("operation", "上架"));
        return BizResult.success();
    }

    public BizResult<Void> offline(int configId) {
        ResourceConfig resourceConfig = resourceConfigRepository.queryByPrimaryKey(configId);
        if (resourceConfig == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "资源位配置不存在");
        }
        resourceConfigRepository.updateByPrimaryKeySelective(resourceConfig.setStatus(ResourceConfigStatus.NOT_ONLINE.getCode()));
        deleteRoleCache();
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.CHANGE_PUBLISH_STATUS).add("id", configId).add("operation", "下架"));
        return BizResult.success();
    }

    private void deleteRoleCache() {
        LettuceClusterClient lettuceClusterClient = LettuceClusterUtil.getClusterClientByName(ALL_PUBLISH_RESOURCE_INFO.getReadWriteVip());
        lettuceClusterClient.del(ALL_PUBLISH_RESOURCE_INFO.getKeyPattern());
    }

    public List<TopicView> getAllTopics() {
        Set<TopicView> set = Sets.newHashSet();
        List<Role> roles = roleRepository.queryAll();
        if (CollectionUtils.isNotEmpty(roles)) {
            roles.forEach(role -> {
                set.add(new TopicView()
                        .setTopicName(role.getTopicName())
                        .setTopicId(role.getTopicId()));
            });
        }

        List<TopicView> result = new ArrayList<>();
        Map<Integer, List<TopicView>> map = set.stream().collect(Collectors.groupingBy(TopicView::getTopicId));
        for (Map.Entry<Integer, List<TopicView>> entry : map.entrySet()) {
            result.add(entry.getValue().get(0));
        }

        return result;
    }

    public BizResult<List<TopicView>> findAllTopics() {
        return BizResult.success(getAllTopics());
    }
}
