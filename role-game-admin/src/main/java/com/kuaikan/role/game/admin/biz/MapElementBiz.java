package com.kuaikan.role.game.admin.biz;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.converter.MapElementConverter;
import com.kuaikan.role.game.admin.model.param.MapElementAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.UpdateStatusParam;
import com.kuaikan.role.game.admin.model.view.MapElementDetailView;
import com.kuaikan.role.game.admin.model.view.MapElementView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.repository.BuildingMapRepository;
import com.kuaikan.role.game.admin.repository.BuildingRepository;
import com.kuaikan.role.game.admin.repository.MapCityRepository;
import com.kuaikan.role.game.admin.repository.MapElementRepository;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.enums.BuildingType;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.common.bean.BuildingMap;
import com.kuaikan.role.game.common.bean.MapCity;
import com.kuaikan.role.game.common.bean.MapElement;
import com.kuaikan.role.game.common.bean.MapElementSkin;
import com.kuaikan.role.game.common.enums.MapElementType;
import com.kuaikan.role.game.common.exception.BusinessException;

/**
 * 地图元素逻辑.
 * <AUTHOR>
 */
@Service
public class MapElementBiz {

    @Resource
    private MapElementRepository mapElementRepository;

    @Resource
    private MapElementConverter mapElementConverter;

    @Resource
    private BuildingMapRepository buildingMapRepository;

    @Resource
    private MapCityRepository mapCityRepository;

    @Resource
    private BuildingRepository buildingRepository;

    public BizResult<PageResult<MapElementView>> selectMapElementList(Integer id, String name, String creator, List<Integer> mapElementTypes, Integer statusId,
                                                                      Integer pageNum, Integer pageSize) {
        CommonStatus status = null;
        if (statusId != null) {
            status = CommonStatus.getByCode(statusId);
            if (status == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "状态不存在");
            }
        }
        PageInfo<MapElement> pageInfo = mapElementRepository.selectList(id, name, creator, status, mapElementTypes, pageNum, pageSize);
        List<MapElement> mapElementList = pageInfo.getList();
        List<MapElementView> mapElementViewList = mapElementList.stream().map(mapElement -> {
            MapElementView mapElementView = mapElementConverter.toMapElementView(mapElement);
            return buildingSplitMapping(mapElementView, mapElement);
        }).collect(Collectors.toList());
        PageResult<MapElementView> pageResult = PageResult.from(pageInfo, mapElementViewList);
        return BizResult.success(pageResult);
    }

    // 元素类型转建筑类型,元素图片展示
    private MapElementView buildingSplitMapping(MapElementView mapElementView, MapElement mapElement) {
        MapElementSkin mapElementSkin = mapElement.getMapElementSkin();
        if (mapElementSkin == null) {
            return mapElementView;
        }
        MapElementSkin.Config config = mapElementSkin.getConfig();
        if (config == null) {
            return mapElementView;
        }
        Integer buildingNameId = config.getBuildingNameId();
        if (buildingNameId == null) {
            return mapElementView;
        }
        Building building = buildingRepository.queryById(buildingNameId);
        if (building == null) {
            return mapElementView;
        }
        mapElementView.setBuildingTypeName(BuildingType.getByCode(building.getType()).getDesc());
        if (CollectionUtils.isEmpty(config.getElementPictureList())) {
            return mapElementView;
        }
        mapElementView.setElementImgUrl(config.getElementPictureList().get(0).getImageInfo().getUrl());
        return mapElementView;
    }

    public BizResult updateStatus(UpdateStatusParam param, String userName) {
        Optional<MapElement> mapElementOpt = mapElementRepository.selectMapElementById(param.getId());
        if (!mapElementOpt.isPresent()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "地图元素ID不存在");
        }
        checkUpdateStatus(mapElementOpt.get(), param);
        CommonStatus status = CommonStatus.getByCode(param.getStatus());
        if (status == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "地图元素状态不存在");
        }
        mapElementRepository.updateStatus(param.getId(), status, userName);
        Operation operation = Operation.of(OperationConstants.OperateSubType.MAP_ELEMENT_UPDATE_STATUS)
                .add("id", String.valueOf(param.getId()))
                .add("newStatus", param.getStatus());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult addOrUpdate(MapElementAddOrUpdateParam param, String userName) {
        checkParam(param);
        MapElement mapElement = mapElementConverter.toMapElement(param);
        MapElementSkin mapElementSkin = mapElementConverter.toMapElementSkin(param);
        if (param.getElementTypeId() == MapElementType.COMMON_BUILDING.getCode()
                || param.getElementTypeId() == MapElementType.NORMAL_BUILDING.getCode()
                || param.getElementTypeId() == MapElementType.REWARD_BUILDING.getCode()) {
            mapElement.getConfig().setBuildingId(param.getBuildingNameId());
        }
        if(param.getElementTypeId() == MapElementType.DECORATION.getCode()){
            mapElement.getConfig().setBuildingId(param.getRelatedBuildingId());
        }
        if (mapElement.getId() == null) {
            mapElementRepository.insertMapElement(mapElement, mapElementSkin, userName);
            return BizResult.success();
        } else {
            Optional<MapElement> oldMapElement = mapElementRepository.selectMapElementById(mapElement.getId());
            if (!oldMapElement.isPresent()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "地图元素ID不存在");
            }
            mapElementRepository.updateMapElement(mapElement, mapElementSkin, userName, oldMapElement.get().getStatus());
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.MAP_ELEMENT_ADD_OR_UPDATE).add("newData", JsonUtils.writeValueAsString(param));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<MapElementDetailView> getMapElementDetailViewById(Integer id) {
        Optional<MapElementDetailView> mapElementDetailView = mapElementRepository.selectMapElementDetailById(id);
        if (mapElementDetailView.isPresent()) {
            return BizResult.success(mapElementDetailView.get());
        } else {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "元素ID不存在");
        }
    }

    private void checkParam(MapElementAddOrUpdateParam param) {
        if (param.getName() == null) {
            throw new BusinessException("名称不能为空");
        }
        if (param.getElementTypeId() == null) {
            throw new BusinessException("元素类型不能为空");
        }
        if (param.getScaleRatio() == null) {
            throw new BusinessException("缩放比例不能为空");
        }
        MapElementType mapElementType = MapElementType.getByCode(param.getElementTypeId());
        if (mapElementType == MapElementType.UNKNOWN) {
            throw new BusinessException("元素类型不存在");
        }
        // 元素类型-通用建筑
        if (mapElementType == MapElementType.COMMON_BUILDING) {
            if (param.getBuildingNameId() == null) {
                throw new BusinessException("建筑ID不能为空");
            }
            // TODO:如果建筑类型-主建筑非必填，则不用校验元素图片
            Building building = buildingRepository.queryById(param.getBuildingNameId());
            if (BuildingType.UNKNOWN == BuildingType.getByCode(building.getType())) {
                throw new BusinessException("建筑类型不存在");
            }
            // 主建筑校验元素图片
            if (BuildingType.MAIN_BUILDING == BuildingType.getByCode(building.getType())) {
                if (CollectionUtils.isEmpty(param.getElementPictureList())) {
                    param.setElementPictureList(new ArrayList<>());
                } // 娱乐建筑、打工建筑校验图片素材
            } else if (param.getImageInfo() == null) {
                throw new BusinessException("图片素材不能为空");
            }
        }
        if (mapElementType == MapElementType.SPECIAL_BUILDING) {
            if (param.getSpecialBuildingType() == null) {
                throw new BusinessException("特殊建筑类型不能为空");
            }
        }
        if (mapElementType == MapElementType.NPC) {
            if (param.getSpineMaterial() == null) {
                throw new BusinessException("骨骼动画文件不能为空");
            }
        }
        if (mapElementType == MapElementType.DECORATION) {
            if (param.getImageInfo() == null) {
                throw new BusinessException("图片素材不能为空");
            }
        }
    }

    private void checkUpdateStatus(MapElement mapElement, UpdateStatusParam param) {
        if (mapElement.getStatus() == CommonStatus.ONLINE && param.getStatus() == CommonStatus.ONLINE.getCode()) {
            throw new BusinessException("地图元素已上线");
        }
        if (mapElement.getStatus() == CommonStatus.NOT_ONLINE && param.getStatus() == CommonStatus.NOT_ONLINE.getCode()) {
            throw new BusinessException("地图元素已下线");
        }
        if (param.getStatus() == CommonStatus.NOT_ONLINE.getCode()) {
            List<MapCity> mapCityList = mapCityRepository.query(null, null, null);
            mapCityList.forEach(mapCity -> {
                boolean hasOnlineCityUse = mapCity.getConfig()
                        .getMapElementConfigList()
                        .stream()
                        .anyMatch(mapElementConfig -> mapElementConfig.getElementId().equals(mapElement.getId()));
                if (hasOnlineCityUse) {
                    throw new BusinessException(String.format("已在城市地图id:%d中使用", mapCity.getId()));
                }
            });
            List<BuildingMap> buildingMapList = buildingMapRepository.selectBuildingMapList(null, null, null);
            if (CollectionUtils.isNotEmpty(buildingMapList)) {
                buildingMapList.forEach(buildingMap -> {
                    if (CollectionUtils.isEmpty(buildingMap.getBuildingAreaMapList())) {
                        return;
                    }
                    boolean hasOnlineBuildingUse = buildingMap.getBuildingAreaMapList()
                            .stream()
                            .flatMap(buildingAreaMap -> buildingAreaMap.getConfig().getMapElementConfigList().stream())
                            .anyMatch(mapElementConfig -> mapElementConfig.getElementId().equals(mapElement.getId()));
                    if (hasOnlineBuildingUse) {
                        throw new BusinessException(String.format("已在内景地图id:%d中使用", buildingMap.getId()));
                    }
                });
            }
        }
    }

}
