package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.model.param.FoodAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.FoodView;
import com.kuaikan.role.game.admin.repository.FoodRepository;
import com.kuaikan.role.game.common.bean.Food;

/**
 * <AUTHOR>
 * @version 2024-10-30
 */
@Service
public class FoodBiz {

    @Resource
    private FoodRepository foodRepository;

    public BizResult<Void> addOrUpdate(FoodAddOrUpdateParam param) {
        final int id = param.getId();
        if (id > 0) {
            final Food food = foodRepository.queryById(id);
            Operation operation = Operation.of(OperationConstants.OperateSubType.FOOD_ADD_OR_UPDATE).add("oldData", JsonUtils.writeValueAsString(food));
            if (food == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "食物不存在");
            }
            food.setName(param.getName());
            final Food.Config config = food.getConfig();
            config.setImage(param.getImage());
            foodRepository.updateByPrimaryKeySelective(food);
            operation.add("newData", JsonUtils.writeValueAsString(food));
            OperateLogUtils.asyncRecord(operation);
        } else {
            final Food food = new Food();
            food.setName(param.getName());
            final Food.Config config = new Food.Config();
            config.setImage(param.getImage());
            food.setConfig(config);
            foodRepository.insert(food);
            Operation operation = Operation.of(OperationConstants.OperateSubType.FOOD_ADD_OR_UPDATE).add("newData", JsonUtils.writeValueAsString(food));
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();
    }

    public BizResult<PageView<FoodView>> list(int pageNum, int pageSize) {
        final List<Food> foods = foodRepository.queryByPage((pageNum - 1) * pageSize, pageSize);
        final int total = foodRepository.queryAll().size();
        final List<FoodView> foodViews = foods.stream().map(FoodView::valueOf).collect(Collectors.toList());
        return BizResult.success(PageView.form(total, foodViews));
    }

    public BizResult<Void> updateSort(int id, int order) {
        List<Food> foods = foodRepository.queryAll();
        Food food = foods.stream().filter(item -> item.getId() == id).findAny().orElse(null);
        if (food == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "食物不存在");
        }
        if (order == 0) {
            food.setOrderNum(order);
            foodRepository.updateByPrimaryKeySelective(food);
            return BizResult.success();
        }
        int oldOrder = food.getOrderNum();
        if (order > oldOrder) {
            foodRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(order);
            food.setOrderNum(order);
            foodRepository.updateByPrimaryKeySelective(food);
        } else if (order < oldOrder) {
            Food sameOrderRole = foods.stream().filter(item -> item.getOrderNum() == order).findAny().orElse(null);
            if (sameOrderRole != null) {
                sameOrderRole.setOrderNum(oldOrder);
                foodRepository.updateByPrimaryKeySelective(sameOrderRole);
            }
            food.setOrderNum(order);
            foodRepository.updateByPrimaryKeySelective(food);
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.FOOD_ORDER_UPDATE).add("id", id).add("oldOrder", oldOrder).add("newOrder", order);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }
}
