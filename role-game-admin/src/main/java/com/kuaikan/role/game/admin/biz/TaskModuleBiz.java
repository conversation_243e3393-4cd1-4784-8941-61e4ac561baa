package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.model.param.TaskModuleAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.TaskModuleConfigView;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.TaskModuleConfig;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;

@Slf4j
@Service
public class TaskModuleBiz {

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private RedDotService redDotService;

    public BizResult<Void> addOrUpdate(TaskModuleAddOrUpdateParam param) {
        String key = KeyValueConfigKeys.TASK_MODULE_CONFIG;
        List<Long> taskIdList = param.getTaskConfigList().stream().map(TaskModuleAddOrUpdateParam.TaskConfigParam::getTaskId).collect(Collectors.toList());
        if (taskIdList.size() != taskIdList.stream().distinct().count()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "任务id不能重复");
        }
        updateKeyValueConfig(key, JsonUtils.toJson(param.toBean()), taskIdList);
        return BizResult.success();
    }

    void updateKeyValueConfig(String key, String value, List<Long> taskIdList) {
        log.info("updateKeyValueConfig key: {}, value: {}", key, value);
        KeyValueConfig keyValueConfig = new KeyValueConfig().setKey(key).setValue(value);
        KeyValueConfig dbKeyValueConfig = keyValueConfigRepository.queryByKey(key);
        if (dbKeyValueConfig == null) {
            keyValueConfigRepository.insert(keyValueConfig);
        } else {
            keyValueConfigRepository.updateByKey(keyValueConfig);
        }
        redDotService.sendNewTaskEvent(taskIdList);
        Operation operation = Operation.of(OperationConstants.OperateSubType.TASK_MODULE_CONFIG)
                .add("oldData", JsonUtils.toJson(dbKeyValueConfig))
                .add("newData", JsonUtils.toJson(keyValueConfig));
        OperateLogUtils.asyncRecord(operation);
    }

    public BizResult<TaskModuleConfigView> detail() {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.TASK_MODULE_CONFIG);
        if (keyValueConfig == null) {
            return BizResult.success();
        }
        TaskModuleConfig taskModuleConfig = JsonUtils.fromJson(keyValueConfig.getValue(), TaskModuleConfig.class);
        TaskModuleConfigView taskModuleConfigView = TaskModuleConfigView.valueOf(taskModuleConfig);
        return BizResult.success(taskModuleConfigView);
    }

}
