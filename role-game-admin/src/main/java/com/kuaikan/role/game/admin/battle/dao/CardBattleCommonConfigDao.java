package com.kuaikan.role.game.admin.battle.dao;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCommonConfig;

/**
 * 通用配置 mapper
 *
 * <AUTHOR>
 * @date 2024/6/18
 */
public interface CardBattleCommonConfigDao {

    int insert(CardBattleCommonConfig record);

    CardBattleCommonConfig findFirst();

    CardBattleCommonConfig selectByPrimaryKey(@Param("id") Long id);

    int updateByPrimaryKey(CardBattleCommonConfig record);

}
