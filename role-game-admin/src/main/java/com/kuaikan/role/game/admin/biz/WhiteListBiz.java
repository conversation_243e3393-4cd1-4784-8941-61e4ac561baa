package com.kuaikan.role.game.admin.biz;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;

/**
 * <AUTHOR>
 * @version 2024-08-08
 */
@Service
public class WhiteListBiz {

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    public BizResult<Void> edit(Set<Integer> ids) {

        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.WHITE_LIST);
        if (keyValueConfig == null) {
            keyValueConfig = new KeyValueConfig().setKey(KeyValueConfigKeys.WHITE_LIST).setValue(JsonUtils.writeValueAsString(ids));
            keyValueConfigRepository.insert(keyValueConfig);
        } else {
            keyValueConfig.setValue(JsonUtils.writeValueAsString(ids));
            keyValueConfigRepository.updateByKey(keyValueConfig);
        }
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.WRITE_LIST_IDS).add("ids", ids));
        return BizResult.success();
    }

    public BizResult<List<Integer>> query() {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.WHITE_LIST);
        if (keyValueConfig == null) {
            return BizResult.success(Collections.emptyList());
        }
        return BizResult.success(JsonUtils.findList(keyValueConfig.getValue(), Integer.class));
    }

}
