package com.kuaikan.role.game.admin.biz;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.dao.mongo.RoleGroupBlindBoxConfigDao;
import com.kuaikan.role.game.admin.model.excel.MockLotteryCostumeExcelDTO;
import com.kuaikan.role.game.admin.model.param.CostumeAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.SpineMaterialParam;
import com.kuaikan.role.game.admin.model.view.BasicCostumeView;
import com.kuaikan.role.game.admin.model.view.CostumeBlindBoxConfigView;
import com.kuaikan.role.game.admin.model.view.CostumeView;
import com.kuaikan.role.game.admin.model.view.ProbabilityRuleView;
import com.kuaikan.role.game.admin.model.view.SimpleCostumeView;
import com.kuaikan.role.game.admin.model.view.SimpleGroupCostumeView;
import com.kuaikan.role.game.admin.model.view.SpineMaterialView;
import com.kuaikan.role.game.admin.repository.BlindBoxProbRuleConfigRepository;
import com.kuaikan.role.game.admin.repository.CostumePartRelationRepository;
import com.kuaikan.role.game.admin.repository.CostumePartRepository;
import com.kuaikan.role.game.admin.repository.CostumeRepository;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleCostumeRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.utils.DateUtil;
import com.kuaikan.role.game.admin.utils.ExcelUtils;
import com.kuaikan.role.game.api.bean.BlindBoxProbRuleConfig;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxConfig;
import com.kuaikan.role.game.api.bean.CostumePart;
import com.kuaikan.role.game.api.bean.CostumePartRelation;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleCostumeRelation;
import com.kuaikan.role.game.api.bean.RoleGroupCostumeBlindBoxConfig;
import com.kuaikan.role.game.api.bean.SpineMaterial;
import com.kuaikan.role.game.api.enums.CostumeFileType;
import com.kuaikan.role.game.api.enums.CostumeStatus;
import com.kuaikan.role.game.api.enums.RoleStatus;
import com.kuaikan.role.game.api.model.BlindBoxDrawResultModel;
import com.kuaikan.role.game.api.model.MockLotteryResultModel;
import com.kuaikan.role.game.api.rpc.param.CostumeBlindBoxConfigParam;
import com.kuaikan.role.game.api.rpc.param.MockLotteryParam;
import com.kuaikan.role.game.api.rpc.param.ProbabilityRuleParam;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.api.service.UserCostumeService;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.common.enums.CacheConfig;


/**
 * <AUTHOR>
 * @date 2024/2/27
 */
@Slf4j
@Service
public class CostumeBiz {

    private static final int MINIMUM_COSTUME_PART_COUNT = 2;

    @Resource
    private CostumeRepository costumeRepository;
    @Resource
    private CostumePartRepository costumePartRepository;

    @Resource
    private RoleCostumeRelationRepository roleCostumeRelationRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private CostumePartRelationRepository costumePartRelationRepository;

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;

    @Resource
    private RedDotService redDotService;

    @Resource
    private BlindBoxProbRuleConfigRepository blindBoxProbRuleConfigRepository;

    @Resource
    private RoleGroupBlindBoxConfigDao roleGroupBlindBoxConfigDao;

    @Resource
    private UserCostumeService userCostumeService;

    @Resource
    private MaterialBiz materialBiz;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(CostumeAddOrUpdateParam addOrUpdateParam) {
        int roleId = addOrUpdateParam.getRoleId();
        int id = addOrUpdateParam.getId();
        SpineMaterialParam actionMaterial = addOrUpdateParam.getActionMaterial();
        SpineMaterial spineMaterial = SpineMaterialParam.toMaterialInfo(actionMaterial);

        SpineMaterialParam actionSmallMaterial = addOrUpdateParam.getActionSmallMaterial();
        SpineMaterial spineSmallMaterial = SpineMaterialParam.toMaterialInfo(actionSmallMaterial);

        if (roleRepository.queryById(roleId) == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }

        CostumeFileType fileTypeEnum = CostumeFileType.getByCode(addOrUpdateParam.getFileType());
        if (CostumeFileType.UNKNOWN == fileTypeEnum) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮文件类型错误");
        }

        Integer fileType = fileTypeEnum.getCode();
        Integer relatedBasicId = addOrUpdateParam.getRelatedBasicId();

        Operation operation;
        if (id > 0) {
            Costume costume = costumeRepository.queryById(id);
            String old = JsonUtils.writeValueAsString(costume);
            if (costume == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色装扮不存在");
            }
            costume.setName(addOrUpdateParam.getName());
            Costume.Config config = addOrUpdateParam.toCostumeConfig()
                    .setActionSpineMaterial(spineMaterial)
                    .setActionSpineSmallMaterial(spineSmallMaterial)
                    .setRelatedBasicId(relatedBasicId);
            costume.setConfig(config);
            costume.setLevel(addOrUpdateParam.getLevel());
            costume.setFileType(fileType);
            costumeRepository.updateByPrimaryKeySelective(costume);
            operation = Operation.of(OperationConstants.OperateSubType.COSTUME_ADD_OR_UPDATE)
                    .add("oldData", old)
                    .add("newData", JsonUtils.writeValueAsString(costume));
        } else {
            Costume.Config config = addOrUpdateParam.toCostumeConfig()
                    .setActionSpineMaterial(spineMaterial)
                    .setActionSpineSmallMaterial(spineSmallMaterial)
                    .setRelatedBasicId(relatedBasicId);
            Costume costume = new Costume().setConfig(config)
                    .setName(addOrUpdateParam.getName())
                    .setStatus(CostumeStatus.NOT_ON_SHELF.getCode())
                    .setLevel(addOrUpdateParam.getLevel());
            costume.setFileType(fileType);
            costumeRepository.insert(costume);
            RoleCostumeRelation roleCostumeRelation = new RoleCostumeRelation().setRoleId(roleId).setCostumeId(costume.getId());
            roleCostumeRelationRepository.insertBatch(Collections.singletonList(roleCostumeRelation));
            id = costume.getId();
            operation = Operation.of(OperationConstants.OperateSubType.COSTUME_ADD_OR_UPDATE).add("newData", JsonUtils.writeValueAsString(addOrUpdateParam));
        }
        deleteCache(id);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<PageView<CostumeView>> list(int pageNum, int pageSize) {
        int count = costumeRepository.count();
        if (count == 0) {
            return BizResult.success(PageView.empty());
        }
        int offset = (pageNum - 1) * pageSize;
        List<Costume> costumes = costumeRepository.queryByPage(offset, pageSize);
        List<Integer> costumeIds = costumes.stream().map(Costume::getId).collect(Collectors.toList());
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByCostumeIds(costumeIds);
        Map<Integer, Integer> costumeId2RoleIdMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, RoleCostumeRelation::getRoleId));
        Set<Integer> roleIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getRoleId).collect(Collectors.toSet());
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, role -> role));

        List<CostumeView> costumeViews = costumes.stream().map(item -> CostumeView.valueOf(item, costumeId2RoleIdMap, roleMap)).collect(Collectors.toList());
        return BizResult.success(PageView.form(count, costumeViews));
    }

    public BizResult<List<SimpleGroupCostumeView>> listByGroupId(int groupId) {
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupId(groupId);
        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByRoleIds(roleIds);
        List<Integer> costumeIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toList());
        Map<Integer, Costume> costumeMap = costumeRepository.queryByIds(costumeIds).stream().collect(Collectors.toMap(Costume::getId, Function.identity()));
        Map<Integer, List<RoleCostumeRelation>> roleCostumeMap = roleCostumeRelations.stream().collect(Collectors.groupingBy(RoleCostumeRelation::getRoleId));
        List<SimpleGroupCostumeView> simpleGroupCostumeViews = roleIds.stream().map(roleId -> {
            List<RoleCostumeRelation> relations = roleCostumeMap.get(roleId);
            List<SimpleCostumeView> simpleCostumeViews = relations.stream()
                    .map(relation -> costumeMap.getOrDefault(relation.getCostumeId(), null))
                    .filter(Objects::nonNull)
                    .map(SimpleCostumeView::valueOf)
                    .collect(Collectors.toList());
            return new SimpleGroupCostumeView().setRoleId(roleId).setCostumeViews(simpleCostumeViews);
        }).collect(Collectors.toList());
        return BizResult.success(simpleGroupCostumeViews);
    }

    public BizResult<List<String>> listAnimations(int costumeId) {
        Costume costume = costumeRepository.queryById(costumeId);
        Set<String> animations = costume.getConfig().getActionSpineMaterial().getAnimations();
        if (CollectionUtils.isEmpty(animations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "动画配置不存在");
        }
        return BizResult.success(Lists.newArrayList(animations).stream().sorted(Collections.reverseOrder()).collect(Collectors.toList()));
    }

    public BizResult<Void> publish(int costumeId) {
        Costume costume = costumeRepository.queryById(costumeId);
        if (costume == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色装扮不存在");
        }
        if (costume.getStatus() == CostumeStatus.ON_SHELF.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色装扮已上架");
        }
        final List<CostumePartRelation> costumePartRelations = costumePartRelationRepository.selectByCostumeId(costumeId);
        if (CollectionUtils.size(costumePartRelations) < MINIMUM_COSTUME_PART_COUNT) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "单品数量不足");
        }
        final List<CostumePart> costumeParts = costumePartRepository.selectByIds(
                costumePartRelations.stream().map(CostumePartRelation::getCostumePartId).collect(Collectors.toSet()));
        for (CostumePart costumePart : costumeParts) {
            if (costumePart == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "单品不存在");
            }
            if (costumePart.getConfig() == null || costumePart.getConfig().getComposeConfig() == null || CollectionUtils.isEmpty(
                    costumePart.getConfig().getComposeConfig().getStuffConfigs()) || costumePart.getConfig().getComposeConfig().getSilverCoinCount() < 0) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮单品材料配置表配置信息错误");
            }
        }
        costume.setStatus(CostumeStatus.ON_SHELF.getCode());
        costumeRepository.updateByPrimaryKeySelective(costume);
        redDotService.sendAddCostumeEvent(costume.getId());
        deleteCache(costumeId);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.COSTUME_PUBLISH).add("id", costumeId));
        return BizResult.success();
    }

    public BizResult<Void> offline(int costumeId) {
        Costume costume = costumeRepository.queryById(costumeId);
        if (costume == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色装扮不存在");
        }
        List<String> onlineRoleName = roleRepository.queryByCostumeId(costumeId)
                .stream()
                .filter(item -> item.getStatus() == RoleStatus.ONLINE.getCode())
                .map(Role::getName)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(onlineRoleName)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮已被角色《" + StringUtils.join(onlineRoleName, ",") + "》使用");
        }
        if (costume.getStatus() != CostumeStatus.ON_SHELF.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色装扮未上架");
        }
        costume.setStatus(CostumeStatus.OFF_SHELF.getCode());
        costumeRepository.updateByPrimaryKeySelective(costume);
        deleteCache(costumeId);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.COSTUME_OFFLINE).add("id", costumeId));
        return BizResult.success();
    }

    public BizResult<List<SimpleCostumeView>> intersectCostumesByRoles(Collection<Integer> roleIds) {
        final List<Integer> costumeIds = roleCostumeRelationRepository.intersectCostumeIdByRoles(roleIds, roleIds.size());
        if (CollectionUtils.isEmpty(costumeIds)) {
            return BizResult.success();
        }
        final List<Costume> costumes = costumeRepository.queryByIds(costumeIds);
        return BizResult.success(costumes.stream().map(SimpleCostumeView::valueOf).collect(Collectors.toList()));
    }

    public BizResult<List<String>> defaultConfigAnimations(int roleId) {
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        int defaultCostumeId = role.getDefaultCostumeId();
        if (defaultCostumeId == 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在默认装扮");
        }
        Costume costume = costumeRepository.queryById(defaultCostumeId);
        if (costume.getConfig().getActionSpineMaterial() == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色默认装扮未配置动画");
        }
        Set<String> animations = costume.getConfig().getActionSpineMaterial().getAnimations();
        List<String> animationsList = new ArrayList<>(animations);
        animationsList.sort(String::compareTo);
        return BizResult.success(animationsList);
    }

    public BizResult<CostumeBlindBoxConfigView> getCostumeBlindBoxConfig() {
        final KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG);
        if (keyValueConfig == null) {
            return BizResult.success();
        }
        CostumeBlindBoxConfig config = JsonUtils.findObject(keyValueConfig.getValue(), CostumeBlindBoxConfig.class);
        return BizResult.success(CostumeBlindBoxConfigView.valueOf(config));
    }

    public BizResult<Void> updateCostumeBlindBoxConfig(CostumeBlindBoxConfigParam param) {
        final KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG);
        CostumeBlindBoxConfig config = new CostumeBlindBoxConfig().setOneStarPartProbability(param.getOneStarPartProbability())
                .setTwoStarPartProbability(param.getTwoStarPartProbability())
                .setThreeStarPartProbability(param.getThreeStarPartProbability())
                .setFourStarPartProbability(param.getFourStarPartProbability())
                .setFiveStarPartProbability(param.getFiveStarPartProbability())
                .setNotAcquiredCostumePartProbability(param.getNotAcquiredCostumePartProbability())
                .setGearConfigs(param.getGearConfigs().stream().map(CostumeBlindBoxConfig.GearConfig::valueOf).collect(Collectors.toList()))
                .setCornerMark(param.getCornerMark())
                .setRuleDescription(param.getRuleDescription());
        if (keyValueConfig == null) {
            keyValueConfigRepository.insert(
                    new KeyValueConfig().setKey(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG).setValue(JsonUtils.writeValueAsString(config)));
        } else {
            keyValueConfigRepository.updateByKey(
                    new KeyValueConfig().setKey(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG).setValue(JsonUtils.writeValueAsString(config)));
        }
        deleteKeyValueCache(KeyValueConfigKeys.COSTUME_BLIND_BOX_CONFIG);
        Operation operation = Operation.of(OperationConstants.OperateSubType.COSTUME_BLIND_BOX_CONFIG_UPDATE)
                .add("newData", JsonUtils.writeValueAsString(config));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    private void deleteCache(int costumeId) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(CacheConfig.COSTUME_INFO.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.COSTUME_INFO.getKeyPattern(), costumeId);
        clusterClientByName.del(cacheKey);
    }

    private void deleteKeyValueCache(String key) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.KEY_VALUE_CONFIG.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.KEY_VALUE_CONFIG.getKeyPattern(), key);
        redisClient.del(cacheKey);
    }

    //根据roleId获取它的角色组中另一个角色装扮id  name level
    public BizResult<List<SimpleCostumeView>> getAnotherRoleCostumeList(int roleId) {
        //获取当前角色的RoleGroupRelation
        RoleGroupRelation roleGroupRelation = roleGroupRelationRepository.queryByRoleId(roleId);
        if (roleGroupRelation == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        //获取角色组中另一个角色的id
        int anotherRoleId = roleRepository.queryByGroupIdAndRoleId(roleGroupRelation.getRoleGroupId(), roleId);
        //根据角色id获取角色装扮id
        List<Integer> costumeIds = roleCostumeRelationRepository.queryCostumeIdsByRoleId(anotherRoleId);
        if (costumeIds == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色装扮不存在");
        }
        //根据角色装扮id 获取装扮列表
        List<Costume> costumes = costumeRepository.queryByIds(costumeIds);

        List<SimpleCostumeView> simpleCostumeViews = costumes.stream().map(SimpleCostumeView::valueOf).collect(Collectors.toList());
        return BizResult.success(simpleCostumeViews);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addProbabilityRule(ProbabilityRuleParam param) {
        if (!param.isParamIllegal()) {
            log.error("addProbabilityRule error, param is illegal, param:{}", param);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "概率规则参数不合法");
        }
        String ruleName = param.getProbabilityRuleName();
        List<BlindBoxProbRuleConfig> existRuleConfigs = blindBoxProbRuleConfigRepository.queryByName(ruleName);
        if (CollectionUtils.isNotEmpty(existRuleConfigs)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), String.format("概率规则名称:%s已存在", ruleName));
        }
        log.info("addProbabilityRule ruleName:{}, ruleConfig:{}", ruleName, JsonUtils.writeValueAsString(param.toConfig()));
        BlindBoxProbRuleConfig blindBoxProbRuleConfig = new BlindBoxProbRuleConfig().setName(ruleName).setConfig(param.toConfig());
        blindBoxProbRuleConfigRepository.insert(blindBoxProbRuleConfig);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.BLIND_BOX_PROB_RULE_ADD)
                .add("ruleName", param.getProbabilityRuleName())
                .add("param", JsonUtils.writeValueAsString(param)));
        return BizResult.success();
    }

    public BizResult<List<SimpleCostumeView>> searchByName(String name) {
        List<Costume> costumes = StringUtils.isNotBlank(name) ? costumeRepository.queryByNameLike(name) : costumeRepository.queryAll();
        if (CollectionUtils.isEmpty(costumes)) {
            log.warn("searchByName costumes is empty, name:{}", name);
            return BizResult.success(Collections.emptyList());
        }
        List<Integer> costumeIds = costumes.stream().map(Costume::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(costumeIds)) {
            log.warn("searchByName costumes is not onShelf, name:{}", name);
            return BizResult.success(Collections.emptyList());
        }
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByCostumeIds(costumeIds);
        Map<Integer, Integer> costumeId2RoleIdMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, RoleCostumeRelation::getRoleId));
        Set<Integer> roleIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getRoleId).collect(Collectors.toSet());
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, role -> role));
        Set<Integer> groupIds = roleGroupRelationRepository.queryByRoleIds(roleIds).stream().map(RoleGroupRelation::getRoleGroupId).collect(Collectors.toSet());
        List<RoleGroupCostumeBlindBoxConfig.Config> allConfigs = roleGroupBlindBoxConfigDao.findByGroupIdList(groupIds)
                .stream()
                .map(RoleGroupCostumeBlindBoxConfig::getCostumeBlindBoxConfigs)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        Map<Integer, RoleGroupCostumeBlindBoxConfig.Config> costumeId2ConfigMap = allConfigs.stream()
                .collect(Collectors.toMap(RoleGroupCostumeBlindBoxConfig.Config::getCostumeId, config -> config));
        return BizResult.success(costumes.stream()
                .map(costume -> SimpleCostumeView.valueOf(costume, costumeId2RoleIdMap, roleMap, costumeId2ConfigMap))
                .collect(Collectors.toList()));
    }

    public BizResult<PageView<ProbabilityRuleView>> getProbabilityRuleList(int pageNum, int pageSize) {
        final int count = blindBoxProbRuleConfigRepository.count();
        if (count == 0) {
            return BizResult.success(PageView.empty());
        }
        final int offset = (pageNum - 1) * pageSize;
        final List<BlindBoxProbRuleConfig> configs = blindBoxProbRuleConfigRepository.queryByPage(offset, pageSize);
        if (CollectionUtils.isEmpty(configs)) {
            return BizResult.success(PageView.empty());
        }
        final List<ProbabilityRuleView> probabilityRuleViews = configs.stream().map(ProbabilityRuleView::valueOf).collect(Collectors.toList());
        return BizResult.success(PageView.form(count, probabilityRuleViews));
    }

    public void mockLotteryCostume(MockLotteryParam param, HttpServletResponse response) {
        if (!param.isParamIllegal()) {
            log.error("mockLotteryCostume error, param is illegal, param:{}", param);
            return;
        }

        RpcResult<BlindBoxDrawResultModel> result = userCostumeService.getBlindBoxLotteryMock(param);
        if (!result.isSuccess() || Objects.isNull(result.getData()) || CollectionUtils.isEmpty(result.getData().getDrawInfoModels())) {
            log.error("getBlindBoxLotteryMock result failed, result:{}", JsonUtils.writeValueAsString(result));
            return;
        }

        List<BlindBoxDrawResultModel.DrawInfoModel> drawInfoModels = result.getData().getDrawInfoModels();
        List<MockLotteryResultModel> resultList = Lists.newArrayListWithExpectedSize(drawInfoModels.size());
        Map<Integer, Costume> costumeMap = costumeRepository.queryAll().stream().collect(Collectors.toMap(Costume::getId, Function.identity()));
        Map<Integer, CostumePart> costumePartMap = costumePartRepository.selectAll()
                .stream()
                .collect(Collectors.toMap(CostumePart::getId, Function.identity()));
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByCostumeIds(new ArrayList<>(costumeMap.keySet()));
        Map<Integer, Integer> costumeId2RoleIdMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, RoleCostumeRelation::getRoleId));
        Set<Integer> roleIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getRoleId).collect(Collectors.toSet());
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));

        IntStream.range(0, drawInfoModels.size()).forEach(index -> {
            BlindBoxDrawResultModel.DrawInfoModel drawInfoModel = drawInfoModels.get(index);
            int currentLotteryCount = index + 1;
            resultList.add(MockLotteryResultModel.valueOf(drawInfoModel, costumeMap, costumePartMap, costumeId2RoleIdMap, roleMap, currentLotteryCount));
        });

        downloadExcel(response, resultList, param.getLotteryNum());
    }

    // download excel
    public void downloadExcel(HttpServletResponse response, List<MockLotteryResultModel> list, int lotteryCount) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        String fileName = String.format("MOCK_LOTTERY_%d_%s", lotteryCount, DateUtil.formatDD8(new Date()));
        List<MockLotteryCostumeExcelDTO> resultList = list.stream().map(MockLotteryCostumeExcelDTO::fromResultModel).collect(Collectors.toList());
        ExcelUtils.writeExcel(fileName, resultList, MockLotteryCostumeExcelDTO.class, response);
    }

    public BizResult<List<BasicCostumeView>> getBasicList(int roleId) {
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByRoleId(roleId);
        if (CollectionUtils.isEmpty(roleCostumeRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在对应装扮");
        }
        Set<Integer> costumeIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toSet());
        List<Costume> costumes = costumeRepository.queryByIds(costumeIds)
                .stream()
                .filter(x -> !Objects.equals(x.getFileType(), CostumeFileType.REPLACE.getCode()))
                .collect(Collectors.toList());
        List<BasicCostumeView> basicCostumeViews = costumes.stream().map(BasicCostumeView::valueOf).collect(Collectors.toList());
        return BizResult.success(basicCostumeViews);
    }

    public BizResult<List<String>> updateActionSmallMaterial(MultipartFile[] files) {

        if (files == null || files.length == 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件为空");
        }

        List<String> fileNamesNotFound = Lists.newArrayList();

        for (MultipartFile file : files) {
            String materialName = file.getOriginalFilename();
            Costume costume = costumeRepository.queryByMaterialName(materialName);
            if (Objects.isNull(costume)) {
                fileNamesNotFound.add(materialName);
                continue;
            }

            BizResult<SpineMaterialView> bizResult = materialBiz.uploadCostumeMaterial(file);
            if (!bizResult.isSuccess()) {
                fileNamesNotFound.add(materialName);
                continue;
            }


            SpineMaterialView spineMaterialView = bizResult.getData();
            SpineMaterial spineMaterial = new SpineMaterial();
            spineMaterial.setJsonFileKey(spineMaterialView.getJsonFileKey());
            spineMaterial.setAtlasFileKey(spineMaterialView.getAtlasFileKey());
            spineMaterial.setPngFileKey(spineMaterialView.getPngFileKey());
            spineMaterial.setName(spineMaterialView.getName());
            spineMaterial.setAnimations(spineMaterialView.getAnimations());

            Costume.Config config = costume.getConfig();
            config.setActionSpineSmallMaterial(spineMaterial);

            costumeRepository.updateByPrimaryKeySelective(costume);
            deleteCache(costume.getId());
        }

        return BizResult.success(fileNamesNotFound);
    }
}
