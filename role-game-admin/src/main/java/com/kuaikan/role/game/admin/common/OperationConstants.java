package com.kuaikan.role.game.admin.common;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
public class OperationConstants {

    public interface OperateSubType {

        int ROLE_ADD_OR_UPDATE = 898;
        int ROLE_PUBLISH = 898;
        int ROLE_OFFLINE = 900;
        int ROLE_WHITE_LIST = 963;
        int ROLE_SORT_UPDATE = 901;
        int ROLE_COSTUME_SORT_UPDATE = 902;
        int ROLE_DEFAULT_COSTUME = 903;
        int COSTUME_ADD_OR_UPDATE = 904;
        int COSTUME_PUBLISH = 905;
        int COSTUME_OFFLINE = 906;
        int SCENE_ADD_OR_UPDATE = 907;
        int SCENE_PUBLISH = 909;
        int SCENE_OFFLINE = 910;
        int ROLE_DEFAULT_SCENE = 911;

        int STORY_ADD = 912;

        int STORY_UPDATE = 913;

        int STORY_PUBLISH = 914;

        int STORY_OFFLINE = 915;

        int STORY_LETTER_UPSERT = 916;
        int ITEM_PUBLISH = 1012;
        int ITEM_OFFLINE = 1013;
        int ITEM_ADD = 921;
        int ITEM_UPDATE = 922;
        int ITEM_UPDATE_STATUS = 923;
        int ITEM_PROBABILITY_UPDATE = 924;
        int SILVER_COIN_CONFIG_UPDATE = 926;
        int ROLE_GROWTH_CONFIG_ADD = 917;
        int ROLE_GROWTH_CONFIG_UPDATE = 918;
        int STUFF_ADD = 927;
        int STUFF_UPDATE = 928;
        int COSTUME_PART_ADD = 929;
        int COSTUME_PART_UPDATE = 930;
        int COSTUME_PART_DECOMPOSE_RATE_UPDATE = 931;
        int COSTUME_BLIND_BOX_CONFIG_UPDATE = 932;

        int COSTUME_PART_DELETE = 933;

        int INSERT_OR_UPDATE_RESOURCE_CONFIG = 942;
        int CHANGE_RESOURCE_CONFIG_ORDER = 943;
        int CHANGE_PUBLISH_STATUS = 944;

        int AVG_CREATE_OR_UPDATE = 945;
        int AVG_FILE_ONLINE = 946;
        int AVG_FILE_OFFLINE = 947;
        int AVG_ORIGIN_FILE_CREATE = 948;
        int AVG_ORIGIN_FILE_DELETE = 949;
        int AVG_ORIGIN_FILE_UPDATE = 955;
        int ROLE_COSTUME_CONFIG_UPDATE = 950;
        int MAP_ELEMENT_ADD_OR_UPDATE = 951;
        int BUILDING_MAP_ADD_OR_UPDATE = 952;
        int MAP_ELEMENT_UPDATE_STATUS = 953;
        int BUILDING_MAP_UPDATE_STATUS = 954;

        int BUILDING_ADD_OR_UPDATE = 955;
        int BUILDING_DELETE = 956;
        int BUILDING_AREA_DELETE = 957;
        int BUILDING_ORDER_UPDATE = 958;

        int SCHEDULE_ADD_OR_UPDATE = 959;
        int SCHEDULE_DELETE = 960;

        int CITY_MAP_ADD_OR_UPDATE = 961;
        int CITY_MAP_UPDATE_STATUS = 962;
        int AVG_DIR_CREATE = 963;
        int AVG_DIR_UPDATE = 964;



        int WRITE_LIST_IDS = 964;
        int AVG_PROJECT_ADD = 965;
        int AVG_PROJECT_DELETE = 966;
        int AVG_PROJECT_UPDATE = 967;
        int AVG_CHAPTER_ADD = 968;
        int AVG_CHAPTER_UPDATE = 969;
        int AVG_CHAPTER_DELETE = 970;
        int AVG_FILE_UPDATE = 971;

        int ROLE_UNHEALTHY_CONFIG_UPDATE = 972;
        int ROLE_TRAP_CONFIG_UPDATE = 973;

        int NUMERICAL_CONFIG_UPDATE = 977;

        int FOOD_ADD_OR_UPDATE = 998;
        int FOOD_ORDER_UPDATE = 999;
        int REACTION_AFTER_FEEDING_UPDATE = 1000;

        int ROLE_GROUP_COSTUME_CONFIG_UPDATE = 1001;

        int COSTUME_ACTIVITY_ADD_OR_UPDATE = 1006;
        int COSTUME_ACTIVITY_PUBLISH = 1007;
        int COSTUME_ACTIVITY_OFFLINE = 1008;
        int ADD_COUPON_AND_SEND_MSG = 1009;

        int ONLINE_COUPON = 1010;
        int OFFLINE_COUPON = 1011;

        int TASK_MODULE_CONFIG = 1021;
        int ROLE_ADOPT_COUPON_ADD_OR_UPDATE = 1022;
        int BLIND_BOX_COUPON_ADD_OR_UPDATE = 1023;

        int BLIND_BOX_PROB_RULE_ADD = 1029;

        int ROLE_SPIRIT_STONE_INIT = 1030;
        int ROLE_SPIRIT_STONE_UPDATE = 1031;

        int ROLE_ADOPT_ANIMATION_UPDATE = 1032;

        int FURNITURE_COUPON_ADD = 1039;
        int FURNITURE_ACQUISITION_WAY_UPDATE = 1038;
        int CABIN_ADD_OR_UPDATE = 1040;
        int CABIN_STATUS_UPDATE = 1041;
        int FURNITURE_ADD_OR_UPDATE = 1042;
        int FURNITURE_STATUS_UPDATE = 1043;
        int FURNITURE_GROUP_ADD_OR_UPDATE = 1044;
        int FURNITURE_GROUP_STATUS_UPDATE = 1045;

        int COURTYARD_ADD_OR_UPDATE = 1050;
        int COURTYARD_STATUS_UPDATE = 1051;


        int ROLE_TRIAL_COUPON_ADD_OR_UPDATE = 1052;

        int FURNITURE_ACTIVITY_ADD_OR_UPDATE = 1056;
        int FURNITURE_ACTIVITY_PUBLISH = 1057;
        int FURNITURE_ACTIVITY_OFFLINE = 1058;

        int AVG_EDITOR_SAVE_TEXT = 1052;
        int AVG_EDITOR_ADD_TEXT = 1053;
        int AVG_EDITOR_BATCH_UPDATE = 1054;
    }
}
