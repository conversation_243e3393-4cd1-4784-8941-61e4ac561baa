package com.kuaikan.role.game.admin.battle.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardModelConfig;

/**
 * 卡牌战斗-卡牌模型配置
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
public interface CardModelConfigDao {

  List<CardBattleCardModelConfig> selectAll();

  void updateInvalidStatusByIds(@Param("list") List<Long> ids);

  int batchInsert(@Param("list") List<CardBattleCardModelConfig> records);
}
