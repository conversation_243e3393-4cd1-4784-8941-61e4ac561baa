package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import com.kuaikan.comic.bean.Topic;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.service.TopicService;
import com.kuaikan.game.common.util.PageUtil;
import com.kuaikan.role.game.admin.model.param.BattleMonsterAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.BattlePageQueryParam;
import com.kuaikan.role.game.admin.model.view.battle.BattleMonsterView;
import com.kuaikan.role.game.admin.model.view.battle.PageWrapper;
import com.kuaikan.role.game.admin.repository.BattleMonsterRepo;
import com.kuaikan.role.game.api.bean.cardbattle.BattleMonster;
import com.kuaikan.role.game.api.constant.CardBattleResponseCodeMsg;

/**
 *<AUTHOR>
 *@date 2024/8/29
 */
@Slf4j
@Service
public class BattleMonsterBiz {

    @Resource
    private BattleMonsterRepo battleMonsterRepo;

    @Resource
    private TopicService topicService;

    public BizResult<PageWrapper<BattleMonsterView>> listMonsters(BattlePageQueryParam query) {
        PageWrapper<BattleMonsterView> result;
        Integer status = query.getStatus();
        if (StringUtils.isNotBlank(query.getId())) {
            BattleMonster monster = battleMonsterRepo.getBattleMonsterById(query.getId());
            if (monster == null) {
                result = PageWrapper.of(0, Lists.newArrayList());
            } else if (status != null && monster.getStatus() != status) {
                result = PageWrapper.of(0, Lists.newArrayList());
            } else {
                BattleMonsterView monsterView = BattleMonsterView.convert(monster);
                int maxLevel = battleMonsterRepo.getMaxLevelByModelId(monster.getBattleModelId());
                monsterView.setMaxLevel(maxLevel);
                Topic topic = topicService.getTopicById((int) monsterView.getTopicId());
                monsterView.setTopicName(Optional.ofNullable(topic).map(Topic::getTitle).orElse(""));
                result = PageWrapper.of(1, Lists.newArrayList(monsterView));
            }
        } else {
            int page = query.getPage();
            int pageSize = query.getPageSize();
            int total = battleMonsterRepo.countBattleMonster(status);
            List<BattleMonster> monsters = battleMonsterRepo.listBattleMonster(pageSize, page, status);
            int totalPage = PageUtil.getTotalPage(total, pageSize);
            List<BattleMonsterView> battleMonsterViews = Lists.newArrayList();
            for (BattleMonster monster : monsters) {
                BattleMonsterView monsterView = BattleMonsterView.convert(monster);
                int maxLevel = battleMonsterRepo.getMaxLevelByModelId(monster.getBattleModelId());
                monsterView.setMaxLevel(maxLevel);
                Topic topic = topicService.getTopicById((int) monsterView.getTopicId());
                monsterView.setTopicName(Optional.ofNullable(topic).map(Topic::getTitle).orElse(""));
                battleMonsterViews.add(monsterView);
            }
            result = PageWrapper.of(totalPage, Lists.newArrayList(battleMonsterViews));
        }
        return BizResult.success(result);
    }

    public BizResult<Void> createMonster(BattleMonsterAddOrUpdateParam request) {
        battleMonsterRepo.createMonster(request.toBattleMonster());
        return BizResult.success();
    }

    public BizResult<Void> updateMonster(BattleMonsterAddOrUpdateParam request) {
        BattleMonster oldMonster = battleMonsterRepo.getBattleMonsterById(request.getId());
        if (oldMonster == null) {
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_MONSTER_NOT_EXIST);
        }

        battleMonsterRepo.updateMonster(request.toBattleMonster());
        return BizResult.success();
    }

    public BizResult<Void> updateMonsterStatus(String id, int status) {
        BattleMonster oldMonster = battleMonsterRepo.getBattleMonsterById(id);
        if (oldMonster == null) {
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_MONSTER_NOT_EXIST);
        }

        battleMonsterRepo.updateMonsterStatus(id, status);
        return BizResult.success();
    }
}
