package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.admin.component.QiniuComponent.FREE_BUCKET_NAME;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.model.bo.NumericalFileConfig;
import com.kuaikan.role.game.admin.model.view.NumericalConfigView;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.admin.template.numerical.NumericalTemplate;
import com.kuaikan.role.game.admin.template.numerical.NumericalTemplateFactory;
import com.kuaikan.role.game.admin.template.numerical.RoleTrapNumericalTemplate;
import com.kuaikan.role.game.admin.utils.FunctionUtils;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.common.RoleGameResponse;
import com.kuaikan.role.game.api.enums.NumericalConfigType;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;

/**
 * NumericalBiz
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Service
public class NumericalBiz {

    @Resource
    private NumericalTemplateFactory numericalTemplateFactory;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private QiniuComponent qiniuComponent;
    public static final String EXCEL_KEY = "role/game/excel/%d/%s";

    public BizResult<List<FailMessage>> importNumericalConfigs(List<MultipartFile> excelFiles) throws IOException {

        List<FailMessage> failMessages = Lists.newArrayList();
        for (MultipartFile excelFile : excelFiles) {
            NumericalConfigType numericalType = NumericalConfigType.getByFileName(excelFile.getOriginalFilename());
            if (numericalType == NumericalConfigType.UNKNOWN) {
                failMessages.add(new FailMessage().setNumericalConfigType("文件错误").setMessage("文件名错误：" + excelFile.getOriginalFilename()));
                continue;
            }
            // 找到对应模版类，若不存在则文件名有误
            NumericalTemplate template = numericalTemplateFactory.getInstance(numericalType);
            if (template == null) {
                failMessages.add(new FailMessage().setNumericalConfigType("文件错误").setMessage("文件名错误：" + excelFile.getOriginalFilename()));
                continue;
            }
            // 让模版对象处理对应的文件，并返回结果
            BizResult<List<FailMessage>> resultOfTemplate = template.importNumericalConfig(excelFile);
            // 如果对应code不为200，说明改文件内部配置有问题，则将原因放到结果里
            if (!resultOfTemplate.isSuccess()) {
                failMessages.addAll(resultOfTemplate.getData());
                continue;
            }
            saveKeyValue(excelFile, numericalType);
        }
        if (CollectionUtils.isNotEmpty(failMessages)) {
            return BizResult.result(failMessages, RoleGameResponse.BAD_REQUEST.getCode(), "导入文件数据有误");
        }
        return BizResult.success(failMessages);
    }

    private void saveKeyValue(MultipartFile excelFile, NumericalConfigType numericalType) throws IOException {
        String fileKey = String.format(EXCEL_KEY, BufferedIdGenerator.getId(), excelFile.getOriginalFilename());
        qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, excelFile.getBytes(), fileKey);
        String valueKey = String.format(KeyValueConfigKeys.NUMERICAL_CONFIG_PATTERN, numericalType.getCode());
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(valueKey);
        String operator = AuthContext.getCurrentUser().getName();
        NumericalFileConfig numericalFileConfig = new NumericalFileConfig().setKey(fileKey).setName(excelFile.getOriginalFilename()).setMd5("");
        Operation operation;
        if (keyValueConfig == null) {
            operation = Operation.of(OperationConstants.OperateSubType.NUMERICAL_CONFIG_UPDATE)
                    .add("newData", CdnUtil.getDefaultDomainWithBackSlash() + fileKey);
            keyValueConfig = new KeyValueConfig().setKey(valueKey).setValue(GsonUtils.toJson(numericalFileConfig)).setUpdater(operator);
            keyValueConfigRepository.insert(keyValueConfig);
        } else {
            String value = keyValueConfig.getValue();
            NumericalFileConfig old = GsonUtils.tryParseObject(value, NumericalFileConfig.class);
            operation = Operation.of(OperationConstants.OperateSubType.NUMERICAL_CONFIG_UPDATE)
                    .add("oldData", CdnUtil.getDefaultDomainWithBackSlash() + old.getKey())
                    .add("newData", CdnUtil.getDefaultDomainWithBackSlash() + fileKey);
            keyValueConfig.setValue(GsonUtils.toJson(numericalFileConfig));
            keyValueConfig.setUpdater(operator);
            keyValueConfigRepository.updateByKey(keyValueConfig);
        }
        OperateLogUtils.asyncRecord(operation);
    }

    public BizResult<List<NumericalConfigView>> getNumericalConfigList() {
        NumericalConfigType[] configTypes = NumericalConfigType.values();
        List<NumericalConfigType> configTypeList = Arrays.asList(configTypes)
                .stream()
                .filter(e -> e.getCode() != NumericalConfigType.UNKNOWN.getCode())
                .filter(NumericalConfigType::isNumerical)
                .collect(Collectors.toList());
        List<String> keys = configTypeList.stream()
                .map(e -> String.format(KeyValueConfigKeys.NUMERICAL_CONFIG_PATTERN, e.getCode()))
                .collect(Collectors.toList());
        List<KeyValueConfig> keyValueConfigs = keyValueConfigRepository.queryByKeys(keys);
        Map<String, KeyValueConfig> keyValueConfigMap = FunctionUtils.toMap(keyValueConfigs, KeyValueConfig::getKey);
        List<NumericalConfigView> views = Lists.newArrayList();
        for (NumericalConfigType configType : configTypeList) {
            if (configType.getCode() == NumericalConfigType.UNKNOWN.getCode()) {
                continue;
            }
            String key = String.format(KeyValueConfigKeys.NUMERICAL_CONFIG_PATTERN, configType.getCode());
            NumericalConfigView view = NumericalConfigView.valueOf(configType, keyValueConfigMap.get(key));
            views.add(view);
        }
        return BizResult.success(views);
    }

    public List<FailMessage> updateTrapConfig(File file, Integer roleId, List<String> imageNameList, List<File> imageList) throws IOException {
        NumericalConfigType numericalType = NumericalConfigType.ROLE_TRAP;
        NumericalTemplate template = numericalTemplateFactory.getInstance(numericalType);
        RoleTrapNumericalTemplate roleTrapNumericalTemplate = null;
        roleTrapNumericalTemplate = (RoleTrapNumericalTemplate) template;
        roleTrapNumericalTemplate.setImageNameList(imageNameList);
        roleTrapNumericalTemplate.setImageList(imageList);
        roleTrapNumericalTemplate.setRoleId(roleId);
        return roleTrapNumericalTemplate.importNumericalConfig(file);
    }
}
