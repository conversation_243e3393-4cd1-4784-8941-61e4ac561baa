package com.kuaikan.role.game.admin.battle.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardIdModelConfig;

/**
 * CardBattleCardIdModelConfigMapper
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
public interface CardIdModelConfigDao {

    List<CardBattleCardIdModelConfig> selectAll();

    int batchInsert(@Param("list") List<CardBattleCardIdModelConfig> record);

    void updateInvalidStatusByIds(@Param("list") List<Long> ids);

    CardBattleCardIdModelConfig getByCardId(@Param("cardId") String cardId);
}
