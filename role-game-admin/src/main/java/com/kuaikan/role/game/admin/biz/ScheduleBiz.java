package com.kuaikan.role.game.admin.biz;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.model.param.ScheduleAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.ScheduleView;
import com.kuaikan.role.game.admin.repository.BuildingAreaRepository;
import com.kuaikan.role.game.admin.repository.BuildingRepository;
import com.kuaikan.role.game.admin.repository.ScheduleRepository;
import com.kuaikan.role.game.admin.repository.UserRoleOngoingScheduleRepository;
import com.kuaikan.role.game.api.bean.Building;
import com.kuaikan.role.game.api.bean.BuildingArea;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.common.enums.CacheConfig;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Service
public class ScheduleBiz {

    @Resource
    private ScheduleRepository scheduleRepository;

    @Resource
    private BuildingRepository buildingRepository;

    @Resource
    private BuildingAreaRepository buildingAreaRepository;

    @Resource
    private UserRoleOngoingScheduleRepository userRoleOngoingScheduleRepository;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(ScheduleAddOrUpdateParam addOrUpdateParam) {
        int scheduleId = addOrUpdateParam.getId();
        Schedule schedule = scheduleRepository.queryByBuildingIdAndAreaId(addOrUpdateParam.getBuildingId(), addOrUpdateParam.getAreaId());
        if (schedule != null && scheduleId == 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "子场景下已经存在日程");
        }
        if (scheduleId > 0) {
            if (schedule == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "日程不存在");
            }
            Schedule.Config config = ScheduleAddOrUpdateParam.toConfig(addOrUpdateParam.getConfig());
            Schedule.Config dbConfig = schedule.getConfig();
            config.setConsumeMinute(dbConfig.getConsumeMinute())
                    .setConsumeEnergy(dbConfig.getConsumeEnergy())
                    .setSilverCoinChange(dbConfig.getSilverCoinChange())
                    .setDimensionExp(dbConfig.getDimensionExp())
                    .setConsumeEnergyPerPeriod(dbConfig.getConsumeEnergyPerPeriod())
                    .setSilverCoinChangePerPeriod(dbConfig.getSilverCoinChangePerPeriod())
                    .setDimensionExpPerPeriod(dbConfig.getDimensionExpPerPeriod());
            schedule.setName(addOrUpdateParam.getName()).setConfig(config).setType(addOrUpdateParam.getType());
            scheduleRepository.update(schedule);
        } else {
            Schedule record = new Schedule();
            record.setBuildingId(addOrUpdateParam.getBuildingId());
            record.setAreaId(addOrUpdateParam.getAreaId());
            Schedule.Config config = ScheduleAddOrUpdateParam.toConfig(addOrUpdateParam.getConfig());
            record.setName(addOrUpdateParam.getName()).setConfig(config).setType(addOrUpdateParam.getType());
            scheduleRepository.insert(record);
            scheduleId = record.getId();
            schedule = record;
        }
        deleteCache(schedule);
        Operation operation = Operation.of(OperationConstants.OperateSubType.SCHEDULE_ADD_OR_UPDATE)
                .add("id", scheduleId)
                .add("oldData", JsonUtils.toJson(schedule))
                .add("newData", JsonUtils.toJson(addOrUpdateParam));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<PageView<ScheduleView>> list(Integer buildingId, int pageNum, int pageSize) {
        int total = scheduleRepository.countByBuildingId(buildingId);
        List<Schedule> schedules = scheduleRepository.queryByPage(buildingId, (pageNum - 1) * pageSize, pageSize);
        Set<Integer> buildingIds = schedules.stream().map(Schedule::getBuildingId).collect(Collectors.toSet());
        Map<Integer, String> buildingNameMap = buildingRepository.queryByIds(buildingIds)
                .stream()
                .collect(Collectors.toMap(Building::getId, Building::getName));
        Map<Integer, String> areaNameMap = buildingAreaRepository.queryByBuildingIds(buildingIds)
                .stream()
                .collect(Collectors.toMap(BuildingArea::getId, BuildingArea::getName));
        List<ScheduleView> result = schedules.stream().map(item -> ScheduleView.valueOf(item, buildingNameMap, areaNameMap)).collect(Collectors.toList());
        return BizResult.success(PageView.form(total, result));
    }

    public BizResult<Void> delete(int id) {
        Schedule schedule = scheduleRepository.queryById(id);
        if (schedule == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "日程不存在");
        }
        int onGoingCount = userRoleOngoingScheduleRepository.countByScheduleIds(Collections.singleton(id));
        if (onGoingCount > 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "有正在进行的日程，无法删除");
        }
        scheduleRepository.deleteById(id);
        deleteCache(schedule);
        Operation operation = Operation.of(OperationConstants.OperateSubType.SCHEDULE_DELETE).add("id", id);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    private void deleteCache(Schedule schedule) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(CacheConfig.SCHEDULE_INFO.getReadWriteVip());
        String key1 = KeyGenerator.generate(CacheConfig.SCHEDULE_INFO.getKeyPattern(), schedule.getId());
        String key2 = CacheConfig.SCHEDULE_ALL.getKeyPattern();
        List<String> keys = new ArrayList<>();
        keys.add(key1);
        keys.add(key2);
        clusterClientByName.del(keys.toArray(new String[0]));
    }

}
