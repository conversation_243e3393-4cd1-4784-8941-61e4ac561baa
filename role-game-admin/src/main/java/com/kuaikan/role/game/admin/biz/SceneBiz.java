package com.kuaikan.role.game.admin.biz;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jsoup.internal.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.model.param.SceneAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.SpineMaterialParam;
import com.kuaikan.role.game.admin.model.view.RoleGroupView;
import com.kuaikan.role.game.admin.model.view.SceneView;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupSceneRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.repository.SceneRepository;
import com.kuaikan.role.game.admin.utils.FunctionUtils;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.bean.Scene;
import com.kuaikan.role.game.api.enums.SceneStatus;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.bean.RoleGroupSceneRelation;
import com.kuaikan.role.game.common.enums.CacheConfig;

/**
 *
 * <AUTHOR>
 * @date 2024/2/29
 */
@Service
@Slf4j
public class SceneBiz {

    @Resource
    private SceneRepository sceneRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private RoleGroupSceneRelationRepository roleGroupSceneRelationRepository;
    @Resource
    private RoleGroupRepository roleGroupRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private RoleGroupBiz roleGroupBiz;
    @Resource
    private QiniuComponent qiniuComponent;
    @Resource
    private MaterialBiz materialBiz;

    public static final String ZIP_KEY = "role/game/zip/scene/%d/%s.zip";

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(SceneAddOrUpdateParam addOrUpdateParam) {
        if (isParamIllegal(addOrUpdateParam)) {
            log.error("addOrUpdate param illegal, addOrUpdateParam:{}", addOrUpdateParam);
            return BizResult.result(RoleGameResponse.PARAM_ILLEGAL);
        }
        final String name = addOrUpdateParam.getName();
        Scene.Config config = new Scene.Config().setActivityId(addOrUpdateParam.getActivityId())
                .setObtainCopywriting(addOrUpdateParam.getObtainCopywriting())
                .setCornerMark(addOrUpdateParam.getCornerMark())
                .setThumbnail(addOrUpdateParam.getThumbnail())
                .setLargeImage(addOrUpdateParam.getLargeImage())
                .setRgba(addOrUpdateParam.getRgba())
                .setLargeImageSpineMaterial(SpineMaterialParam.toMaterialInfo(addOrUpdateParam.getLargeImageSpineMaterial()));
        final Integer id = addOrUpdateParam.getId();
        List<RoleGroup> roleGroups = roleGroupRepository.queryByIds(addOrUpdateParam.getRoleGroupIds());
        Map<Integer, RoleGroup> roleGroupMap = FunctionUtils.toMap(roleGroups, RoleGroup::getId);
        Operation operation;
        if (id != null && id > 0) {
            final Scene scene = sceneRepository.selectByPrimaryKey(id);
            if (scene == null) {
                return BizResult.result(RoleGameResponse.SCENE_NOT_EXIST);
            }
            operation = Operation.of(OperationConstants.OperateSubType.SCENE_ADD_OR_UPDATE).add("oldData", JsonUtils.writeValueAsString(scene));
            scene.setName(name).setConfig(config);
            // 设置家园类型
            if (ObjectUtil.isNotEmpty(addOrUpdateParam.getType())) {
                scene.setType(addOrUpdateParam.getType());
            }
            sceneRepository.updateByPrimaryKeySelective(scene);
            roleGroupSceneRelationRepository.deleteBySceneId(id);
            List<RoleGroupSceneRelation> roleGroupSceneRelations = addOrUpdateParam.getRoleGroupIds()
                    .stream()
                    .map(roleGroupId -> new RoleGroupSceneRelation().setSceneId(id).setRoleGroupId(roleGroupId))
                    .collect(Collectors.toList());
            roleGroupSceneRelationRepository.batchInsert(roleGroupSceneRelations);
            deleteCache(id);
            roleGroupSceneRelations.stream()
                    .filter(e -> roleGroupMap.get(e.getRoleGroupId()) != null && roleGroupMap.get(e.getRoleGroupId()).getDefaultSceneId() == 0)
                    .forEach(e -> roleGroupBiz.defaultSceneUncheck(e.getRoleGroupId(), e.getSceneId()));
            operation.add("newData", JsonUtils.writeValueAsString(scene));
            log.info("Update scene success, sceneId:{}", id);
        } else {
            Scene scene = new Scene().setName(name).setConfig(config).setStatus(SceneStatus.NOT_ON_SHELF.getCode());
            // 设置家园类型 0普通场景  1家园场景
            if (ObjectUtil.isNotEmpty(addOrUpdateParam.getType())) {
                scene.setType(addOrUpdateParam.getType());
            } else {
                scene.setType(0);
            }
            sceneRepository.insert(scene);
            final Integer newId = scene.getId();
            List<RoleGroupSceneRelation> roleGroupSceneRelations = addOrUpdateParam.getRoleGroupIds()
                    .stream()
                    .map(roleGroupId -> new RoleGroupSceneRelation().setSceneId(newId).setRoleGroupId(roleGroupId))
                    .collect(Collectors.toList());
            roleGroupSceneRelationRepository.batchInsert(roleGroupSceneRelations);
            roleGroupSceneRelations.stream()
                    .filter(e -> roleGroupMap.get(e.getRoleGroupId()) != null && roleGroupMap.get(e.getRoleGroupId()).getDefaultSceneId() == 0)
                    .forEach(e -> roleGroupBiz.defaultSceneUncheck(e.getRoleGroupId(), e.getSceneId()));
            operation = Operation.of(OperationConstants.OperateSubType.SCENE_ADD_OR_UPDATE).add("newData", JsonUtils.writeValueAsString(scene));
            log.info("Add scene success, sceneId:{}", newId);
        }

        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<SceneView> detail(int sceneId) {
        final Scene scene = sceneRepository.selectByPrimaryKey(sceneId);
        if (scene == null) {
            return BizResult.result(RoleGameResponse.SCENE_NOT_EXIST);
        }
        List<RoleGroupSceneRelation> roleGroupSceneRelations = roleGroupSceneRelationRepository.queryBySceneId(sceneId);
        List<Integer> roleGroupIds = roleGroupSceneRelations.stream().map(RoleGroupSceneRelation::getRoleGroupId).collect(Collectors.toList());
        List<RoleGroup> roleGroups = roleGroupRepository.queryByIds(roleGroupIds);
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIds(roleGroupIds);
        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        List<RoleGroupView> roleGroupViews = roleGroups.stream()
                .map(roleGroup -> RoleGroupView.valueOf(roleGroup, roleGroupRelations, roleMap))
                .collect(Collectors.toList());
        SceneView sceneView = SceneView.valueOf(scene, null, null).setRoleGroups(roleGroupViews);
        return BizResult.success(sceneView);
    }

    public BizResult<PageView<SceneView>> list(int pageNum, int pageSize) {
        final int offset = (pageNum - 1) * pageSize;
        final List<Scene> scenes = sceneRepository.queryByPage(offset, pageSize);
        if (CollectionUtils.isEmpty(scenes)) {
            return BizResult.success(PageView.empty());
        }
        final List<Integer> sceneIds = scenes.stream().map(Scene::getId).collect(Collectors.toList());
        final List<SceneView> sceneViews = getSceneViews(sceneIds, scenes, null, null).stream()
                .sorted(Comparator.comparing(SceneView::getCreateTime).reversed())
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(sceneRepository.count(), sceneViews));
    }

    @NotNull
    public List<SceneView> getSceneViews(List<Integer> sceneIds, List<Scene> scenes, Map<Integer, Integer> sceneOrderNumMap, RoleGroup roleGroup) {
        List<RoleGroupSceneRelation> roleGroupSceneRelations = roleGroupSceneRelationRepository.queryBySceneIds(sceneIds);
        Set<Integer> roleGroupIds = roleGroupSceneRelations.stream().map(RoleGroupSceneRelation::getRoleGroupId).collect(Collectors.toSet());
        Map<Integer, RoleGroup> roleGroupMap = roleGroupRepository.queryByIds(roleGroupIds)
                .stream()
                .collect(Collectors.toMap(RoleGroup::getId, Function.identity()));
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIds(roleGroupIds);
        Map<Integer, List<RoleGroupRelation>> roleGroupRelationMap = roleGroupRelations.stream()
                .collect(Collectors.groupingBy(RoleGroupRelation::getRoleGroupId));
        Set<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toSet());
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        Map<Integer, List<Integer>> scene2RoleGroupIds = roleGroupSceneRelations.stream()
                .collect(Collectors.groupingBy(RoleGroupSceneRelation::getSceneId,
                        Collectors.mapping(RoleGroupSceneRelation::getRoleGroupId, Collectors.toList())));
        return scenes.stream().map(scene -> {
            Integer orderNum = sceneOrderNumMap == null ? null : sceneOrderNumMap.get(scene.getId());
            SceneView sceneView = SceneView.valueOf(scene, orderNum, roleGroup == null ? null : roleGroup.getDefaultSceneId() == scene.getId());
            sceneView.setType(scene.getType());
            List<Integer> sceneRoleGroupIds = scene2RoleGroupIds.get(scene.getId());
            if (CollectionUtils.isNotEmpty(sceneRoleGroupIds)) {
                List<RoleGroupView> roleGroupViews = sceneRoleGroupIds.stream().map(groupId -> {
                    RoleGroup rg = roleGroupMap.get(groupId);
                    List<RoleGroupRelation> roleGroupRelation = roleGroupRelationMap.get(groupId);
                    return RoleGroupView.valueOf(rg, roleGroupRelation, roleMap);
                }).collect(Collectors.toList());
                sceneView.setRoleGroups(roleGroupViews);
            }
            return sceneView;
        }).collect(Collectors.toList());
    }

    public BizResult<Void> publish(int id) {
        Scene scene = sceneRepository.selectByPrimaryKey(id);
        if (scene == null) {
            return BizResult.result(RoleGameResponse.SCENE_NOT_EXIST);
        }
        scene.setStatus(SceneStatus.ON_SHELF.getCode());
        sceneRepository.updateByPrimaryKeySelective(scene);
        deleteCache(id);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.SCENE_PUBLISH).add("id", id));
        return BizResult.success();
    }

    public BizResult<Void> offline(int id) {
        Scene scene = sceneRepository.selectByPrimaryKey(id);
        if (scene == null) {
            return BizResult.result(RoleGameResponse.SCENE_NOT_EXIST);
        }
        final List<RoleGroup> roleGroups = roleGroupRepository.queryBySceneId(id);
        if (CollectionUtils.isNotEmpty(roleGroups)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色场景已被角色" + roleGroups + "使用");
        }
        scene.setStatus(SceneStatus.NOT_ON_SHELF.getCode());
        sceneRepository.updateByPrimaryKeySelective(scene);
        deleteCache(id);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.SCENE_OFFLINE).add("id", id));
        return BizResult.success();
    }

    private boolean isParamIllegal(SceneAddOrUpdateParam addOrUpdateParam) {
        if (addOrUpdateParam == null) {
            return true;
        }
        return StringUtil.isBlank(addOrUpdateParam.getName())
                || CollectionUtils.isEmpty(addOrUpdateParam.getRoleGroupIds())
                || StringUtil.isBlank(addOrUpdateParam.getObtainCopywriting())
                || addOrUpdateParam.getThumbnail() == null
                || addOrUpdateParam.getLargeImage() == null
                || addOrUpdateParam.getActivityId() == null;
    }

    private void deleteCache(int roleId) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.SCENE_INFO.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.SCENE_INFO.getKeyPattern(), roleId);
        redisClient.del(cacheKey);
    }

}
