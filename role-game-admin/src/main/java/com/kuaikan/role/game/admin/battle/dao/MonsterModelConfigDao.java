package com.kuaikan.role.game.admin.battle.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleMonsterModelConfig;

/**
 * 卡牌战斗-战斗对象模板配置
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
public interface MonsterModelConfigDao {

    List<CardBattleMonsterModelConfig> getAllConfigs();

    void updateInvalidStatusByIds(@Param("list") List<Long> ids);

    /**
     * 批量插入战斗对象模板配置
     *
     * @param list 战斗对象模板配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<CardBattleMonsterModelConfig> list);

    CardBattleMonsterModelConfig selectByModelId(int modelId);

}
