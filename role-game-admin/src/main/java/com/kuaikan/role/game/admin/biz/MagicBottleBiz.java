package com.kuaikan.role.game.admin.biz;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.model.param.EmotionBondBottleAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.EmotionBondBottleView;
import com.kuaikan.role.game.admin.repository.ItemRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.api.bean.Item;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.enums.ItemStatusEnum;
import com.kuaikan.role.game.api.enums.ItemType;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/11
 */
@Slf4j
@Service
public class MagicBottleBiz {

    @Resource
    private ItemRepository itemRepository;

    @Resource
    private RoleGroupRepository roleGroupRepository;

    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;

    @Resource
    private RoleRepository roleRepository;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(EmotionBondBottleAddOrUpdateParam param) {
        RoleGroup roleGroup = roleGroupRepository.queryById(param.getRoleGroupId());
        if (Objects.isNull(roleGroup)) {
            log.error("this roleGroup is not exist, groupId={}", param.getRoleGroupId());
            return BizResult.result(RoleGameResponse.PARAM_ILLEGAL.getCode(), "该角色组不存在");
        }

        Operation operation;
        Integer id = param.getId();
        if (Objects.nonNull(id) && id > 0) {
            Item oldItem = itemRepository.getById(id);
            if (Objects.isNull(oldItem)) {
                log.error("emotionBondBottle is not exist, id={}, name={}", id, param.getName());
                return BizResult.result(RoleGameResponse.ITEM_CONFIG_NOT_EXIST.getCode(), "羁绊魔药不存在");
            }
            Item item = itemRepository.getByGroupId(param.getRoleGroupId());
            if (item != null && oldItem.getId() != item.getId()) {
                log.error("emotionBondBottle of roleGroup is exist, groupId={}", param.getRoleGroupId());
                return BizResult.result(RoleGameResponse.ITEM_CONFIG_EXIST.getCode(), "该角色组的羁绊魔药已经存在");
            }
            operation = Operation.of(OperationConstants.OperateSubType.ITEM_UPDATE).add("oldData", JsonUtils.writeValueAsString(oldItem));
            Item.Config config = new Item.Config()
                    .setActivityId(param.getActivityId())
                    .setImage(param.getImage())
                    .setRoleGroupId(param.getRoleGroupId())
                    .setEmotionBondValue(param.getEmotionBondValue());
            oldItem.setName(param.getName()).setConfig(config);
            itemRepository.update(oldItem);
            operation.add("newData", JsonUtils.writeValueAsString(oldItem));
        } else {
            Item item = itemRepository.getByGroupId(param.getRoleGroupId());
            if (Objects.nonNull(item)) {
                log.error("this emotionBondBottle of roleGroup is exist, groupId={}", param.getRoleGroupId());
                return BizResult.result(RoleGameResponse.ITEM_CONFIG_EXIST.getCode(), "该角色组的羁绊魔药已经存在");
            }
            Item newItem = new Item();
            newItem.setName(param.getName());
            newItem.setType(ItemType.EMOTION_BOND_BOTTLE.getCode());
            newItem.setStatus(ItemStatusEnum.NOT_ONLINE.getCode());
            Item.Config config = new Item.Config()
                    .setImage(param.getImage())
                    .setEmotionBondValue(param.getEmotionBondValue())
                    .setRoleGroupId(param.getRoleGroupId())
                    .setActivityId(param.getActivityId());
            newItem.setConfig(config);
            itemRepository.insert(newItem);
            operation = Operation.of(OperationConstants.OperateSubType.ITEM_ADD).add("newData", JsonUtils.writeValueAsString(newItem));
        }
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<PageView<EmotionBondBottleView>> list(int pageNum, int pageSize) {
        int offset = (pageNum - 1) * pageSize;
        List<Item> items = itemRepository.queryByPage(offset, pageSize);
        if (CollectionUtils.isEmpty(items)) {
            return BizResult.success(PageView.empty());
        }
        List<Integer> groupIds = items.stream().map(Item::getConfig).map(Item.Config::getRoleGroupId).collect(Collectors.toList());
        Map<Integer, Role> roleMap = roleRepository.queryAll().stream().collect(Collectors.toMap(Role::getId, v -> v));
        Map<Integer, List<RoleGroupRelation>> groupRelationMap = roleGroupRelationRepository.queryByGroupIds(groupIds).stream()
                .collect(Collectors.groupingBy(RoleGroupRelation::getRoleGroupId));

        List<EmotionBondBottleView> itemViews = items.stream().map(v -> {
            Integer roleGroupId = v.getConfig().getRoleGroupId();
            List<RoleGroupRelation> relations = groupRelationMap.get(roleGroupId);
            List<String> roleName = new ArrayList<>();
            for (RoleGroupRelation relation : relations) {
                String name = roleMap.get(relation.getRoleId()).getName();
                roleName.add(name);
            }
            return EmotionBondBottleView.valueOf(v, String.join(",", roleName));
        }).collect(Collectors.toList());
        int total = itemRepository.count();
        return BizResult.success(PageView.form(total, itemViews));
    }

    public BizResult<Void> publish(Integer id) {
        Item oldItem = itemRepository.getById(id);
        if (Objects.isNull(oldItem)) {
            log.error("emotionBondBottle is not exist, id={}", id);
            return BizResult.result(RoleGameResponse.ITEM_CONFIG_NOT_EXIST.getCode(), "羁绊魔药不存在");
        }
        if (oldItem.getStatus() != ItemStatusEnum.NOT_ONLINE.getCode() && oldItem.getStatus() != ItemStatusEnum.OFFLINE.getCode()) {
            log.error("emotionBondBottle status not offline , id={}, name={}, status={}", id, oldItem.getName(), oldItem.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "羁绊魔药非已下架或未上架状态");
        }
        oldItem.setStatus(ItemStatusEnum.ONLINE.getCode());
        itemRepository.update(oldItem);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.ITEM_PUBLISH).add("id", id));
        return BizResult.success();
    }

    public BizResult<Void> offline(Integer id) {
        Item oldItem = itemRepository.getById(id);
        if (Objects.isNull(oldItem)) {
            log.error("emotion Bond Bottle is not exist, id={}", id);
            return BizResult.result(RoleGameResponse.ITEM_CONFIG_NOT_EXIST.getCode(), "羁绊魔药不存在");
        }
        if (oldItem.getStatus() != ItemStatusEnum.ONLINE.getCode()) {
            log.error("emotionBondBottle status not online, id={}, name={}, status={}", id, oldItem.getName(), oldItem.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "羁绊魔药非已上架状态");
        }
        oldItem.setStatus(ItemStatusEnum.OFFLINE.getCode());
        itemRepository.update(oldItem);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.ITEM_OFFLINE).add("id", id));
        return BizResult.success();
    }
}
