package com.kuaikan.role.game.admin.biz;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.model.param.SilverCoinConfigAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.SilverCoinProbabilityView;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.SilverCoinConfig;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;

/**
 * <AUTHOR>
 * @date 2024/4/19
 */
@Component
public class KeyValueConfigBiz {

    @Resource
    public KeyValueConfigRepository keyValueConfigRepository;

    public BizResult<SilverCoinProbabilityView> getSilverCoinConfig() {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.SILVER_COIN_PROBABILITY);
        if (keyValueConfig == null) {
            return BizResult.success();
        }
        SilverCoinConfig silverCoinConfig = JsonUtils.fromJson(keyValueConfig.getValue(), SilverCoinConfig.class);
        return BizResult.success(SilverCoinProbabilityView.valueOf(silverCoinConfig));
    }

    public BizResult<Void> updateSilverCoinConfig(SilverCoinConfigAddOrUpdateParam param) {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.SILVER_COIN_PROBABILITY);
        SilverCoinConfig silverCoinConfig = new SilverCoinConfig().setOneStarNotObtainedDroppedQuantity(param.getOneStarNotObtainedDroppedQuantity())
                .setTwoStarNotObtainedDroppedQuantity(param.getTwoStarNotObtainedDroppedQuantity())
                .setThreeStarNotObtainedDroppedQuantity(param.getThreeStarNotObtainedDroppedQuantity())
                .setObtainedDroppedQuantity(param.getObtainedDroppedQuantity())
                .setActivityId(param.getActivityId())
                .setUsageDesc(param.getUsageDesc());

        if (keyValueConfig == null) {
            keyValueConfig = new KeyValueConfig().setKey(KeyValueConfigKeys.SILVER_COIN_PROBABILITY).setValue(JsonUtils.toJson(silverCoinConfig));
            keyValueConfigRepository.insert(keyValueConfig);
        } else {
            keyValueConfig.setValue(JsonUtils.toJson(silverCoinConfig));
            keyValueConfigRepository.updateByKey(keyValueConfig);
        }
        deleteKeyValueCache(KeyValueConfigKeys.SILVER_COIN_PROBABILITY);
        Operation operation = Operation.of(OperationConstants.OperateSubType.SILVER_COIN_CONFIG_UPDATE)
                .add("newData", JsonUtils.writeValueAsString(silverCoinConfig));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    private void deleteKeyValueCache(String key) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.KEY_VALUE_CONFIG.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.KEY_VALUE_CONFIG.getKeyPattern(), key);
        redisClient.del(cacheKey);
    }

}
