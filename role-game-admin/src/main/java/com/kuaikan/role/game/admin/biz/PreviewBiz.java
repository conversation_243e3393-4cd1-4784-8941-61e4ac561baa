package com.kuaikan.role.game.admin.biz;

import com.google.common.collect.Lists;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.model.view.PreviewOptionView;
import com.kuaikan.role.game.admin.model.view.PreviewView;
import com.kuaikan.role.game.admin.repository.CostumeRepository;
import com.kuaikan.role.game.admin.repository.RoleCostumeRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.repository.StoryRepository;
import com.kuaikan.role.game.admin.repository.StoryRoleRelationRepository;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleCostumeRelation;
import com.kuaikan.role.game.api.bean.Story;
import com.kuaikan.role.game.api.bean.StoryActionConfig;
import com.kuaikan.role.game.api.bean.StoryRoleRelation;
import com.kuaikan.role.game.api.enums.AnimationConfigType;
import com.kuaikan.role.game.api.enums.PreviewTableType;
import com.kuaikan.role.game.api.enums.StoryType;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.testng.collections.Maps;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Slf4j
@Service
public class PreviewBiz {

    @Resource
    private RoleRepository roleRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private CostumeRepository costumeRepository;
    @Resource
    private StoryRepository storyRepository;
    @Resource
    private StoryRoleRelationRepository storyRoleRelationRepository;
    @Resource
    private RoleCostumeRelationRepository roleCostumeRelationRepository;


    public BizResult<PreviewOptionView> init() {
        log.info("preview option init start");
        PreviewOptionView optionView = new PreviewOptionView();
        // 角色组角色关联
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryAll();
        Set<Integer> roleGroupIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleGroupId).collect(Collectors.toSet());
        // key=groupId，value=该组的角色id
        Map<Integer, List<Integer>> roleGroupRelationMap = roleGroupRelations.stream()
                .collect(Collectors.groupingBy(RoleGroupRelation::getRoleGroupId, Collectors.mapping(RoleGroupRelation::getRoleId, Collectors.toList())));

        // 所有角色
        List<Role> roles = roleRepository.queryAll();
        List<Integer> roleIds = roles.stream().map(Role::getId).collect(Collectors.toList());
        Map<Integer, Role> roleMap = roles.stream().collect(Collectors.toMap(Role::getId, Function.identity()));

        // 角色的所有装扮
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByRoleIds(roleIds);
        // key=roleId,value=该角色下的装扮id
        Map<Integer, List<Integer>> roleCostumeRelationMap = roleCostumeRelations.stream()
                .collect(Collectors.groupingBy(RoleCostumeRelation::getRoleId, Collectors.mapping(RoleCostumeRelation::getCostumeId, Collectors.toList())));
        List<Integer> allCostumeIds = roleCostumeRelationMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<Costume> allCostumes = costumeRepository.queryByIds(allCostumeIds)
                .stream().filter(v -> v.getConfig().getActionSpineMaterial() != null).collect(Collectors.toList());
        // 单角色的装扮, key=角色id，value=该角色的装扮
        Map<Integer, List<Costume>> roleCostumeMap = new HashMap<>();
        for (Map.Entry<Integer, List<Integer>> entry : roleCostumeRelationMap.entrySet()) {
            Integer roleId = entry.getKey();
            List<Integer> costumeIds = entry.getValue();
            List<Costume> costumes = allCostumes.stream()
                    .filter(costume -> costumeIds.contains(costume.getId()))
                    .collect(Collectors.toList());
            roleCostumeMap.put(roleId, costumes);
        }

        // 角色动作剧情
        List<StoryRoleRelation> storyRoleRelations = storyRoleRelationRepository.selectByRoleIds(roleIds);
        // key=roleId，value=该角色的动作剧情
        Map<Integer, List<Integer>> storyRoleRelationMap = storyRoleRelations
                .stream().collect(Collectors.groupingBy(StoryRoleRelation::getRoleId, Collectors.mapping(StoryRoleRelation::getStoryId, Collectors.toList())));
        List<Integer> allStoryIds = storyRoleRelationMap.values().stream()
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        List<Story> allStorys = new ArrayList<>(storyRepository.queryStoriesByIds(allStoryIds).values());
        Map<Integer, List<Story>> roleStoryMap = new HashMap<>();
        for (Map.Entry<Integer, List<Integer>> entry : storyRoleRelationMap.entrySet()) {
            Integer roleId = entry.getKey();
            List<Integer> storyIds = entry.getValue();
            List<Story> stories = allStorys.stream()
                    .filter(story -> storyIds.contains(story.getId()))
                    .collect(Collectors.toList());
            roleStoryMap.put(roleId, stories);
        }

        optionView.setAnimationOption(PreviewOptionView.AnimationOptionView.valueOf(roles, roleCostumeMap));
        optionView.setActionStoryOption(PreviewOptionView.ActionStoryOptionView.valueOf(roleGroupRelationMap, roleMap, roleStoryMap, roleCostumeMap));
        optionView.setEggStoryOption(PreviewOptionView.EggStoryOptionView.valueOf(roleGroupRelationMap, roleMap, roleStoryMap, roleCostumeMap));
        optionView.setDailyStoryOption(PreviewOptionView.DailyStoryOptionView.valueOf(roleGroupRelationMap, roleMap, roleStoryMap, roleCostumeMap));
        return BizResult.success(optionView);
    }

    public BizResult<PreviewView> costumeAnimation(int roleId, List<Integer> costumeIds, List<String> costumeSpineNames) {
        if (roleId <= 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不能为空");
        }
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            log.info("costumeAnimation fail, role is null , roleId={}", roleId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色为空");
        }
        Map<Integer, Role> roleMap = new HashMap<>();
        roleMap.put(role.getId(), role);

        // 角色装扮
        List<Costume> costumes = costumeRepository.queryByIds(costumeIds).stream().filter(v -> v.getConfig().getActionSpineMaterial() != null).collect(Collectors.toList());
        Map<Integer, List<Costume>> roleCostumeMap = Maps.newHashMap();
        roleCostumeMap.put(roleId, costumes);

        costumeSpineNames = costumeSpineNames.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        PreviewView previewView = new PreviewView();
        previewView.setDisplay(PreviewTableType.ANIMATION.getCode());
        previewView.setDress(PreviewView.CostumeView.valueOf(roleCostumeMap, Lists.newArrayList(roleId), roleMap));
        previewView.setAction(PreviewView.ActionView.valueOf(costumeSpineNames, null));
        return BizResult.success(previewView);
    }

    public BizResult<PreviewView> actionStory(int firstRoleId, int secondRoleId, List<Integer> firstCostumeIds, List<Integer> secondCostumeIds, List<Integer> storyIds) {
        if (firstRoleId <= 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色1不能为空");
        }
        List<Integer> roleIds = Arrays.asList(firstRoleId, secondRoleId);
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        if (roleMap.isEmpty()) {
            log.info("actionStory preview fail, roles is null , firstRoleId={} ,secondRoleId={} ", firstRoleId, secondRoleId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色为空");
        }

        Map<Integer, List<Integer>> roleToCostumeIdsMap = getCostumeIdsMap(firstRoleId, secondRoleId, firstCostumeIds, secondCostumeIds);
        Map<Integer, List<Costume>> roleCostumeMap = getRoleCostumeMap(roleToCostumeIdsMap);

        // 动作剧情
        List<Story> stories = getStories(storyIds, StoryType.ACTION);
        List<PreviewView.AnimationConfigView> animationConfigs = getAnimationConfigViews(roleMap, stories);

        PreviewView previewView = new PreviewView();
        previewView.setDisplay(PreviewTableType.ACTION_STORY.getCode());
        previewView.setDress(PreviewView.CostumeView.valueOf(roleCostumeMap, roleIds, roleMap));
        previewView.setAction(PreviewView.ActionView.valueOf(null, animationConfigs));
        return BizResult.success(previewView);
    }

    public BizResult<PreviewView> eggStory(int firstRoleId, int secondRoleId, List<Integer> firstCostumeIds, List<Integer> secondCostumeIds, List<Integer> storyIds) {
        if (firstRoleId <= 0 || secondRoleId <= 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不能为空");
        }
        List<Integer> roleIds = Arrays.asList(firstRoleId, secondRoleId);
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        if (roleMap.size() < 2) {
            log.info("eggStory preview fail, roles is null , firstRoleId={} ,secondRoleId={} ", firstRoleId, secondRoleId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色为空");
        }

        // 角色装扮
        Map<Integer, List<Integer>> roleToCostumeIdsMap = getCostumeIdsMap(firstRoleId, secondRoleId, firstCostumeIds, secondCostumeIds);
        Map<Integer, List<Costume>> roleCostumeMap = getRoleCostumeMap(roleToCostumeIdsMap);

        List<Story> stories = getStories(storyIds, StoryType.EGG);
        List<PreviewView.AnimationConfigView> animationConfigs = getAnimationConfigViews(roleMap, stories);

        PreviewView previewView = new PreviewView();
        previewView.setDisplay(PreviewTableType.EGG_STORY.getCode());
        previewView.setDress(PreviewView.CostumeView.valueOf(roleCostumeMap, roleIds, roleMap));
        previewView.setAction(PreviewView.ActionView.valueOf( null, animationConfigs));
        return BizResult.success(previewView);
    }

    public BizResult<PreviewView> dailyStory(int firstRoleId, int secondRoleId, List<Integer> firstCostumeIds, List<Integer> secondCostumeIds, List<Integer> storyIds) {
        if (firstRoleId <= 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色1不能为空");
        }
        List<Integer> roleIds = Arrays.asList(firstRoleId, secondRoleId);
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        if (roleMap.isEmpty()) {
            log.info("actionStory preview fail, roles is null , firstRoleId={} ,secondRoleId={} ", firstRoleId, secondRoleId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色为空");
        }

        Map<Integer, List<Integer>> roleToCostumeIdsMap = getCostumeIdsMap(firstRoleId, secondRoleId, firstCostumeIds, secondCostumeIds);
        // 不同角色对应的装扮
        Map<Integer, List<Costume>> roleCostumeMap = getRoleCostumeMap(roleToCostumeIdsMap);

        // 动作剧情
        List<Story> stories = getStories(storyIds, StoryType.DAILY);
        List<PreviewView.AnimationConfigView> animationConfigs = getAnimationConfigViews(roleMap, stories);

        PreviewView previewView = new PreviewView();
        previewView.setDisplay(PreviewTableType.DAILY_STORY.getCode());
        previewView.setDress(PreviewView.CostumeView.valueOf(roleCostumeMap, roleIds, roleMap));
        previewView.setAction(PreviewView.ActionView.valueOf(null, animationConfigs));
        return BizResult.success(previewView);
    }

    private static @NotNull List<PreviewView.AnimationConfigView> getAnimationConfigViews(Map<Integer, Role> roleMap, List<Story> stories) {
        List<PreviewView.AnimationConfigView> animationConfigs;
        int animationConfigType = roleMap.size() >= 2 ? AnimationConfigType.DOUBLE.getCode() :AnimationConfigType.SINGLE.getCode();
        animationConfigs = stories.stream()
                .filter(story -> {
                    StoryActionConfig storyActionConfig = JsonUtils.fromJson(story.getConfig(), StoryActionConfig.class);
                    if (storyActionConfig.getAnimationConfig() == null) {
                        return false;
                    }
                    int type = storyActionConfig.getAnimationConfig().getType();
                    return type == animationConfigType;
                })
                .map(story -> {
                    StoryActionConfig storyActionConfig = JsonUtils.fromJson(story.getConfig(), StoryActionConfig.class);
                    String name = story.getId() + "-" + story.getName();
                    return PreviewView.AnimationConfigView.valueOf(storyActionConfig.getAnimationConfig(), name);
                }).collect(Collectors.toList());
        return animationConfigs;
    }

    private @NotNull Map<Integer, List<Costume>> getRoleCostumeMap(Map<Integer, List<Integer>> roleToCostumeIdsMap) {
        // 不同角色对应的装扮
        List<Integer> allCostumeIds = roleToCostumeIdsMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<Costume> allCostumes = costumeRepository.queryByIds(allCostumeIds).stream().filter(v -> v.getConfig().getActionSpineMaterial() != null).collect(Collectors.toList());
        Map<Integer, List<Costume>> roleCostumeMap = roleToCostumeIdsMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> allCostumes.stream()
                        .filter(costume -> entry.getValue().contains(costume.getId()))
                        .collect(Collectors.toList())));
        return roleCostumeMap;
    }

    private @NotNull List<Story> getStories(List<Integer> storyIds, StoryType action) {
        List<Story> stories = storyRepository.queryStoriesByIds(storyIds).values().stream()
                .filter(v -> action.getCode().equals(v.getType()))
                .sorted(Comparator.comparing(Story::getId).reversed())
                .collect(Collectors.toList());
        return stories;
    }

    private static @NotNull Map<Integer, List<Integer>> getCostumeIdsMap(int firstRoleId, int secondRoleId, List<Integer> firstCostumeIds, List<Integer> secondCostumeIds) {
        Map<Integer, List<Integer>> roleToCostumeIdsMap = new HashMap<>();
        roleToCostumeIdsMap.put(firstRoleId, firstCostumeIds);
        if (secondRoleId > 0) {
            roleToCostumeIdsMap.put(secondRoleId, secondCostumeIds);
        }
        return roleToCostumeIdsMap;
    }
}
