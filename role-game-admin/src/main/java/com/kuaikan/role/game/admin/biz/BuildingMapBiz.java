package com.kuaikan.role.game.admin.biz;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.converter.BuildingMapConverter;
import com.kuaikan.role.game.admin.model.param.BuildingMapAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.UpdateStatusParam;
import com.kuaikan.role.game.admin.model.view.BuildingMapDetailView;
import com.kuaikan.role.game.admin.model.view.BuildingMapView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.repository.BuildingAreaRepository;
import com.kuaikan.role.game.admin.repository.BuildingMapRepository;
import com.kuaikan.role.game.admin.repository.MapElementRepository;
import com.kuaikan.role.game.admin.repository.ScheduleRepository;
import com.kuaikan.role.game.admin.utils.FunctionUtils;
import com.kuaikan.role.game.api.bean.BuildingArea;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.common.bean.BuildingAreaMap;
import com.kuaikan.role.game.common.bean.BuildingMap;
import com.kuaikan.role.game.common.bean.MapElement;
import com.kuaikan.role.game.common.bean.MapElementConfig;
import com.kuaikan.role.game.common.enums.MapElementType;

/**
 * 内景地图业务逻辑.
 * <AUTHOR>
 */
@Service
public class BuildingMapBiz {

    @Resource
    private BuildingMapRepository buildingMapRepository;

    @Resource
    private BuildingMapConverter buildingMapConverter;

    @Resource
    private MapElementRepository mapElementRepository;

    @Resource
    private BuildingAreaRepository buildingAreaRepository;

    @Resource
    private ScheduleRepository scheduleRepository;

    public BizResult<PageResult<BuildingMapView>> selectBuildingMapList(String name, String creator, Integer statusId, Integer pageNum, Integer pageSize) {
        CommonStatus status = null;
        if (statusId != null) {
            status = CommonStatus.getByCode(statusId);
            if (status == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "状态不存在");
            }
        }
        PageInfo<BuildingMap> pageInfo = buildingMapRepository.selectSimpleBuildingMapList(name, creator, status, pageNum, pageSize);
        PageResult<BuildingMapView> pageResult = PageResult.from(pageInfo, buildingMapConverter::toBuildingMapView);
        return BizResult.success(pageResult);
    }

    public BizResult updateStatus(UpdateStatusParam param, String userName) {
        Optional<BuildingMap> buildingMapOpt = buildingMapRepository.selectSimpleBuildingMapById(param.getId());
        if (!buildingMapOpt.isPresent()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "内景地图ID不存在");
        }
        checkUpdateStatus(buildingMapOpt.get(), param);
        CommonStatus status = CommonStatus.getByCode(param.getStatus());
        if (status == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "内景地图状态不存在");
        }
        buildingMapRepository.updateStatus(param.getId(), status, userName);
        Operation operation = Operation.of(OperationConstants.OperateSubType.BUILDING_MAP_UPDATE_STATUS)
                .add("id", String.valueOf(param.getId()))
                .add("newStatus", param.getStatus());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<BuildingMapDetailView> getBuildingMapDetailViewById(Integer id) {
        BuildingMap buildingMap = buildingMapRepository.selectBuildingMapDetailById(id);
        if (buildingMap == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "内景地图ID不存在");
        }
        BuildingMapDetailView buildingMapDetailView = buildingMapConverter.toBuildingMapDetailView(buildingMap);
        return BizResult.success(buildingMapDetailView);
    }

    public BizResult addOrUpdateBuildingMap(BuildingMapAddOrUpdateParam param, String userName) {
        checkParam(param);
        BuildingMap buildingMap = buildingMapConverter.toBuildingMap(param);
        List<BuildingAreaMap> buildingAreaMapList = buildingMapConverter.toBuildingAreaMapList(param.getBuildingAreaMapParamList());
        BuildingMap.Config config = calBuildingMapConfig(buildingAreaMapList);
        buildingMap.setConfig(config);
        buildingMap.setBuildingAreaMapList(buildingAreaMapList);
        if (buildingMap.getId() == null) {
            buildingMapRepository.insertBuildingMap(buildingMap, userName);
            return BizResult.success();
        } else {
            Optional<BuildingMap> oldBuildingMap = buildingMapRepository.selectSimpleBuildingMapById(buildingMap.getId());
            if (!oldBuildingMap.isPresent()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "内景地图ID不存在");
            }
            buildingMapRepository.updateBuildingMap(buildingMap, userName);
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.BUILDING_MAP_ADD_OR_UPDATE).add("newData", JsonUtils.writeValueAsString(param));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    private BuildingMap.Config calBuildingMapConfig(List<BuildingAreaMap> buildingAreaMapList) {

        List<Integer> elementIdList = buildingAreaMapList.stream()
                .flatMap(buildingAreaMap -> buildingAreaMap.getConfig().getMapElementConfigList().stream())
                .map(MapElementConfig::getElementId)
                .collect(Collectors.toList());
        BuildingMap.Config config = new BuildingMap.Config();
        config.setImageInfo(buildingAreaMapList.get(0).getConfig().getImageInfo());
        if (CollectionUtils.isNotEmpty(elementIdList)) {
            List<Integer> distinctElementIdList = elementIdList.stream().distinct().collect(Collectors.toList());
            List<MapElement> mapElementList = mapElementRepository.selectMapElementByIds(distinctElementIdList);
            Map<Integer, MapElementType> elementId2TypeMap = FunctionUtils.toMap(mapElementList, MapElement::getId, MapElement::getElementType);
            Map<MapElementType, Integer> elementTypeCountMap = elementIdList.stream()
                    .map(elementId2TypeMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.summingInt(e -> 1)));
            config.setNpcNum(elementTypeCountMap.getOrDefault(MapElementType.NPC, 0));
            config.setDecorationNum(elementTypeCountMap.getOrDefault(MapElementType.DECORATION, 0));
        } else {
            config.setNpcNum(0);
            config.setDecorationNum(0);
        }
        return config;
    }

    private void checkParam(BuildingMapAddOrUpdateParam param) {
        if (param.getName() == null) {
            throw new IllegalArgumentException("内景地图名称不能为空");
        }
        if (param.getId() != null) {
            Optional<BuildingMap> buildingMapOpt = buildingMapRepository.selectSimpleBuildingMapById(param.getId());
            if (!buildingMapOpt.isPresent()) {
                throw new IllegalArgumentException("内景地图ID不存在");
            }
        }
        List<BuildingArea> buildingAreas = buildingAreaRepository.queryByBuildingId(param.getBuildingId());
        if (CollectionUtils.isEmpty(buildingAreas)) {
            throw new IllegalArgumentException("内景地图区域不存在");
        }
        if (param.getBuildingAreaMapParamList() == null) {
            throw new IllegalArgumentException("内景地图区域信息不能为空");
        }
        if (param.getBuildingAreaMapParamList().size() != buildingAreas.size()) {
            throw new IllegalArgumentException("内景地图区域数量不匹配");
        }
        List<Integer> buildingAreaIds = buildingAreas.stream().map(BuildingArea::getId).collect(Collectors.toList());
        List<Schedule> schedules = scheduleRepository.queryByAreaIds(buildingAreaIds);
        if (CollectionUtils.isEmpty(schedules) || schedules.size() != buildingAreas.size()) {
            throw new IllegalArgumentException("此建筑未配置日程");
        }
        Map<Integer, Schedule> areaId2ScheduleMap = FunctionUtils.toMap(schedules, Schedule::getAreaId);
        Map<Integer, BuildingArea> id2BuildingAreaMap = FunctionUtils.toMap(buildingAreas, BuildingArea::getId);
        param.getBuildingAreaMapParamList().forEach(buildingAreaMapParam -> {
            if (!id2BuildingAreaMap.containsKey(buildingAreaMapParam.getBuildingAreaId())) {
                throw new IllegalArgumentException("内景地图区域ID不存在:" + buildingAreaMapParam.getBuildingAreaId());
            }
            BuildingArea buildingArea = id2BuildingAreaMap.get(buildingAreaMapParam.getBuildingAreaId());
            String name = buildingArea.getName();
            if (buildingAreaMapParam.getImageInfo() == null) {
                throw new IllegalArgumentException("内景地图区域图片信息不能为空:" + name);
            }
            if (buildingAreaMapParam.getInitialArea() == null) {
                throw new IllegalArgumentException("内景地图区域初始区域信息不能为空:" + name);
            }
            if (buildingAreaMapParam.getRoleScheduleConfigList() == null) {
                throw new IllegalArgumentException("内景地图区域角色日程配置信息不能为空:" + name);
            }
            Schedule schedule = areaId2ScheduleMap.get(buildingAreaMapParam.getBuildingAreaId());
            if (schedule == null) {
                throw new IllegalArgumentException("此建筑未配置日程");
            }

            for (BuildingAreaMap.RoleScheduleConfig roleScheduleConfig : buildingAreaMapParam.getRoleScheduleConfigList()) {
                if (schedule.getConfig().getNpcCount() > 0) {
                    if (roleScheduleConfig.getNpcPositionList() == null || roleScheduleConfig.getNpcPositionList().size() != schedule.getConfig()
                            .getNpcCount()) {
                        throw new IllegalArgumentException("内景地图区域NPC位置数目不正确:" + name);
                    }
                }

                if (roleScheduleConfig.getScheduleActionPosition() == null) {
                    throw new IllegalArgumentException("内景地图区域角色日程动作位置信息不能为空:" + name);
                }
                if (roleScheduleConfig.getScheduleTriggerAreaList() == null) {
                    throw new IllegalArgumentException("内景地图区域角色日程触发区域信息不能为空:" + name);
                }
            }
            List<MapElementConfig> mapElementConfigList = buildingAreaMapParam.getMapElementConfigList();
            List<Integer> elementIds = Lists.transform(mapElementConfigList, MapElementConfig::getElementId);
            Map<Integer, MapElement> elementId2elementMap = FunctionUtils.toMap(mapElementRepository.selectMapElementByIds(elementIds), MapElement::getId);
            int npcNum = 0;
            for (MapElementConfig mapElementConfig : mapElementConfigList) {
                if (!elementId2elementMap.containsKey(mapElementConfig.getElementId())) {
                    throw new IllegalArgumentException("内景地图区域元素ID不存在:" + mapElementConfig.getElementId());
                }
                MapElement element = elementId2elementMap.get(mapElementConfig.getElementId());
                if (element.getElementType() == MapElementType.NPC) {
                    npcNum++;
                }
            }
            int needNpcCnt = schedule.getConfig().getNpcCount() * 2;
            if (npcNum < needNpcCnt) {
                throw new IllegalArgumentException(String.format("日程【%s】同时2个角色进行时需征用【%d】个NPC，请补充配置NPC", schedule.getName(), needNpcCnt));
            }
        });

    }

    private void checkUpdateStatus(BuildingMap buildingMap, UpdateStatusParam param) {
        if (buildingMap.getStatus() == CommonStatus.ONLINE && param.getStatus() == CommonStatus.ONLINE.getCode()) {
            throw new IllegalArgumentException("内景地图已上线");
        }
        if (buildingMap.getStatus() == CommonStatus.NOT_ONLINE && param.getStatus() == CommonStatus.NOT_ONLINE.getCode()) {
            throw new IllegalArgumentException("内景地图已下线");
        }
        if (param.getStatus() == CommonStatus.NOT_ONLINE.getCode()) {
            List<MapElement> onlineBuildingList = mapElementRepository.selectList(null, null, null, CommonStatus.ONLINE,
                    Lists.newArrayList(MapElementType.COMMON_BUILDING.getCode()), 1, Integer.MAX_VALUE).getList();
            onlineBuildingList.forEach(mapElement -> {
                if (mapElement.getConfig().getRelatedBuildingInstanceId() != null && mapElement.getConfig()
                        .getRelatedBuildingInstanceId()
                        .equals(buildingMap.getId())) {
                    throw new IllegalArgumentException(String.format("内景地图已被建筑%d关联，不能下线", mapElement.getId()));
                }
            });
        }
    }

}
