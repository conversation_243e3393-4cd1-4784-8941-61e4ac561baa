package com.kuaikan.role.game.admin.biz;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.dao.mongo.RoleGroupBlindBoxConfigDao;
import com.kuaikan.role.game.admin.model.param.GroupBlindBoxProbabilityParam;
import com.kuaikan.role.game.admin.model.param.InteractiveActionAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.InteractiveItemAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.RoleGroupAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.RoleGroupCostumeConfigParam;
import com.kuaikan.role.game.admin.model.view.BlindBoxProbabilityView;
import com.kuaikan.role.game.admin.model.view.InteractiveActionView;
import com.kuaikan.role.game.admin.model.view.InteractiveItemView;
import com.kuaikan.role.game.admin.model.view.RoleCostumeConfigView;
import com.kuaikan.role.game.admin.model.view.RoleGroupView;
import com.kuaikan.role.game.admin.model.view.SceneView;
import com.kuaikan.role.game.admin.model.view.SimpleRoleView;
import com.kuaikan.role.game.admin.repository.BlindBoxProbRuleConfigRepository;
import com.kuaikan.role.game.admin.repository.CabinRepository;
import com.kuaikan.role.game.admin.repository.CabinRoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.CostumeRepository;
import com.kuaikan.role.game.admin.repository.FurnitureGroupRepository;
import com.kuaikan.role.game.admin.repository.FurnitureGroupRoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.InteractiveActionRepository;
import com.kuaikan.role.game.admin.repository.InteractiveItemRepository;
import com.kuaikan.role.game.admin.repository.InteractiveItemStoryRelationRepository;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleCostumeRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupInteractiveActionRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupInteractiveItemRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupSceneRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.repository.SceneRepository;
import com.kuaikan.role.game.admin.utils.FunctionUtils;
import com.kuaikan.role.game.api.bean.BlindBoxProbRuleConfig;
import com.kuaikan.role.game.api.bean.Cabin;
import com.kuaikan.role.game.api.bean.CabinRoleGroupRelation;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.FurnitureGroup;
import com.kuaikan.role.game.api.bean.FurnitureGroupRoleGroupRelation;
import com.kuaikan.role.game.api.bean.InteractiveAction;
import com.kuaikan.role.game.api.bean.InteractiveItem;
import com.kuaikan.role.game.api.bean.InteractiveItemStoryRelation;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleCostumeRelation;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.bean.RoleGroupCostumeBlindBoxConfig;
import com.kuaikan.role.game.api.bean.Scene;
import com.kuaikan.role.game.api.bo.RoleCommonPropertyConfig;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.api.enums.CostumeStatus;
import com.kuaikan.role.game.api.enums.InteractiveActionUnlockTypeEnum;
import com.kuaikan.role.game.api.enums.SceneStatus;
import com.kuaikan.role.game.common.bean.RoleGroupInteractiveActionRelation;
import com.kuaikan.role.game.common.bean.RoleGroupInteractiveItemRelation;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.bean.RoleGroupSceneRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;

/**
 * RoleGroupBiz
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@Service
@Slf4j
public class RoleGroupBiz {

    @Resource
    private RoleGroupRepository roleGroupRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private RoleGroupSceneRelationRepository roleGroupSceneRelationRepository;
    @Resource
    private SceneRepository sceneRepository;
    @Resource
    private SceneBiz sceneBiz;
    @Resource
    private InteractiveActionRepository interactiveActionRepository;
    @Resource
    private InteractiveItemRepository interactiveItemRepository;
    @Resource
    private RoleGroupInteractiveActionRelationRepository roleGroupInteractiveActionRelationRepository;
    @Resource
    private RoleGroupInteractiveItemRelationRepository roleGroupInteractiveItemRelationRepository;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private RoleCostumeRelationRepository roleCostumeRelationRepository;
    @Resource
    private CostumeRepository costumeRepository;
    @Resource
    private RoleGroupBlindBoxConfigDao roleGroupBlindBoxConfigDao;
    @Resource
    private InteractiveItemStoryRelationRepository interactiveItemStoryRelationRepository;
    @Resource
    private BlindBoxProbRuleConfigRepository blindBoxProbRuleConfigRepository;

    @Resource
    private CabinRoleGroupRelationRepository cabinRoleGroupRelationRepository;

    @Resource
    private CabinRepository cabinRepository;
    @Resource
    private FurnitureGroupRoleGroupRelationRepository furnitureGroupRoleGroupRelationRepository;

    @Resource
    private FurnitureGroupRepository furnitureGroupRepository;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> createRoleGroup(RoleGroupAddOrUpdateParam param) {

        BizResult<Void> result = isParamValid(param);
        if (!result.isSuccess()) {
            return result;
        }

        RoleGroup roleGroup = new RoleGroup().setOrderNum(0);
        roleGroupRepository.insert(roleGroup);

        int roleGroupId = roleGroup.getId();
        List<RoleGroupRelation> roleGroupRelations = param.getRoles()
                .stream()
                .map(e -> new RoleGroupRelation().setRoleGroupId(roleGroupId).setRoleId(e.getRoleId()).setOrderNum(e.getOrderNum()))
                .collect(Collectors.toList());
        roleGroupRelationRepository.batchInsert(roleGroupRelations);
        List<Cabin> cabins = cabinRepository.queryAll();
        List<Integer> cabinIds = new ArrayList<>();
        for (Cabin cabin : cabins) {
            Cabin.Config config = cabin.getConfig();
            if(config!=null && config.isAssociateAllRoleGroup()){
                cabinIds.add(cabin.getId());
            }
        }
        log.info("RoleGroupBiz createRoleGroup cabinIds:{}, cabins:{}", cabinIds,cabins);
        List<CabinRoleGroupRelation> cabinRoleGroupRelations = cabinIds.stream().map(e -> new CabinRoleGroupRelation().setCabinId(e).setRoleGroupId(roleGroupId)).collect(Collectors.toList());
        cabinRoleGroupRelationRepository.batchSave(cabinRoleGroupRelations);
        List<FurnitureGroup> furnitureGroups = furnitureGroupRepository.queryAll();
        List<Integer> furnitureGroupIds = new ArrayList<>();
        for (FurnitureGroup furnitureGroup : furnitureGroups) {
            FurnitureGroup.Config config = furnitureGroup.getConfig();
            if(config!=null && config.isAssociateAllRoleGroup()){
                furnitureGroupIds.add(furnitureGroup.getId());
            }
        }
        log.info("RoleGroupBiz createRoleGroup furnitureGroupIds:{}", furnitureGroupIds);
        List<FurnitureGroupRoleGroupRelation> furnitureGroupRoleGroupRelations = furnitureGroupIds.stream().map(e -> new FurnitureGroupRoleGroupRelation().setFurnitureGroupId(e).setRoleGroupId(roleGroupId)).collect(Collectors.toList());
        furnitureGroupRoleGroupRelationRepository.batchSave(furnitureGroupRoleGroupRelations);
        // todo 操作记录
        return BizResult.success();
    }

    private BizResult<Void> isParamValid(RoleGroupAddOrUpdateParam param) {
        if (CollectionUtils.isEmpty(param.getRoles())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "至少配置一个角色id");
        }
        List<Integer> roleIds = param.getRoles().stream().map(RoleGroupAddOrUpdateParam.RoleGroupRelationParam::getRoleId).collect(Collectors.toList());
        List<Role> roles = roleRepository.queryByIds(roleIds);
        Map<Integer, Role> roleMap = FunctionUtils.toMap(ListUtils.emptyIfNull(roles), Role::getId);
        boolean paramError = roleIds.stream().anyMatch(e -> roleMap.get(e) == null);
        if (paramError) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色id无效");
        }

        List<RoleGroupRelation> existRoleGroupRelations = roleGroupRelationRepository.queryByRoleIds(roleIds);
        existRoleGroupRelations.removeIf(e -> e.getRoleGroupId() == param.getId());
        if (CollectionUtils.isNotEmpty(existRoleGroupRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "输入角色已存在角色组");
        }
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateRoleGroup(RoleGroupAddOrUpdateParam param) {
        BizResult<Void> result = isParamValid(param);
        if (!result.isSuccess()) {
            return result;
        }
        final RoleGroup roleGroup = roleGroupRepository.queryById(param.getId());
        if (roleGroup == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        roleGroupRelationRepository.deleteByGroupId(param.getId());
        List<RoleGroupRelation> newRelations = param.getRoles()
                .stream()
                .map(e -> new RoleGroupRelation().setRoleGroupId(param.getId()).setRoleId(e.getRoleId()).setOrderNum(e.getOrderNum()))
                .collect(Collectors.toList());
        roleGroupRelationRepository.batchInsert(newRelations);
        //        Operation operation = Operation.of(OperationConstants.OperateSubType.ITEM_ADD).add("newData", JsonUtils.writeValueAsString(item));
        //        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> updateEmotionBondSwitch(int groupId, boolean emotionBondSwitch) {
        final RoleGroup roleGroup = roleGroupRepository.queryById(groupId);
        if (roleGroup == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        RoleGroup.Config config = roleGroup.getConfig();
        if (config == null) {
            config = new RoleGroup.Config();
        }
        config.setEmotionBondSwitch(emotionBondSwitch);
        log.debug("updateEmotionBondSwitch roleGroup:{} emotionBondSwitch:{},config:{}", roleGroup, emotionBondSwitch, config);
        roleGroupRepository.updateConfigById(config, groupId);
        return BizResult.success();
    }

    public BizResult<PageView<RoleGroupView>> list() {
        List<RoleGroup> roleGroupList = roleGroupRepository.queryAll();
        List<RoleGroupRelation> relations = roleGroupRelationRepository.queryAll();
        Map<Integer, List<RoleGroupRelation>> groupRelationMap = FunctionUtils.groupBy(relations, RoleGroupRelation::getRoleGroupId);
        List<Integer> roleIds = ListUtils.emptyIfNull(relations).stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        List<Role> roles = roleRepository.queryByIds(roleIds);
        Map<Integer, Role> roleMap = FunctionUtils.toMap(roles, Role::getId);
        List<RoleGroupView> viewList = ListUtils.emptyIfNull(roleGroupList)
                .stream()
                .map(e -> RoleGroupView.valueOf(e, groupRelationMap.get(e.getId()), roleMap))
                .collect(Collectors.toList());
        PageView<RoleGroupView> pageView = PageView.form(viewList.size(), viewList);
        return BizResult.success(pageView);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateSort(int roleGroupId, int order) {
        List<RoleGroup> roleGroups = roleGroupRepository.queryAll();
        RoleGroup roleGroup = roleGroups.stream().filter(e -> e.getId() == roleGroupId).findAny().orElse(null);
        if (roleGroup == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        if (order == 0) {
            roleGroup.setOrderNum(order);
            roleGroupRepository.update(roleGroup);
            return BizResult.success();
        }
        int oldOrder = roleGroup.getOrderNum();
        if (order > oldOrder) {
            roleGroupRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(order);
            roleGroup.setOrderNum(order);
            roleGroupRepository.update(roleGroup);
        } else if (order < oldOrder) {
            RoleGroup sameOrderRoleGroup = roleGroups.stream().filter(e -> e.getOrderNum() == order).findAny().orElse(null);
            if (sameOrderRoleGroup != null) {
                sameOrderRoleGroup.setOrderNum(oldOrder);
                roleGroupRepository.update(sameOrderRoleGroup);
            }
            roleGroup.setOrderNum(order);
            roleGroupRepository.update(roleGroup);
        }
        // todo 操作记录
        return BizResult.success();
    }

    public BizResult<List<SimpleRoleView>> queryUnbindGroupRoles() {
        List<Role> roles = roleRepository.queryAll();

        List<RoleGroupRelation> groupRelations = roleGroupRelationRepository.queryAll();
        Set<Integer> bindGroupRoleIds = ListUtils.emptyIfNull(groupRelations).stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toSet());

        List<SimpleRoleView> views = roles.stream()
                .filter(e -> !bindGroupRoleIds.contains(e.getId()))
                .map(SimpleRoleView::valueOf)
                .collect(Collectors.toList());
        return BizResult.success(views);
    }

    public BizResult<Void> defaultScene(int roleGroupId, int sceneId) {
        RoleGroup roleGroup = roleGroupRepository.queryById(roleGroupId);
        if (roleGroup == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        Optional<RoleGroupSceneRelation> haveCostume = roleGroupSceneRelationRepository.queryByGroupId(roleGroupId)
                .stream()
                .filter(item -> item.getSceneId() == sceneId)
                .findFirst();
        if (!haveCostume.isPresent()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在对应场景");
        }
        Scene scene = sceneRepository.selectByPrimaryKey(sceneId);
        if (scene == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "场景不存在");
        }
        if (scene.getStatus() != SceneStatus.ON_SHELF.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "上架角色不能设置下架场景");
        }
        roleGroupRepository.update(roleGroup.setDefaultSceneId(sceneId));
        return BizResult.success();
    }

    public void defaultSceneUncheck(int roleGroupId, int sceneId) {
        RoleGroup roleGroup = roleGroupRepository.queryById(roleGroupId);
        if (roleGroup == null) {
            return;
        }
        roleGroupRepository.update(roleGroup.setDefaultSceneId(sceneId));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateSceneSort(int roleGroupId, int sceneId, int orderNum) {
        List<RoleGroupSceneRelation> roleGroupSceneRelations = roleGroupSceneRelationRepository.queryByGroupId(roleGroupId);
        RoleGroupSceneRelation roleGroupSceneRelation = roleGroupSceneRelations.stream().filter(item -> item.getSceneId() == sceneId).findFirst().orElse(null);
        if (roleGroupSceneRelation == null) {
            return BizResult.result(RoleGameResponse.ROLE_SCENE_RELATION_NOT_EXIST);
        }
        if (orderNum == 0) {
            roleGroupSceneRelationRepository.updateOrderNumById(orderNum, roleGroupId, sceneId);
            return BizResult.success();
        }
        if (orderNum > roleGroupSceneRelation.getOrderNum()) {
            roleGroupSceneRelationRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(orderNum, roleGroupId);
            roleGroupSceneRelationRepository.updateOrderNumById(orderNum, roleGroupId, sceneId);
        } else if (orderNum < roleGroupSceneRelation.getOrderNum()) {
            roleGroupSceneRelations.stream()
                    .filter(item -> item.getOrderNum() == orderNum)
                    .findAny()
                    .ifPresent(sameOrderRelation -> roleGroupSceneRelationRepository.updateOrderNumById(roleGroupSceneRelation.getOrderNum(), roleGroupId,
                            sameOrderRelation.getSceneId()));
            roleGroupSceneRelationRepository.updateOrderNumById(orderNum, roleGroupId, sceneId);
        }
        return BizResult.success();
    }

    public BizResult<PageView<SceneView>> sceneList(int roleGroupId, int pageNum, int pageSize) {
        RoleGroup roleGroup = roleGroupRepository.queryById(roleGroupId);
        if (roleGroup == null) {
            return BizResult.result(RoleGameResponse.ROLE_NOT_EXIST);
        }
        int offset = (pageNum - 1) * pageSize;
        List<RoleGroupSceneRelation> roleGroupSceneRelations = roleGroupSceneRelationRepository.queryPageByGroupId(roleGroupId, offset, pageSize);
        Map<Integer, Integer> sceneOrderNumMap = roleGroupSceneRelations.stream()
                .collect(Collectors.toMap(RoleGroupSceneRelation::getSceneId, RoleGroupSceneRelation::getOrderNum));
        List<Integer> sceneIds = roleGroupSceneRelations.stream().map(RoleGroupSceneRelation::getSceneId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(sceneIds)) {
            return BizResult.success(PageView.empty());
        }
        List<Scene> scenes = sceneRepository.queryBySceneIds(sceneIds);
        List<SceneView> sceneViews = sceneBiz.getSceneViews(sceneIds, scenes, sceneOrderNumMap, roleGroup)
                .stream()
                .sorted(Comparator.comparing(SceneView::getOrderNum).thenComparing(Comparator.comparing(SceneView::getCreateTime).reversed()))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(roleGroupSceneRelationRepository.countByRoleGroupId(roleGroupId), sceneViews));
    }

    public BizResult<RoleCostumeConfigView> queryGroupBlindBoxConfig(int groupId) {
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupId(groupId);
        if (CollectionUtils.isEmpty(roleGroupRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }

        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        Map<Integer, Integer> roleOrderMap = roleGroupRelations.stream()
                .collect(Collectors.toMap(RoleGroupRelation::getRoleId, RoleGroupRelation::getOrderNum));
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByRoleIds(roleIds);
        Map<Integer, RoleCostumeRelation> costumeIdRelationMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));
        Map<Integer, Role> costumeIdRoleMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, roleCostumeRelation -> roleMap.get(roleCostumeRelation.getRoleId())));
        List<Integer> costumeIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toList());
        List<Costume> costumes = costumeRepository.queryByIds(costumeIds)
                .stream()
                .filter(costume -> costume.getStatus() == CostumeStatus.ON_SHELF.getCode())
                .sorted(Comparator.comparing(Costume::getLevel)
                        .thenComparing(costume -> roleOrderMap.get(costumeIdRelationMap.get(costume.getId()).getRoleId()))
                        .thenComparing(costume -> costumeIdRelationMap.get(costume.getId()).getOrderNum()))
                .collect(Collectors.toList());

        RoleGroupCostumeBlindBoxConfig configList = roleGroupBlindBoxConfigDao.findByGroupId(groupId);

        RoleCostumeConfigView roleCostumeConfigView = new RoleCostumeConfigView();
        List<RoleCostumeConfigView.Config> configs;
        if (configList == null) {
            configs = costumes.stream()
                    .map(costume -> RoleCostumeConfigView.Config.valueOf(costume, (RoleCostumeRelation.Config) null,
                            costumeIdRoleMap.get(costume.getId()).getName()))
                    .collect(Collectors.toList());
        } else {
            List<RoleGroupCostumeBlindBoxConfig.Config> costumeBlindBoxConfigs = configList.getCostumeBlindBoxConfigs();
            Map<Integer, RoleGroupCostumeBlindBoxConfig.Config> configMap = costumeBlindBoxConfigs.stream()
                    .collect(Collectors.toMap(RoleGroupCostumeBlindBoxConfig.Config::getCostumeId, Function.identity()));
            configs = costumes.stream()
                    .map(costume -> RoleCostumeConfigView.Config.valueOf(costume, configMap.get(costume.getId()),
                            costumeIdRoleMap.get(costume.getId()).getName()))
                    .collect(Collectors.toList());
        }
        roleCostumeConfigView.setConfigs(configs);
        return BizResult.success(roleCostumeConfigView);
    }

    public BizResult<Void> upsertGroupBlindBoxConfig(RoleGroupCostumeConfigParam param) {
        final int groupId = param.getGroupId();
        final List<RoleGroupCostumeConfigParam.Config> configs = param.getConfigs();
        if (CollectionUtils.isEmpty(configs)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮配置不能为空");
        }

        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupId(groupId);
        if (CollectionUtils.isEmpty(roleGroupRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }

        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        final Map<Integer, RoleCostumeRelation> roleCostumeRelationMap = roleCostumeRelationRepository.queryByRoleIds(roleIds)
                .stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));

        for (RoleGroupCostumeConfigParam.Config config : configs) {
            final RoleCostumeRelation roleCostumeRelation = roleCostumeRelationMap.get(config.getCostumeId());
            if (roleCostumeRelation == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮与角色组关系不存在");
            }
            if (config.getProbability() < 0 || config.getProbability() > 100d) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "概率范围错误");
            }
        }

        roleGroupBlindBoxConfigDao.upsert(groupId, configs);

        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_GROUP_COSTUME_CONFIG_UPDATE).add("groupId", groupId).add("configs", configs);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<PageView<InteractiveActionView>> actionList(int roleGroupId, int pageNum, int pageSize) {
        RoleGroup roleGroup = roleGroupRepository.queryById(roleGroupId);
        if (roleGroup == null) {
            return BizResult.result(RoleGameResponse.ROLE_NOT_EXIST);
        }
        int offset = (pageNum - 1) * pageSize;
        List<RoleGroupInteractiveActionRelation> roleGroupInteractiveActionRelations = roleGroupInteractiveActionRelationRepository.queryPageByGroupId(
                roleGroupId, offset, pageSize);
        Map<Integer, Integer> actionOrderNumMap = roleGroupInteractiveActionRelations.stream()
                .collect(Collectors.toMap(RoleGroupInteractiveActionRelation::getActionId, RoleGroupInteractiveActionRelation::getOrderNum));
        List<Integer> actionIds = roleGroupInteractiveActionRelations.stream()
                .map(RoleGroupInteractiveActionRelation::getActionId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(actionIds)) {
            return BizResult.success(PageView.empty());
        }
        // get同一个角色组下互动动作
        List<InteractiveAction> interactiveActions = interactiveActionRepository.queryByActionIds(actionIds);
        // 获取互动动作views排序输出
        List<InteractiveActionView> interactiveActionViews = this.getInteractiveActionViews(actionIds, interactiveActions, actionOrderNumMap)
                .stream()
                .sorted(Comparator.comparing(InteractiveActionView::getOrderNum)
                        .thenComparing(Comparator.comparing(InteractiveActionView::getCreateTime).reversed()))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(roleGroupInteractiveActionRelationRepository.countByRoleGroupId(roleGroupId), interactiveActionViews));
    }

    private List<InteractiveActionView> getInteractiveActionViews(List<Integer> actionIds, List<InteractiveAction> interactiveActions,
                                                                  Map<Integer, Integer> actionOrderNumMap) {
        List<RoleGroupInteractiveActionRelation> roleGroupInteractiveActionRelations = roleGroupInteractiveActionRelationRepository.queryByActionIds(actionIds);
        // 理论上只有一个值,因为是同一个服务组
        Set<Integer> roleGroupIds = roleGroupInteractiveActionRelations.stream()
                .map(RoleGroupInteractiveActionRelation::getRoleGroupId)
                .collect(Collectors.toSet());
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIds(roleGroupIds);
        Set<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toSet());
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        return interactiveActions.stream().map(interactiveAction -> {
            Integer orderNum = null == actionOrderNumMap ? null : actionOrderNumMap.get(interactiveAction.getId());
            return InteractiveActionView.valueOf(interactiveAction, orderNum, roleMap);
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateActionSort(int roleGroupId, int actionId, int order) {
        List<RoleGroupInteractiveActionRelation> roleGroupInteractiveActionRelations = roleGroupInteractiveActionRelationRepository.queryByRoleGroupId(
                roleGroupId);
        RoleGroupInteractiveActionRelation roleGroupInteractiveActionRelation = roleGroupInteractiveActionRelations.stream()
                .filter(item -> item.getActionId() == actionId)
                .findAny()
                .orElse(null);
        if (roleGroupInteractiveActionRelation == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "互动动作不存在");
        }
        if (0 == order) {
            roleGroupInteractiveActionRelationRepository.updateOrderNumByRoleGroupIdAndActionId(order, roleGroupId, actionId);
            return BizResult.success();
        }
        int oldOrder = roleGroupInteractiveActionRelation.getOrderNum();
        if (order > oldOrder) {
            roleGroupInteractiveActionRelationRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(order, roleGroupId);
            roleGroupInteractiveActionRelationRepository.updateOrderNumByRoleGroupIdAndActionId(order, roleGroupId, actionId);
        } else if (order < oldOrder) {
            roleGroupInteractiveActionRelations.stream()
                    .filter(item -> item.getOrderNum() == order)
                    .findAny()
                    .ifPresent(sameOrderRelation -> roleGroupInteractiveActionRelationRepository.updateOrderNumByRoleGroupIdAndActionId(order, roleGroupId,
                            sameOrderRelation.getActionId()));
            roleGroupInteractiveActionRelationRepository.updateOrderNumByRoleGroupIdAndActionId(order, roleGroupId, actionId);
        }
        // TODO：operation,delete cache
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdateAction(InteractiveActionAddOrUpdateParam param) {
        // 剧情类型固定：动作剧情
        Integer actionId = param.getId();
        if (actionId != null && actionId > 0) {
            return doUpdateAction(param);
        } else {
            return doInsertAction(param);
        }
    }

    private BizResult<Void> doUpdateAction(InteractiveActionAddOrUpdateParam param) {
        // 更新:不支持编辑关联角色和剧情类型
        InteractiveAction originalInteractiveAction = interactiveActionRepository.selectByPrimaryKey(param.getId());
        if (null == originalInteractiveAction) {
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "互动动作不存在");
        }
        if (null != param.getEmotionBondLevel()) {
            if (emotionBondLevelCheck(param.getEmotionBondLevel())) {
                originalInteractiveAction.setEmotionBondLevel(param.getEmotionBondLevel());
            }
        }
        if (StringUtils.isNotBlank(param.getName())) {
            originalInteractiveAction.setName(param.getName());
        }
        // update config
        InteractiveAction.Config originalActionConfig = originalInteractiveAction.getConfig();
        if (null != param.getActionImage()) {
            originalActionConfig.setActionImage(param.getActionImage());
        }
        // 解锁
        if (null != param.getUnlockType()) {
            originalActionConfig.setUnlockType(param.getUnlockType());
            boolean isNeedCondition = null != param.getUnlockCondition() && InteractiveActionUnlockTypeEnum.FREE_UNLOCK.getCode() != param.getUnlockType();
            if (isNeedCondition) {
                originalActionConfig.setUnlockCondition(param.getUnlockCondition());
            }
        }
        // 动画图片
        if (null != param.getAnimationConfig()) {
            InteractiveAction.Config.AnimationConfig animationConfigBean = param.getAnimationConfig().toBean();
            if (animationConfigBean.getRoleAnimationConfigs().size() != 2) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "双人剧情需要两个角色动画配置");
            }
            originalActionConfig.setAnimationConfig(animationConfigBean);
        }
        // 互动配置
        originalInteractiveAction.setConfig(originalActionConfig);
        interactiveActionRepository.updateByPrimaryKeySelective(originalInteractiveAction);
        return BizResult.success();
    }

    private BizResult<Void> doInsertAction(InteractiveActionAddOrUpdateParam param) {
        InteractiveAction interactiveAction = new InteractiveAction();
        // 羁绊值等阶
        if (null == param.getEmotionBondLevel()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "未配置羁绊值等阶");
        }
        if (!emotionBondLevelCheck(param.getEmotionBondLevel())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "羁绊值等阶配置错误");
        }
        interactiveAction.setEmotionBondLevel(param.getEmotionBondLevel());
        // 默认未上架
        interactiveAction.setStatus(null != param.getStatus() ? param.getStatus() : CommonStatus.NOT_ONLINE.getCode());
        // 动作名称为空报错
        if (StringUtils.isBlank(param.getName())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "未配置动作名称");
        }
        interactiveAction.setName(param.getName());
        // set config
        InteractiveAction.Config roleActionConfig = new InteractiveAction.Config();
        // 动作图像
        roleActionConfig.setActionImage(param.getActionImage());
        // 解锁类型校验
        InteractiveActionUnlockTypeEnum unlockType = InteractiveActionUnlockTypeEnum.getByCode(param.getUnlockType());
        if (unlockType == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "解锁条件类型配置错误");
        }
        roleActionConfig.setUnlockType(param.getUnlockType());
        // 解锁条件校验
        if (unlockConditionCheck(param.getUnlockType(), param.getUnlockCondition())) {
            roleActionConfig.setUnlockCondition(param.getUnlockCondition());
        }
        // 动画图片
        if (null != param.getAnimationConfig()) {
            InteractiveAction.Config.AnimationConfig animationConfigBean = param.getAnimationConfig().toBean();
            if (animationConfigBean.getRoleAnimationConfigs().size() != 2) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "双人剧情需要两个角色动画配置");
            }
            roleActionConfig.setAnimationConfig(animationConfigBean);
        }
        // 互动动作配置
        interactiveAction.setConfig(roleActionConfig);
        interactiveActionRepository.insert(interactiveAction);
        // 角色组-互动动作关系
        RoleGroupInteractiveActionRelation roleGroupInteractiveActionRelation = new RoleGroupInteractiveActionRelation();
        roleGroupInteractiveActionRelation.setRoleGroupId(param.getRoleGroupId());
        roleGroupInteractiveActionRelation.setActionId(interactiveAction.getId());
        // 新建默认排序为0
        roleGroupInteractiveActionRelation.setOrderNum(0);
        roleGroupInteractiveActionRelationRepository.insert(roleGroupInteractiveActionRelation);
        return BizResult.success();
    }

    // 校验解锁条件
    private boolean unlockConditionCheck(Integer unlockType, Integer unlockCondition) {
        if (InteractiveActionUnlockTypeEnum.FREE_UNLOCK.getCode() == unlockType) {
            return false;
        }
        return unlockCondition != null && unlockCondition > 0;
    }

    // 校验羁绊值等阶
    private boolean emotionBondLevelCheck(Integer emotionBondLevel) {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.ROLE_COMMON_PROPERTY);
        if (keyValueConfig == null) {
            return false;
        }
        RoleCommonPropertyConfig roleCommonPropertyConfig = JsonUtils.findObject(keyValueConfig.getValue(), RoleCommonPropertyConfig.class);
        if (roleCommonPropertyConfig == null) {
            return false;
        }
        return roleCommonPropertyConfig.checkActionEmotionBondValueIndex(emotionBondLevel);
    }

    public BizResult<Void> actionPublish(int id) {
        final InteractiveAction interactiveAction = interactiveActionRepository.selectByPrimaryKey(id);
        if (null == interactiveAction) {
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "互动动作不存在");
        }
        if (CommonStatus.ONLINE.getCode() == interactiveAction.getStatus()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "互动动作已上架");
        }
        interactiveActionRepository.updateStatusById(id, CommonStatus.ONLINE.getCode());
        // TODO:cache,operation
        return BizResult.success();
    }

    public BizResult<Void> actionOffline(int id) {
        final InteractiveAction interactiveAction = interactiveActionRepository.selectByPrimaryKey(id);
        if (null == interactiveAction) {
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "互动动作不存在");
        }
        if (CommonStatus.NOT_ONLINE.getCode() == interactiveAction.getStatus()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "互动动作已下架");
        }
        interactiveActionRepository.updateStatusById(id, CommonStatus.NOT_ONLINE.getCode());
        // TODO:cache,operation
        return BizResult.success();
    }

    public BizResult<PageView<InteractiveItemView>> itemList(int roleGroupId, int pageNum, int pageSize) {
        RoleGroup roleGroup = roleGroupRepository.queryById(roleGroupId);
        if (roleGroup == null) {
            return BizResult.result(RoleGameResponse.ROLE_NOT_EXIST);
        }
        int offset = (pageNum - 1) * pageSize;
        List<RoleGroupInteractiveItemRelation> roleGroupInteractiveItemRelations = roleGroupInteractiveItemRelationRepository.queryPageByRoleGroupId(
                roleGroupId, offset, pageSize);
        Map<Integer, Integer> itemOrderNumMap = roleGroupInteractiveItemRelations.stream()
                .collect(Collectors.toMap(RoleGroupInteractiveItemRelation::getItemId, RoleGroupInteractiveItemRelation::getOrderNum));
        List<Integer> itemIds = roleGroupInteractiveItemRelations.stream().map(RoleGroupInteractiveItemRelation::getItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemIds)) {
            return BizResult.success(PageView.empty());
        }
        List<InteractiveItem> interactiveItems = interactiveItemRepository.queryByItemIds(itemIds);
        // 获取互动道具排序输出
        List<InteractiveItemView> interactiveItemViews = this.getInteractiveItemViews(itemIds, interactiveItems, itemOrderNumMap)
                .stream()
                .sorted(Comparator.comparing(InteractiveItemView::getOrderNum)
                        .thenComparing(Comparator.comparing(InteractiveItemView::getCreateTime).reversed()))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(roleGroupInteractiveItemRelationRepository.countByRoleGroupId(roleGroupId), interactiveItemViews));
    }

    @NotNull
    private List<InteractiveItemView> getInteractiveItemViews(List<Integer> itemIds, List<InteractiveItem> interactiveItems,
                                                              Map<Integer, Integer> itemOrderNumMap) {
        List<RoleGroupInteractiveItemRelation> roleGroupInteractiveItemRelations = roleGroupInteractiveItemRelationRepository.queryByItemIds(itemIds);
        Set<Integer> roleGroupIds = roleGroupInteractiveItemRelations.stream()
                .map(RoleGroupInteractiveItemRelation::getRoleGroupId)
                .collect(Collectors.toSet());
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIds(roleGroupIds);
        Set<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toSet());
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        final List<InteractiveItemStoryRelation> interactiveItemStoryRelations = interactiveItemStoryRelationRepository.selectByItemIds(itemIds);
        final Map<Integer, List<InteractiveItemStoryRelation>> interactiveItemRelationMap = interactiveItemStoryRelations.stream()
                .collect(Collectors.groupingBy(InteractiveItemStoryRelation::getInteractiveItemId));
        return interactiveItems.stream().map(interactiveItem -> {
            Integer orderNum = null == itemOrderNumMap ? null : itemOrderNumMap.get(interactiveItem.getId());
            final List<InteractiveItemStoryRelation> relations = interactiveItemRelationMap.get(interactiveItem.getId());
            return InteractiveItemView.valueOf(interactiveItem, relations, orderNum, roleMap);
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdateItem(InteractiveItemAddOrUpdateParam param) {
        // 剧情类型固定：动作剧情
        Integer itemId = param.getId();
        if (itemId != null && itemId > 0) {
            return doUpdateItem(param);
        } else {
            return doInsertItem(param);
        }
    }

    private BizResult<Void> doInsertItem(InteractiveItemAddOrUpdateParam param) {
        InteractiveItem interactiveItem = new InteractiveItem();
        // 默认未上架
        interactiveItem.setStatus(null != param.getStatus() ? param.getStatus() : CommonStatus.NOT_ONLINE.getCode());
        // 道具名称为空报错
        if (StringUtils.isBlank(param.getName())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "道具名称不能为空");
        }
        interactiveItem.setName(param.getName());
        // set config
        InteractiveItem.Config roleItemConfig = new InteractiveItem.Config();
        roleItemConfig.setItemImage(param.getItemImage());
        interactiveItem.setConfig(roleItemConfig);
        interactiveItemRepository.insert(interactiveItem);
        log.info("doInsertItem insert interactiveItem:{}, param:{}", interactiveItem, param);
        final List<Integer> storyIds = param.getStoryIds();
        if (CollectionUtils.isNotEmpty(storyIds)) {
            List<InteractiveItemStoryRelation> interactiveItemStoryRelations = Lists.newArrayListWithCapacity(storyIds.size());
            for (Integer storyId : storyIds) {
                InteractiveItemStoryRelation interactiveItemStoryRelation = new InteractiveItemStoryRelation().setInteractiveItemId(interactiveItem.getId())
                        .setStoryId(storyId);
                interactiveItemStoryRelations.add(interactiveItemStoryRelation);
            }
            interactiveItemStoryRelationRepository.insertBatch(interactiveItemStoryRelations);
        }
        // 角色组-互动道具拍-关系表
        RoleGroupInteractiveItemRelation roleGroupInteractiveItemRelation = new RoleGroupInteractiveItemRelation();
        roleGroupInteractiveItemRelation.setRoleGroupId(param.getRoleGroupId());
        roleGroupInteractiveItemRelation.setItemId(interactiveItem.getId());
        // 新建互动道具,order默认是0
        roleGroupInteractiveItemRelation.setOrderNum(0);
        roleGroupInteractiveItemRelationRepository.insert(roleGroupInteractiveItemRelation);
        return BizResult.success();
    }

    private BizResult<Void> doUpdateItem(InteractiveItemAddOrUpdateParam param) {
        final InteractiveItem originalInteractiveItem = interactiveItemRepository.selectByPrimaryKey(param.getId());
        if (null == originalInteractiveItem) {
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "互动道具不存在");
        }
        // 道具名称
        if (StringUtils.isNotBlank(param.getName())) {
            originalInteractiveItem.setName(param.getName());
        }
        // 更新config
        InteractiveItem.Config originalConfig = originalInteractiveItem.getConfig();
        if (null != param.getItemImage()) {
            originalConfig.setItemImage(param.getItemImage());
        }
        // 动画配置
        if (CollectionUtils.isNotEmpty(param.getStoryIds())) {
            final List<Integer> storyIds = param.getStoryIds();
            interactiveItemStoryRelationRepository.deleteByInteractiveItemId(param.getId());
            List<InteractiveItemStoryRelation> interactiveItemStoryRelations = Lists.newArrayListWithCapacity(storyIds.size());
            for (Integer storyId : storyIds) {
                InteractiveItemStoryRelation interactiveItemStoryRelation = new InteractiveItemStoryRelation().setInteractiveItemId(param.getId())
                        .setStoryId(storyId);
                interactiveItemStoryRelations.add(interactiveItemStoryRelation);
            }
            interactiveItemStoryRelationRepository.insertBatch(interactiveItemStoryRelations);
        }
        originalInteractiveItem.setConfig(originalConfig);
        interactiveItemRepository.updateByPrimaryKeySelective(originalInteractiveItem);
        return BizResult.success();
    }

    public BizResult<Void> itemPublish(int id) {
        InteractiveItem interactiveItem = interactiveItemRepository.selectByPrimaryKey(id);
        if (interactiveItem == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "互动道具不存在");
        }
        if (interactiveItem.getStatus() == CommonStatus.ONLINE.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "互动道具已上架");
        }
        interactiveItemRepository.updateStatusById(id, CommonStatus.ONLINE.getCode());
        // todo:cache,operation
        return BizResult.success();
    }

    public BizResult<Void> itemOffline(int id) {
        InteractiveItem interactiveItem = interactiveItemRepository.selectByPrimaryKey(id);
        if (interactiveItem == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "互动道具不存在");
        }
        if (interactiveItem.getStatus() == CommonStatus.NOT_ONLINE.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "互动道具已下架");
        }
        interactiveItemRepository.updateStatusById(id, CommonStatus.NOT_ONLINE.getCode());
        // todo:cache,operation
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateItemSort(int roleGroupId, int itemId, int order) {
        List<RoleGroupInteractiveItemRelation> roleGroupInteractiveItemRelations = roleGroupInteractiveItemRelationRepository.queryByRoleGroupId(roleGroupId);
        RoleGroupInteractiveItemRelation roleGroupInteractiveItemRelation = roleGroupInteractiveItemRelations.stream()
                .filter(item -> item.getItemId() == itemId)
                .findAny()
                .orElse(null);
        if (roleGroupInteractiveItemRelation == null) {
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "互动道具不存在");
        }
        if (0 == order) {
            roleGroupInteractiveItemRelationRepository.updateOrderNumByRoleGroupIdAndItemId(order, roleGroupId, itemId);
            return BizResult.success();
        }
        int oldOrder = roleGroupInteractiveItemRelation.getOrderNum();
        if (order > oldOrder) {
            roleGroupInteractiveItemRelationRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(order, roleGroupId);
            roleGroupInteractiveItemRelationRepository.updateOrderNumByRoleGroupIdAndItemId(order, roleGroupId, itemId);
        } else if (order < oldOrder) {
            roleGroupInteractiveItemRelations.stream()
                    .filter(item -> item.getOrderNum() == order)
                    .findAny()
                    .ifPresent(sameOrderRelation -> roleGroupInteractiveItemRelationRepository.updateOrderNumByRoleGroupIdAndItemId(order, roleGroupId,
                            sameOrderRelation.getItemId()));
            roleGroupInteractiveItemRelationRepository.updateOrderNumByRoleGroupIdAndItemId(order, roleGroupId, itemId);
        }
        return BizResult.success();
    }

    /**
     * @param roleGroupId 角色组ID
     * @param questContainerId 道具任务活动ID，支持数字和空值
     */
    public BizResult<Void> questContainerIdUpdate(Integer roleGroupId, String questContainerId) {

        if (null == roleGroupId) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        RoleGroup roleGroup = roleGroupRepository.queryById(roleGroupId);
        if (roleGroup == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }

        if (StringUtils.isNotBlank(questContainerId) && !StringUtils.isNumeric(questContainerId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "仅支持填写数字");
        }
        RoleGroup.Config config = roleGroup.getConfig();
        if (config == null) {
            config = new RoleGroup.Config();
        }
        if (StringUtils.isNotBlank(questContainerId)) {
            config.setQuestContainerId(Long.valueOf(questContainerId));
        } else {
            config.setQuestContainerId(null);
        }
        log.debug("questContainerIdUpdate roleGroupId:{}, questContainerId:{}, config:{}", roleGroupId, questContainerId, config);
        roleGroupRepository.updateConfigById(config, roleGroupId);
        return BizResult.success();
    }

    public BizResult<BlindBoxProbabilityView> getGroupBlindBoxProbability(Integer groupId) {
        if (groupId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组ID不能为空");
        }
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupId(groupId);
        if (CollectionUtils.isEmpty(roleGroupRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        Map<Integer, Integer> roleOrderMap = roleGroupRelations.stream()
                .collect(Collectors.toMap(RoleGroupRelation::getRoleId, RoleGroupRelation::getOrderNum));
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByRoleIds(roleIds);
        Map<Integer, RoleCostumeRelation> costumeIdRelationMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));
        Map<Integer, Role> costumeIdRoleMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, roleCostumeRelation -> roleMap.get(roleCostumeRelation.getRoleId())));
        List<Integer> costumeIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toList());
        List<Costume> costumes = costumeRepository.queryByIds(costumeIds)
                .stream()
                .filter(costume -> costume.getStatus() == CostumeStatus.ON_SHELF.getCode())
                .sorted(Comparator.comparing(Costume::getLevel)
                        .thenComparing(costume -> roleOrderMap.get(costumeIdRelationMap.get(costume.getId()).getRoleId()))
                        .thenComparing(costume -> costumeIdRelationMap.get(costume.getId()).getOrderNum()))
                .collect(Collectors.toList());
        RoleGroupCostumeBlindBoxConfig probabilityConfig = roleGroupBlindBoxConfigDao.findByGroupId(groupId);
        BlindBoxProbabilityView probabilityView = new BlindBoxProbabilityView();
        if (null == probabilityConfig || CollectionUtils.isEmpty(probabilityConfig.getCostumeBlindBoxConfigs())) {
            probabilityView.setConfigs(costumes.stream()
                    .map(costume -> BlindBoxProbabilityView.ConfigView.valueOf(costume, null, costumeIdRoleMap.get(costume.getId()).getName()))
                    .collect(Collectors.toList()));
        } else {
            List<RoleGroupCostumeBlindBoxConfig.Config> costumeBlindBoxConfigs = probabilityConfig.getCostumeBlindBoxConfigs();
            Map<Integer, RoleGroupCostumeBlindBoxConfig.Config> costumeBlindBoxConfigMap = costumeBlindBoxConfigs.stream()
                    .collect(Collectors.toMap(RoleGroupCostumeBlindBoxConfig.Config::getCostumeId, Function.identity()));
            probabilityView.setDefaultRuleId(probabilityConfig.getDefaultRuleId());
            probabilityView.setConfigs(costumes.stream()
                    .map(costume -> BlindBoxProbabilityView.ConfigView.valueOf(costume, costumeBlindBoxConfigMap.get(costume.getId()),
                            costumeIdRoleMap.get(costume.getId()).getName()))
                    .collect(Collectors.toList()));
            probabilityView.setExptRules(BlindBoxProbabilityView.ExptRuleView.valueOf(probabilityConfig.getExptRules()));
        }
        return BizResult.success(probabilityView);
    }

    public BizResult<Void> updateGroupBlindBoxProbability(GroupBlindBoxProbabilityParam param) {
        final Integer groupId = param.getGroupId();
        final Integer defaultRuleId = param.getDefaultRuleId();
        final List<GroupBlindBoxProbabilityParam.Config> configs = param.getConfigs();
        final GroupBlindBoxProbabilityParam.ExptRule exptRules = param.getExptRules();
        if (groupId == null || (null == exptRules & defaultRuleId == null) || CollectionUtils.isEmpty(configs)) {
            log.error("updateGroupBlindBoxProbability error, groupId: {}, defaultRuleId: {}, configs: {}", groupId, defaultRuleId, configs);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒概率参数错误");
        }
        if (null != defaultRuleId) {
            BlindBoxProbRuleConfig defaultRule = blindBoxProbRuleConfigRepository.selectByPrimaryKey(defaultRuleId);
            if (defaultRule == null) {
                log.error("updateGroupBlindBoxProbability error, groupId: {}, defaultRuleId: {}, configs: {}", groupId, defaultRuleId, configs);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "普通盲盒概率规则不存在");
            }
        }
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupId(groupId);
        if (CollectionUtils.isEmpty(roleGroupRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
        final Map<Integer, RoleCostumeRelation> roleCostumeRelationMap = roleCostumeRelationRepository.queryByRoleIds(roleIds)
                .stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));
        for (GroupBlindBoxProbabilityParam.Config config : configs) {
            final RoleCostumeRelation roleCostumeRelation = roleCostumeRelationMap.get(config.getCostumeId());
            if (roleCostumeRelation == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮与角色组关系不存在");
            }
        }
        if (Objects.isNull(exptRules)) {
            param.setExptRules(new GroupBlindBoxProbabilityParam.ExptRule());
        }
        roleGroupBlindBoxConfigDao.upsertConfig(param);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_GROUP_COSTUME_CONFIG_UPDATE)
                .add("groupId", groupId)
                .add("defaultRuleId", defaultRuleId)
                .add("configs", configs)
                .add("exptRules", JsonUtils.writeValueAsString(param.getExptRules()));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }
}
