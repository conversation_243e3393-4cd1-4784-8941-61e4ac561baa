package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.ActivityRewardComponent;
import com.kuaikan.role.game.admin.model.param.ActivityAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.CostumeBlindBoxActivityView;
import com.kuaikan.role.game.admin.model.view.SimpleRewardView;
import com.kuaikan.role.game.admin.repository.BlindBoxCouponConfigRepository;
import com.kuaikan.role.game.admin.repository.CostumeBlindBoxActivityRepository;
import com.kuaikan.role.game.admin.repository.CostumePartRepository;
import com.kuaikan.role.game.admin.repository.CostumeRepository;
import com.kuaikan.role.game.admin.repository.InteractiveActionRepository;
import com.kuaikan.role.game.admin.repository.ItemRepository;
import com.kuaikan.role.game.admin.repository.RoleAdoptCouponConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleSpiritStoneConfigRepository;
import com.kuaikan.role.game.admin.repository.SceneRepository;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.CostumeBlindBoxActivity;
import com.kuaikan.role.game.api.bean.reward.RoleConfig;
import com.kuaikan.role.game.api.enums.ActivityStatus;
import com.kuaikan.role.game.api.enums.CostumeStatus;
import com.kuaikan.role.game.api.enums.RewardType;
import com.kuaikan.role.game.common.enums.CacheConfig;

@Service
@Slf4j
public class ActivityBiz {

    @Resource
    private CostumeBlindBoxActivityRepository costumeBlindBoxActivityRepository;
    @Resource
    private SceneRepository sceneRepository;
    @Resource
    private CostumePartRepository costumePartRepository;
    @Resource
    private InteractiveActionRepository interactiveActionRepository;
    @Resource
    private ItemRepository itemRepository;
    @Resource
    private CostumeRepository costumeRepository;
    @Resource
    private RoleAdoptCouponConfigRepository roleAdoptCouponConfigRepository;
    @Resource
    private BlindBoxCouponConfigRepository blindBoxCouponConfigRepository;
    @Resource
    private RoleSpiritStoneConfigRepository roleSpiritStoneConfigRepository;
    @Resource
    private ActivityRewardComponent activityRewardComponent;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(ActivityAddOrUpdateParam param) {
        int id = param.getId();
        Operation operation;
        if (id > 0) {
            CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryById(id);
            if (activity == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "盲盒活动不存在");
            }
            String old = JsonUtils.writeValueAsString(activity);

            activity.setOrderNum(param.getOrderNum());
            activity.setName(param.getName());
            activity.setRoleGroupId(param.getRoleGroupId());
            activity.setStartAt(param.getStartAt());
            activity.setEndAt(param.getEndAt());
            activity.setConfig(param.toCostumeBlindBoxActivityConfig());
            costumeBlindBoxActivityRepository.updateByPrimaryKeySelective(activity);
            operation = Operation.of(OperationConstants.OperateSubType.COSTUME_ACTIVITY_ADD_OR_UPDATE)
                    .add("oldData", old)
                    .add("newData", JsonUtils.writeValueAsString(activity));
        } else {
            CostumeBlindBoxActivity.Config config = param.toCostumeBlindBoxActivityConfig();
            CostumeBlindBoxActivity activity = new CostumeBlindBoxActivity().setOrderNum(param.getOrderNum())
                    .setName(param.getName())
                    .setRoleGroupId(param.getRoleGroupId())
                    .setStartAt(param.getStartAt())
                    .setEndAt(param.getEndAt())
                    .setConfig(config);
            costumeBlindBoxActivityRepository.insert(activity);
            id = activity.getId();
            operation = Operation.of(OperationConstants.OperateSubType.COSTUME_ACTIVITY_ADD_OR_UPDATE).add("newData", JsonUtils.writeValueAsString(activity));
        }
        deleteCacheByActivityId(id);
        deleteCacheByGroupId(param.getRoleGroupId());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<PageView<CostumeBlindBoxActivityView>> list(int pageNum, int pageSize) {
        int count = costumeBlindBoxActivityRepository.count();
        if (count == 0) {
            return BizResult.success(PageView.empty());
        }
        int offset = (pageNum - 1) * pageSize;
        List<CostumeBlindBoxActivity> activities = costumeBlindBoxActivityRepository.queryByPage(offset, pageSize);
        List<CostumeBlindBoxActivityView> costumeBlindBoxActivityViews = activities.stream()
                .map(CostumeBlindBoxActivityView::valueOf)
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(count, costumeBlindBoxActivityViews));
    }

    public BizResult<List<SimpleRewardView>> rewardList() {
        return activityRewardComponent.rewardList();
    }

    public BizResult<Void> publish(int activityId) {
        CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryById(activityId);
        if (activity == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动不存在");
        }
        if (activity.getStatus() == ActivityStatus.ONLINE.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动已上架");
        }
        CostumeBlindBoxActivity.Config config = activity.getConfig();
        if (config == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动配置不存在");
        }
        List<RoleConfig> roleConfigs = config.getRoleConfigs();
        List<Integer> costumeIds = roleConfigs.stream().map(RoleConfig::getCostumeId).collect(Collectors.toList());
        List<Costume> costumes = costumeRepository.queryByIds(costumeIds);
        for (Costume costume : costumes) {
            if (costume.getStatus() != CostumeStatus.ON_SHELF.getCode()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "配置的装扮未上架");
            }
        }
        if (config.getFreeCount() <= 0 || config.getFreeCount() > 9) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "免费次数配置错误");
        }
        if (config.getScaleRatio() < 1 || config.getScaleRatio() > 100) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "缩放比例配置错误");
        }
        activity.setStatus(ActivityStatus.ONLINE.getCode());
        costumeBlindBoxActivityRepository.updateByPrimaryKeySelective(activity);
        deleteCacheByActivityId(activityId);
        deleteCacheByGroupId(activity.getRoleGroupId());
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.COSTUME_ACTIVITY_PUBLISH).add("id", activityId));
        return BizResult.success();
    }

    public BizResult<Void> offline(int activityId) {
        CostumeBlindBoxActivity activity = costumeBlindBoxActivityRepository.queryById(activityId);
        if (activity == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动不存在");
        }
        if (activity.getStatus() == ActivityStatus.NOT_ONLINE.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动未上架");
        }
        if (activity.getStartAt() < System.currentTimeMillis() && activity.getEndAt() > System.currentTimeMillis()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮盲盒活动进行中");
        }
        activity.setStatus(ActivityStatus.NOT_ONLINE.getCode());
        costumeBlindBoxActivityRepository.updateByPrimaryKeySelective(activity);
        deleteCacheByActivityId(activityId);
        deleteCacheByGroupId(activity.getRoleGroupId());
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.COSTUME_ACTIVITY_OFFLINE).add("id", activity));
        return BizResult.success();
    }

    private void deleteCacheByActivityId(int activityId) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(CacheConfig.BLIND_BOX_ACTIVITY_BY_ACTIVITY_ID.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.BLIND_BOX_ACTIVITY_BY_ACTIVITY_ID.getKeyPattern(), activityId);
        clusterClientByName.del(cacheKey);
    }

    private void deleteCacheByGroupId(int groupId) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(CacheConfig.BLIND_BOX_ACTIVITY_BY_GROUP_ID.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.BLIND_BOX_ACTIVITY_BY_GROUP_ID.getKeyPattern(), groupId);
        clusterClientByName.del(cacheKey);
    }

}
