package com.kuaikan.role.game.admin.biz;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.model.view.CommonSettingView;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.common.enums.CommonSettingType;

/**
 * CommonSettingBiz
 *
 * <AUTHOR>
 * @since 2024/7/16
 */
@Service
@Slf4j
public class CommonSettingBiz {

    @Resource
    private QiniuComponent qiniuComponent;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    private static final String COMMON_SETTING_FILE_PATH = "commonSetting/";

    public BizResult<Void> updateFile(MultipartFile file, CommonSettingType type, String fileName) {
        log.info("CommonSettingFileUpload file: {}, type: {}, fileName: {}", file, type, fileName);
        String fileKey = COMMON_SETTING_FILE_PATH + fileName;
        try {
            String fileUrl = qiniuComponent.uploadWithStream(QiniuComponent.FREE_BUCKET_NAME, file.getBytes(), fileKey);
            log.info("CommonSettingFileUpload fileUrl: {}", fileUrl);
            updateKeyValueConfig(type.getKey(), fileUrl);
        } catch (IOException e) {
            log.error("CommonSettingFileUpload error, type: {}, fileName: {}", type, fileName, e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
        return BizResult.success();
    }

    public BizResult<Void> updateText(String textContent, CommonSettingType type) {
        log.info("CommonSettingTextUpload textContent: {}, type: {}", textContent, type);
        updateKeyValueConfig(type.getKey(), textContent);
        return BizResult.success();
    }

    void updateKeyValueConfig(String key, String value) {
        log.info("updateKeyValueConfig key: {}, value: {}", key, value);
        KeyValueConfig keyValueConfig = new KeyValueConfig().setKey(key).setValue(value);
        if (keyValueConfigRepository.queryByKey(key) == null) {
            keyValueConfigRepository.insert(keyValueConfig);
        } else {
            keyValueConfigRepository.updateByKey(keyValueConfig);
        }
    }

    public BizResult<List<CommonSettingView>> get() {
        List<CommonSettingView> commonSettingViews = new ArrayList<>();
        for (CommonSettingType value : CommonSettingType.values()) {
            CommonSettingView commonSettingView = get(value);
            if (commonSettingView != null) {
                commonSettingViews.add(commonSettingView);
            } else {
                log.error("CommonSettingType {} not found in keyValueConfigs", value);
            }
        }
        return BizResult.success(commonSettingViews);
    }

    public CommonSettingView get(CommonSettingType type) {
        log.info("CommonSettingGet type: {}", type);
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(type.getKey());
        if (keyValueConfig == null) {
            return null;
        }
        CommonSettingView commonSettingView = new CommonSettingView().setUpdater(keyValueConfig.getUpdater())
                .setUpdatedAt(keyValueConfig.getUpdatedAt())
                .setKey(type.getKey());
        if (type == CommonSettingType.HOME_PAGE_BGM || type == CommonSettingType.CITY_BGM) {
            commonSettingView.setContent(CdnUtil.getDefaultDomainWithBackSlash() + keyValueConfig.getValue());
        } else {
            commonSettingView.setContent(keyValueConfig.getValue());
        }
        log.info("CommonSettingGet commonSettingView: {}", commonSettingView);
        return commonSettingView;
    }
}
