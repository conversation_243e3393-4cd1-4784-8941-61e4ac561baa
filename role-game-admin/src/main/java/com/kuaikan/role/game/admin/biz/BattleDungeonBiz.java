package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import com.kuaikan.comic.bean.Topic;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.service.TopicService;
import com.kuaikan.game.common.util.PageUtil;
import com.kuaikan.role.game.admin.model.param.BattleDungeonAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.BattlePageQueryParam;
import com.kuaikan.role.game.admin.model.view.battle.BattleDungeonConfigView;
import com.kuaikan.role.game.admin.model.view.battle.BizResultWithHost;
import com.kuaikan.role.game.admin.model.view.battle.PageWrapper;
import com.kuaikan.role.game.admin.repository.BattleActivityRepo;
import com.kuaikan.role.game.admin.utils.HostUtil;
import com.kuaikan.role.game.api.bean.cardbattle.BattleDungeonConfig;
import com.kuaikan.role.game.api.constant.CardBattleResponseCodeMsg;
import com.kuaikan.role.game.api.service.cardbattle.CardBattleDataFixService;

@Slf4j
@Service
public class BattleDungeonBiz {

    @Resource
    private BattleActivityRepo battleActivityRepo;

    @Resource
    private TopicService topicService;

    @Resource
    private CardBattleDataFixService cardBattleDataFixService;

    public BizResult<PageWrapper<BattleDungeonConfigView>> listBattleActivity(BattlePageQueryParam query) {
        PageWrapper<BattleDungeonConfigView> result;
        if (StringUtils.isNotBlank(query.getId())) {
            BattleDungeonConfig activity = battleActivityRepo.getBattleActivityById(query.getId());
            if (activity == null) {
                result = PageWrapper.of(0, Lists.newArrayList());
            } else {
                BattleDungeonConfigView activityView = BattleDungeonConfigView.convert(activity);
                List<String> topicNames = activityView.getTopicIds()
                        .stream()
                        .map(topicId -> topicService.getTopicById(topicId).getTitle())
                        .collect(Collectors.toList());
                activityView.setTopicNames(topicNames);
                result = PageWrapper.of(1, Lists.newArrayList(activityView));
            }
        } else {
            int page = query.getPage();
            int pageSize = query.getPageSize();
            int type = query.getType();
            int total = battleActivityRepo.countBattleActivityWithType(type);
            List<BattleDungeonConfig> activities = battleActivityRepo.listBattleActivity(pageSize, page, type);
            int totalPage = PageUtil.getTotalPage(total, pageSize);
            List<BattleDungeonConfigView> activityDtos = Lists.newArrayList();
            for (BattleDungeonConfig activity : activities) {
                BattleDungeonConfigView activityView = BattleDungeonConfigView.convert(activity);
                List<String> topicNames = activityView.getTopicIds().stream().map(topicId -> {
                    Topic topic = topicService.getTopicById(topicId);
                    return Optional.ofNullable(topic).map(Topic::getTitle).orElse("");
                }).collect(Collectors.toList());
                activityView.setTopicNames(topicNames);
                activityDtos.add(activityView);
            }
            result = PageWrapper.of(totalPage, Lists.newArrayList(activityDtos));
        }
        return BizResult.success(result);
    }

    public BizResultWithHost<BattleDungeonConfigView> getBattleActivityById(String id) {
        BattleDungeonConfig battleDungeonConfig = battleActivityRepo.getBattleActivityById(id);
        if (battleDungeonConfig == null) {
            return BizResultWithHost.result(CardBattleResponseCodeMsg.CARD_BATTLE_NO_FOUND_COPY_ACTIVITY_CONFIG);
        }
        BattleDungeonConfigView activityView = BattleDungeonConfigView.convert(battleDungeonConfig);
        List<String> topicNames = activityView.getTopicIds().stream().map(topicId -> {
            Topic topic = topicService.getTopicById(topicId);
            return Optional.ofNullable(topic).map(Topic::getTitle).orElse("");
        }).collect(Collectors.toList());
        activityView.setTopicNames(topicNames);
        return BizResultWithHost.success(activityView).setHost(HostUtil.getHost());
    }

    public BizResult<Void> createBattleActivity(BattleDungeonAddOrUpdateParam request) {
        BattleDungeonConfig battleDungeonConfig = request.toBattleActivity();
        List<Long> lotteryIds = extractLotteryId(battleDungeonConfig);
        // add 防刷
        lotteryIds.forEach(activityId -> cardBattleDataFixService.addAntiBrushFlag(0, activityId));
        // 新副本管理status不用了，默认是1（有效的）
        battleDungeonConfig.setStatus(1);
        battleActivityRepo.createBattleActivity(battleDungeonConfig);
        log.info("Create battleCopyActivity success, battleCopyActivityId:{}, battleCopyActivityInfo:{}, Operator:{}", battleDungeonConfig.getId(),
                battleDungeonConfig, battleDungeonConfig.getOperator());
        Boolean ret = cardBattleDataFixService.setDungeonProbPrizeCache(battleDungeonConfig.getId());
        if (!Boolean.TRUE.equals(ret)) {
            log.error("createBattleDungeon setDungeonProbPrizeCache fail, battleCopyActivityId:{}", battleDungeonConfig.getId());
        }
        return BizResult.success();
    }

    private List<Long> extractLotteryId(BattleDungeonConfig activity) {
        List<Long> lotteryIds = Lists.newArrayList();
        if (activity.getBattleTaskPrizeConf() != null && activity.getBattleTaskPrizeConf().getLotteryId() > 0) {
            lotteryIds.add((long) activity.getBattleTaskPrizeConf().getLotteryId());
        }
        if (activity.getAwardTaskPrizeConf() != null && activity.getAwardTaskPrizeConf().getLotteryId() > 0) {
            lotteryIds.add((long) activity.getAwardTaskPrizeConf().getLotteryId());
        }
        if (CollectionUtils.isNotEmpty(activity.getSpecTasks())) {
            for (BattleDungeonConfig.SpecTask specTask : activity.getSpecTasks()) {
                if (specTask.getPrizeConf() != null && specTask.getPrizeConf().getLotteryId() > 0) {
                    lotteryIds.add((long) specTask.getPrizeConf().getLotteryId());
                }
            }
        }
        return lotteryIds;
    }

    public BizResult<Void> updateBattleActivity(BattleDungeonAddOrUpdateParam request) {
        BattleDungeonConfig oldActivity = battleActivityRepo.getBattleActivityById(request.getId());
        if (oldActivity == null) {
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_NO_FOUND_COPY_ACTIVITY_CONFIG);
        }
        BattleDungeonConfig battleDungeonConfig = request.toBattleActivity();
        List<Long> lotteryIds = extractLotteryId(battleDungeonConfig);
        // add 防刷
        lotteryIds.forEach(activityId -> cardBattleDataFixService.addAntiBrushFlag(0, activityId));
        cardBattleDataFixService.deleteActivityProbPrizeCache(request.getId());
        boolean ret = battleActivityRepo.updateBattleActivity(battleDungeonConfig);
        if (ret) {
            log.info("Update battleCopyActivity success, battleCopyActivityId:{}, battleCopyActivityInfo:{}, Operator:{}", battleDungeonConfig.getId(),
                    battleDungeonConfig, battleDungeonConfig.getOperator());
            Boolean cacheRet = cardBattleDataFixService.setDungeonProbPrizeCache(battleDungeonConfig.getId());
            if (!Boolean.TRUE.equals(cacheRet)) {
                log.error("updateBattleActivity setDungeonProbPrizeCache fail, battleCopyActivityId:{}", battleDungeonConfig.getId());
            }
            return BizResult.success();
        } else {
            log.error("Update battleCopyActivity failed, battleCopyActivityId:{}, battleCopyActivityInfo:{}, Operator:{}", battleDungeonConfig.getId(),
                    battleDungeonConfig, battleDungeonConfig.getOperator());
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_COPY_ACTIVITY_UPDATE_FAIL);
        }
    }

    public BizResult<Void> updateBattleActivityOrder(String id, Integer order) {
        BattleDungeonConfig oldActivity = battleActivityRepo.getBattleActivityById(id);
        if (oldActivity == null) {
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_NO_FOUND_COPY_ACTIVITY_CONFIG);
        }
        battleActivityRepo.updateBattleActivityOrder(id, order);
        return BizResult.success();
    }
}
