package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.admin.component.QiniuComponent.FREE_BUCKET_NAME;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.jetbrains.annotations.NotNull;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.common.MaterialConstants;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.component.MaterialComponent;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.dao.mongo.LetterContentDAO;
import com.kuaikan.role.game.admin.exception.FileRequirementNotMetException;
import com.kuaikan.role.game.admin.model.excel.ActionStoryConfigData;
import com.kuaikan.role.game.admin.model.param.MaterialParam;
import com.kuaikan.role.game.admin.model.param.StoryAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.StoryPreviewParam;
import com.kuaikan.role.game.admin.model.param.StoryProbabilityAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.ImageInfoView;
import com.kuaikan.role.game.admin.model.view.MaterialView;
import com.kuaikan.role.game.admin.model.view.StoryHomeView;
import com.kuaikan.role.game.admin.model.view.StoryIdNameView;
import com.kuaikan.role.game.admin.model.view.StoryIdNameView;
import com.kuaikan.role.game.admin.model.view.StoryProbabilityView;
import com.kuaikan.role.game.admin.model.view.StoryView;
import com.kuaikan.role.game.admin.model.view.story.ActionDetailPreviewView;
import com.kuaikan.role.game.admin.model.view.story.CheckActionStoryConfigView;
import com.kuaikan.role.game.admin.model.view.story.IPreviewDetailView;
import com.kuaikan.role.game.admin.model.view.story.LetterDetailPreviewView;
import com.kuaikan.role.game.admin.repository.AvgRepository;
import com.kuaikan.role.game.admin.repository.CostumeRepository;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.admin.repository.MaterialRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.repository.ScheduleRepository;
import com.kuaikan.role.game.admin.repository.StoryRepository;
import com.kuaikan.role.game.admin.repository.StoryRoleRelationRepository;
import com.kuaikan.role.game.admin.template.numerical.NumericalTemplate;
import com.kuaikan.role.game.admin.template.numerical.NumericalTemplateFactory;
import com.kuaikan.role.game.admin.utils.FileUtils;
import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.api.bean.Material;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.Schedule;
import com.kuaikan.role.game.api.bean.Story;
import com.kuaikan.role.game.api.bean.StoryActionConfig;
import com.kuaikan.role.game.api.bean.StoryAvgConfig;
import com.kuaikan.role.game.api.bean.StoryLetter;
import com.kuaikan.role.game.api.bean.StoryLetterConfig;
import com.kuaikan.role.game.api.bean.StoryPhotoConfig;
import com.kuaikan.role.game.api.bean.StoryProbability;
import com.kuaikan.role.game.api.bean.StoryRoleRelation;
import com.kuaikan.role.game.api.bean.StoryVoiceConfig;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.AnimationConfigType;
import com.kuaikan.role.game.api.enums.AnimationJointType;
import com.kuaikan.role.game.api.enums.AvgUnlockCondition;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.api.enums.LetterDialogueType;
import com.kuaikan.role.game.api.enums.MaterialBizType;
import com.kuaikan.role.game.api.enums.NumericalConfigType;
import com.kuaikan.role.game.api.enums.StoryDisplayEnum;
import com.kuaikan.role.game.api.enums.StoryType;
import com.kuaikan.role.game.api.model.StoryLetterContent;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;
import com.kuaikan.role.game.common.enums.CacheConfig;

/**
 * StoryBiz
 *
 * @version 2024-03-12
 */
@Service
@Slf4j
public class StoryBiz {

    public static final String STORY_TMP_FILE_KEY = "role/game/story/%d/";
    private static final int EXCEL_COLUMN_ROLE_ID = 0;
    /**
     * excel 中 对话角色名称列
     */
    private static final int EXCEL_COLUMN_ROLE_NAME_NUM = 1;
    /**
     * excel 中 对话内容列
     */
    private static final int EXCEL_COLUMN_CONTENT_NUM = 2;
    /**
     * excel 中 对话类型列
     */
    private static final int EXCEL_COLUMN_TYPE = 3;
    /**
     * 所有列
     */
    private static final List<Integer> ALL_COLUMN = Lists.newArrayList(EXCEL_COLUMN_ROLE_ID, EXCEL_COLUMN_ROLE_NAME_NUM, EXCEL_COLUMN_CONTENT_NUM,
            EXCEL_COLUMN_TYPE);

    @Resource
    private StoryRepository storyRepository;
    @Resource
    private StoryRoleRelationRepository storyRoleRelationRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private CostumeRepository costumeRepository;
    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;
    @Resource
    private QiniuComponent qiniuComponent;
    @Resource
    private LetterContentDAO letterContentDAO;
    @Resource
    private MaterialComponent materialComponent;
    @Resource
    private MaterialRepository materialRepository;
    @Resource
    private ScheduleRepository scheduleRepository;
    @Resource
    private NumericalTemplateFactory numericalTemplateFactory;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private AvgRepository avgRepository;

    public BizResult<Void> downloadLetterTemplate(HttpServletResponse response) {
        String resourcePath = "template/xinjianjuqing-muban.zip";
        org.springframework.core.io.Resource resource = new ClassPathResource(resourcePath);
        FileUtils.downloadFile(response, resource);
        return BizResult.success();
    }

    public BizResult<List<StoryView>> all() {
        final List<Story> stories = storyRepository.queryAll();
        final Map<Integer, List<StoryRoleRelation>> storyRoleMap = storyRoleRelationRepository.selectByStoryIds(
                stories.stream().map(Story::getId).collect(Collectors.toSet())).stream().collect(Collectors.groupingBy(StoryRoleRelation::getStoryId));

        return BizResult.success(stories.stream().map(story -> {
            StoryView storyView = new StoryView();
            storyView.setId(story.getId());
            storyView.setName(story.getName());
            storyView.setLevel(story.getLevel());
            storyView.setType(story.getType());
            storyView.setStatus(story.getStatus());
            storyView.setCanRepeat(story.isCanRepeat());
            final List<StoryRoleRelation> storyRoleRelations = storyRoleMap.get(story.getId());
            if (CollectionUtils.isNotEmpty(storyRoleRelations)) {
                storyView.setRoleIds(storyRoleRelations.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toList()));
            }
            return storyView;
        }).collect(Collectors.toList()));
    }

    public BizResult<PageView<StoryView>> list(int pageNum, int pageSize, String type, Integer roleId, Integer status) {
        final int offset = (pageNum - 1) * pageSize;
        List<Story> stories = storyRepository.queryByPage(offset, pageSize, type, roleId, status);
        final Set<Integer> storyIds = stories.stream().map(Story::getId).collect(Collectors.toSet());
        final List<StoryRoleRelation> storyRoleRelations = storyRoleRelationRepository.selectByStoryIds(storyIds);
        final Map<Integer, List<StoryRoleRelation>> storyRoleRelationMap = storyRoleRelations.stream()
                .collect(Collectors.groupingBy(StoryRoleRelation::getStoryId));
        Set<Integer> configRoleIds = stories.stream()
                .filter(item -> item.getType().equals(StoryType.ACTION.getCode()))
                .map(Story::getConfig)
                .map(item -> JsonUtils.findObject(item, StoryActionConfig.class))
                .map(StoryActionConfig::getAnimationConfig)
                .filter(Objects::nonNull)
                .map(StoryActionConfig.AnimationConfig::getRoleAnimationConfigs)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(StoryActionConfig.RoleAnimationConfig::getRoleId)
                .collect(Collectors.toSet());
        Set<Integer> relationRoleIds = storyRoleRelations.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toSet());
        relationRoleIds.addAll(configRoleIds);
        final List<Role> roles = roleRepository.queryByIds(relationRoleIds);
        final Map<Integer, Role> roleMap = roles.stream().collect(Collectors.toMap(Role::getId, role -> role));
        final List<Costume> costumes = costumeRepository.queryByIds(stories.stream().map(Story::getRewardCostumeId).collect(Collectors.toSet()));
        final Map<Integer, Costume> costumeMap = costumes.stream().collect(Collectors.toMap(Costume::getId, costume -> costume));
        final List<StoryView> storyViews = stories.stream().map(story -> {
            final StoryView storyView = new StoryView();
            storyView.setId(story.getId());
            storyView.setName(story.getName());
            storyView.setLevel(story.getLevel());
            storyView.setType(story.getType());
            storyView.setStatus(story.getStatus());
            storyView.setScheduleId(story.getScheduleId());
            final List<StoryRoleRelation> storyRoleRelationsByStory = storyRoleRelationMap.get(story.getId());
            if (CollectionUtils.isNotEmpty(storyRoleRelationsByStory)) {
                storyView.setRoleIds(storyRoleRelationsByStory.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toList()));
                storyView.setRoleNames(storyRoleRelationsByStory.stream()
                        .map(storyRoleRelation -> roleMap.get(storyRoleRelation.getRoleId()).getName())
                        .collect(Collectors.toList()));
            }
            MaterialComponent.setMaterial(story, storyView);
            storyView.setRewardCostumeId(story.getRewardCostumeId());
            storyView.setRewardCostumeName(story.getRewardCostumeId() == 0 || !costumeMap.containsKey(story.getRewardCostumeId())
                    ? null
                    : costumeMap.get(story.getRewardCostumeId()).getName());
            storyView.setCanRepeat(story.isCanRepeat());
            final String config = story.getConfig();
            if (StoryType.ACTION.getCode().equals(story.getType()) || StoryType.EGG.getCode().equals(story.getType()) || StoryType.DAILY.getCode()
                    .equals(story.getType())) {
                StoryActionConfig storyActionConfig = JsonUtils.fromJson(config, StoryActionConfig.class);
                storyView.setObtainCopywriting(storyActionConfig.getObtainCopywriting());
                storyView.setCollectionImage(storyActionConfig.getCollectionImage());
                storyView.setThumbnail(storyActionConfig.getThumbnail());
                storyView.setAnimationConfig(storyActionConfig, roleMap);
                storyView.setAvgUnlockingConditions(storyActionConfig.getUnlockingConditions());
            } else if (StoryType.LETTER.getCode().equals(story.getType())) {
                StoryLetterConfig storyLetterConfig = JsonUtils.fromJson(config, StoryLetterConfig.class);
                storyView.setObtainCopywriting(storyLetterConfig.getObtainCopywriting());
                storyView.setCollectionImage(storyLetterConfig.getCollectionImage());
                Integer unlockingCondition = storyLetterConfig.getUnlockingConditions() == null
                        ? AvgUnlockCondition.SCHEDULE_UNLOCK.getCode()
                        : storyLetterConfig.getUnlockingConditions();
                storyView.setAvgUnlockingConditions(unlockingCondition);
            } else if (StoryType.AVG.getCode().equals(story.getType())) {
                StoryAvgConfig storyAvgConfig = JsonUtils.fromJson(config, StoryAvgConfig.class);
                storyView.setAvgChapterId(storyAvgConfig.getAvgChapterId());
                storyView.setAvgUnlockingConditions(storyAvgConfig.getUnlockingConditions());
                storyView.setCollectionImage(storyAvgConfig.getCollectionImage());
                storyView.setBrief(storyAvgConfig.getBrief());
                storyView.setDisplayStatus(storyAvgConfig.getDisplayType());
            } else if (StoryType.VOICE.getCode().equals(story.getType())) {
                StoryVoiceConfig storyVoiceConfig = JsonUtils.fromJson(config, StoryVoiceConfig.class);
                MaterialView materialView = new MaterialView().setName(storyVoiceConfig.getConfigFileName())
                        .setKey(storyVoiceConfig.getConfigFileKey())
                        .setUrl(CdnUtil.getDefaultDomainWithBackSlash() + storyVoiceConfig.getConfigFileKey());
                MaterialView backgroundMaterial = new MaterialView().setKey(storyVoiceConfig.getBackgroundImageKey())
                        .setUrl(CdnUtil.getDefaultDomainWithBackSlash() + storyVoiceConfig.getBackgroundImageKey());
                MaterialView mp3PreviewMaterial = new MaterialView().setKey(storyVoiceConfig.getAuditionAudioFileKey())
                        .setUrl(CdnUtil.getDefaultDomainWithBackSlash() + storyVoiceConfig.getAuditionAudioFileKey());
                storyView.setMp3Material(materialView);
                storyView.setMp3PreviewMaterial(mp3PreviewMaterial);
                storyView.setBackgroundMaterial(backgroundMaterial);
                storyView.setThumbnail(storyVoiceConfig.getCoverImageKey());
                storyView.setBrief(storyVoiceConfig.getBrief());
                storyView.setMp3PreviewDuration(storyVoiceConfig.getAuditionDuration());
                storyView.setDisplayStatus(storyVoiceConfig.getDisplayType());
                storyView.setAvgUnlockingConditions(storyVoiceConfig.getUnlockingConditions());
            } else if (StoryType.PHOTO.getCode().equals(story.getType())) {
                StoryPhotoConfig storyPhotoConfig = JsonUtils.fromJson(config, StoryPhotoConfig.class);
                storyView.setPhotoImage(ImageInfoView.valueOf(storyPhotoConfig.getImageInfo()));
                storyView.setStoryText(storyPhotoConfig.getStoryText());
                storyView.setAvgUnlockingConditions(storyPhotoConfig.getUnlockingConditions());
                storyView.setUnlockComicId(storyPhotoConfig.getUnlockComicId());
            }
            return storyView;
        }).sorted(Comparator.comparingInt(StoryView::getId).reversed()).collect(Collectors.toList());
        final int count = storyRepository.countByCondition(type, roleId, status);
        return BizResult.success(PageView.form(count, storyViews));
    }

    public BizResult<CheckActionStoryConfigView> checkActionStoryConfig(String fileKey) throws IOException {
        if (StringUtils.isBlank(fileKey)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件key不能为空");
        }
        List<CheckActionStoryConfigView.RoleAnimationConfigView> roleAnimationConfigs = new ArrayList<>();
        String fileUrl = CdnUtil.getDefaultDomainWithBackSlash() + fileKey;
        String outFilePath = FileUtils.downloadFile(fileUrl);
        try {
            if (StringUtils.isNotBlank(outFilePath)) {
                NumericalTemplate<ActionStoryConfigData> instance = numericalTemplateFactory.getInstance(NumericalConfigType.ACTION_STORY_CONFIG);
                BizResult<List<FailMessage>> importResult = instance.importNumericalConfig(Files.newInputStream(Paths.get(outFilePath)));
                if (!importResult.isSuccess()) {
                    List<FailMessage> failMessages = importResult.getData();
                    CheckActionStoryConfigView checkActionStoryConfigView = new CheckActionStoryConfigView().setFailMessages(failMessages);
                    return BizResult.result(checkActionStoryConfigView, RoleGameResponse.ACTION_STORY_CONFIG_EXCEL_FAIL.getCode(), "");
                }
                List<ActionStoryConfigData> excelDataList = instance.getExcelDataList();
                Map<String, List<ActionStoryConfigData>> roleConfig = excelDataList.stream().collect(Collectors.groupingBy(ActionStoryConfigData::getRoleId));
                for (Map.Entry<String, List<ActionStoryConfigData>> roleConfigEntry : roleConfig.entrySet()) {
                    String roleId = roleConfigEntry.getKey();
                    CheckActionStoryConfigView.RoleAnimationConfigView roleAnimationConfig = new CheckActionStoryConfigView.RoleAnimationConfigView();
                    roleAnimationConfig.setRoleId(Integer.parseInt(roleId));
                    List<ActionStoryConfigData> actionStoryConfigDataList = roleConfigEntry.getValue();
                    List<CheckActionStoryConfigView.DialogueConfigView> dialogueConfigs = actionStoryConfigDataList.stream()
                            .map(ActionStoryConfigData::valueOf)
                            .collect(Collectors.toList());
                    roleAnimationConfig.setDialogueConfigs(dialogueConfigs);
                    roleAnimationConfigs.add(roleAnimationConfig);
                }
            }
            CheckActionStoryConfigView checkActionStoryConfigView = new CheckActionStoryConfigView().setRoleAnimationConfigs(roleAnimationConfigs);
            return BizResult.success(checkActionStoryConfigView);
        } finally {
            FileUtils.deleteFile(outFilePath);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(StoryAddOrUpdateParam param) throws FileRequirementNotMetException, IOException {
        final Integer storyId = param.getId();
        final String type = param.getType();
        final StoryType storyType = StoryType.of(type);
        int scheduleId = param.getScheduleId();
        Schedule schedule = scheduleRepository.queryById(scheduleId);
        if (schedule == null && (Objects.equals(param.getType(), StoryType.EGG.getCode()) || (Objects.equals(param.getType(), StoryType.LETTER.getCode())
                && param.getAvgUnlockingConditions() == AvgUnlockCondition.SCHEDULE_UNLOCK.getCode()))) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "日程不存在");
        }
        if (param.getAnimationConfig() != null && param.getAnimationConfig().getRoleGroupId() != null && param.getAnimationConfig().getRoleGroupId() > 0) {
            List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupId(param.getAnimationConfig().getRoleGroupId());
            List<Integer> roleIds = roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList());
            param.setRoleIds(roleIds);
        }
        if (storyId != null && storyId > 0) {
            return doUpdate(param, storyType);
        } else {
            return doInsert(param, storyType);
        }
    }

    private void upsertLetterContent(MaterialParam zipMaterial, Integer storyId, Story story) throws FileRequirementNotMetException {
        final String materialKey = zipMaterial.getKey();
        final String outFilePath = MaterialConstants.TMP_STORY + System.currentTimeMillis() + storyId;
        // 获取 config 信息
        // 信件剧情，不计算 md5
        try {
            // 解压整体 zip 到本地
            final List<File> files = materialComponent.unzipMaterial(materialKey, outFilePath);
            if (CollectionUtils.isEmpty(files)) {
                throw new FileRequirementNotMetException("解压为空或解压失败");
            }
            // 上传解压后的文件，主要是里面的图片文件需要上传
            final Map<String, String> file2UriMap = uploadFiles(files, String.format(STORY_TMP_FILE_KEY, System.currentTimeMillis()));
            final Optional<File> excelFileOptional = files.stream().filter(file -> file.getName().endsWith(".xlsx") && !file.isHidden()).findAny();
            if (!excelFileOptional.isPresent()) {
                throw new FileRequirementNotMetException("未找到excel文件");
            }
            final File excelFile = excelFileOptional.get();
            try (FileInputStream fis = new FileInputStream(excelFile)) {
                // 解析 excel
                StoryLetterContent storyLetterContent = readExcel(fis, file2UriMap);
                // 信件具体信息，存入 mongo
                letterContentDAO.upsert(story.getId(), storyLetterContent, zipMaterial.getMd5());
                Operation.of(OperationConstants.OperateSubType.STORY_LETTER_UPSERT)
                        .add("storyId", story.getId())
                        .add("material", JsonUtils.writeValueAsString(zipMaterial));
            } catch (Exception e) {
                log.error("读取excel文件失败，file2UriMap:{}", file2UriMap, e);
                throw new FileRequirementNotMetException(e.getMessage());
            }
        } finally {
            FileUtils.deleteDirectory(outFilePath);
        }
    }

    private BizResult<Void> doInsert(StoryAddOrUpdateParam param, StoryType storyType) throws FileRequirementNotMetException {
        final String name = param.getName().trim();
        Story story = new Story();
        // 下载文件
        final MaterialParam zipMaterial = param.getZipMaterial();
        String config = null;
        int zipMaterialId = 0;
        if (storyType == StoryType.LETTER) {
            StoryLetterConfig letterConfig = new StoryLetterConfig();
            letterConfig.setObtainCopywriting(param.getObtainCopywriting());
            letterConfig.setZipMaterialMd5(param.getZipMaterial().getMd5());
            letterConfig.setCollectionImage(param.getCollectionImg());
            letterConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            config = JsonUtils.toJson(letterConfig);
        } else if (storyType == StoryType.ACTION || storyType == StoryType.EGG || storyType == StoryType.DAILY) {
            StoryActionConfig actionConfig = new StoryActionConfig();
            if (param.getZipMaterial() != null) {
                zipMaterialId = materialComponent.dealMaterial(0, zipMaterial);
                actionConfig.setZipMaterialId(zipMaterialId);
            }
            if (StringUtils.isNotBlank(param.getBackgroundFileKey())) {
                actionConfig.setBackgroundFileKey(param.getBackgroundFileKey());
            }
            if (StringUtils.isNotBlank(param.getBgmFileKey())) {
                actionConfig.setBgmFileKey(param.getBgmFileKey());
            }
            if (StringUtils.isNotBlank(param.getDialogueFileKey())) {
                actionConfig.setDialogueFileKey(param.getDialogueFileKey());
            }
            BizResult<StoryActionConfig.AnimationConfig> animationConfigBizResult = delActionOrEggConfigAnimationConfig(param, storyType.getCode());
            if (animationConfigBizResult.getCode() != ResponseCodeMsg.SUCCESS.getCode()) {
                return BizResult.result(animationConfigBizResult.getCode(), animationConfigBizResult.getMessage());
            }
            actionConfig.setAnimationConfig(animationConfigBizResult.getData());
            actionConfig.setThumbnail(param.getThumbnail());
            actionConfig.setObtainCopywriting(param.getObtainCopywriting());
            actionConfig.setCollectionImage(param.getCollectionImg());
            actionConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            config = JsonUtils.toJson(actionConfig);
        } else if (storyType == StoryType.AVG) {
            AvgChapter avgChapter = avgRepository.queryAvgChapterByChapterId(param.getAvgChapterId());
            if (avgChapter == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "剧情段落ID不存在");
            }
            StoryAvgConfig storyAvgConfig = new StoryAvgConfig();
            storyAvgConfig.setAvgChapterId(param.getAvgChapterId());
            storyAvgConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            storyAvgConfig.setCollectionImage(param.getCollectionImg());
            storyAvgConfig.setDisplayType(param.getDisplayStatus());
            if (param.getDisplayStatus() != null && param.getDisplayStatus() == StoryDisplayEnum.SECRET_STORY.getCode()) {
                if (StringUtils.isNotBlank(param.getBrief())) {
                    storyAvgConfig.setBrief(param.getBrief());
                }
            }
            config = JsonUtils.toJson(storyAvgConfig);
        } else if (storyType == StoryType.VOICE) {
            StoryVoiceConfig storyVoiceConfig = new StoryVoiceConfig();
            storyVoiceConfig.setConfigFileKey(param.getMp3Config().getConfigFileKey());
            storyVoiceConfig.setConfigFileName(param.getMp3Config().getConfigFileName());
            storyVoiceConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            storyVoiceConfig.setDisplayType(param.getDisplayStatus());
            if (param.getDisplayStatus() != null && param.getDisplayStatus() == StoryDisplayEnum.BOND_WHISPER.getCode()) {
                storyVoiceConfig.setCoverImageKey(param.getThumbnail());
                storyVoiceConfig.setBackgroundImageKey(param.getBackgroundFileKey());
                storyVoiceConfig.setBrief(param.getBrief());
                storyVoiceConfig.setAuditionAudioFileKey(param.getAuditionAudioFileKey());
                storyVoiceConfig.setAuditionDuration(param.getAuditionDuration());
            }
            config = JsonUtils.toJson(storyVoiceConfig);
        } else if (storyType == StoryType.PHOTO) {
            StoryPhotoConfig storyPhotoConfig = new StoryPhotoConfig();
            storyPhotoConfig.setImageInfo(param.getPhotoConfig().getImage());
            storyPhotoConfig.setStoryText(param.getPhotoConfig().getStoryText());
            storyPhotoConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            storyPhotoConfig.setUnlockComicId(param.getUnlockComicId());
            config = JsonUtils.toJson(storyPhotoConfig);
        }
        story.setName(name);
        story.setConfig(config);
        story.setLevel(param.getLevel());
        story.setCanRepeat(param.isCanRepeat());
        story.setRewardCostumeId(param.getRewardCostumeId());
        story.setType(storyType.getCode());
        story.setScheduleId(param.getScheduleId());
        storyRepository.insert(story);
        // 插入关联角色
        storyRoleRelationRepository.insertBatch(
                param.getRoleIds().stream().map(roleId -> new StoryRoleRelation().setStoryId(story.getId()).setRoleId(roleId)).collect(Collectors.toList()));
        if (storyType == StoryType.LETTER) {
            upsertLetterContent(zipMaterial, story.getId(), story);
        } else if (storyType == StoryType.ACTION || storyType == StoryType.DAILY) {
            Material.BizInfo bizInfo = new Material.BizInfo().setStoryId(story.getId()).setType(MaterialBizType.STORY.getCode());
            Material material = new Material().setId(zipMaterialId).setBizInfo(bizInfo);
            materialRepository.updateByPrimaryKeySelective(material);
        }
        deleteCache(story.getId());
        deleteRelationCache(param.getRoleIds());
        Operation operation = Operation.of(OperationConstants.OperateSubType.STORY_ADD).add("newData", JsonUtils.writeValueAsString(story));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    private BizResult<Void> doUpdate(StoryAddOrUpdateParam param, StoryType storyType) throws FileRequirementNotMetException {
        Story story = storyRepository.selectByPrimaryKey(param.getId());
        if (story == null) {
            log.warn("剧情不存在, id:{}", param.getId());
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "剧情不存在");
        }
        // 下载文件
        final MaterialParam zipMaterial = param.getZipMaterial();
        Operation operation = Operation.of(OperationConstants.OperateSubType.STORY_UPDATE).add("oldData", JsonUtils.writeValueAsString(story));
        String config = null;
        if (storyType == StoryType.LETTER) {
            StoryLetterConfig letterConfig = JsonUtils.findObject(story.getConfig(), StoryLetterConfig.class);
            letterConfig.setObtainCopywriting(param.getObtainCopywriting());
            if (param.getZipMaterial() != null) {
                letterConfig.setZipMaterialMd5(param.getZipMaterial().getMd5());
            }
            letterConfig.setCollectionImage(param.getCollectionImg());
            letterConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            config = JsonUtils.toJson(letterConfig);
        } else if (storyType == StoryType.ACTION || storyType == StoryType.EGG || storyType == StoryType.DAILY) {
            final String originConfig = story.getConfig();
            final StoryActionConfig actionConfig = JsonUtils.findObject(originConfig, StoryActionConfig.class);
            if (StringUtils.isNotBlank(param.getBackgroundFileKey())) {
                actionConfig.setBackgroundFileKey(param.getBackgroundFileKey());
            } else {
                actionConfig.setBackgroundFileKey(null);
            }
            if (StringUtils.isNotBlank(param.getBgmFileKey())) {
                actionConfig.setBgmFileKey(param.getBgmFileKey());
            } else {
                actionConfig.setBgmFileKey(null);
            }
            if (StringUtils.isNotBlank(param.getDialogueFileKey())) {
                actionConfig.setDialogueFileKey(param.getDialogueFileKey());
            } else {
                actionConfig.setDialogueFileKey(null);
            }
            BizResult<StoryActionConfig.AnimationConfig> animationConfigBizResult = delActionOrEggConfigAnimationConfig(param, story.getType());
            if (animationConfigBizResult.getCode() != ResponseCodeMsg.SUCCESS.getCode()) {
                return BizResult.result(animationConfigBizResult.getCode(), animationConfigBizResult.getMessage());
            }
            actionConfig.setAnimationConfig(animationConfigBizResult.getData());
            actionConfig.setThumbnail(param.getThumbnail());
            actionConfig.setObtainCopywriting(param.getObtainCopywriting());
            actionConfig.setCollectionImage(param.getCollectionImg());
            actionConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            config = JsonUtils.toJson(actionConfig);
        } else if (storyType == StoryType.AVG) {
            AvgChapter avgChapter = avgRepository.queryAvgChapterByChapterId(param.getAvgChapterId());
            if (avgChapter == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "剧情段落ID不存在");
            }
            StoryAvgConfig avgConfig = JsonUtils.findObject(story.getConfig(), StoryAvgConfig.class);
            avgConfig.setAvgChapterId(param.getAvgChapterId());
            avgConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            avgConfig.setCollectionImage(param.getCollectionImg());
            avgConfig.setDisplayType(param.getDisplayStatus());
            avgConfig.setBrief(param.getBrief());
            config = JsonUtils.toJson(avgConfig);
        } else if (storyType == StoryType.VOICE) {
            StoryVoiceConfig storyVoiceConfig = JsonUtils.findObject(story.getConfig(), StoryVoiceConfig.class);
            storyVoiceConfig.setConfigFileKey(param.getMp3Config().getConfigFileKey());
            storyVoiceConfig.setConfigFileName(param.getMp3Config().getConfigFileName());
            storyVoiceConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            storyVoiceConfig.setCoverImageKey(param.getThumbnail());
            storyVoiceConfig.setBackgroundImageKey(param.getBackgroundFileKey());
            storyVoiceConfig.setBrief(param.getBrief());
            storyVoiceConfig.setAuditionAudioFileKey(param.getAuditionAudioFileKey());
            storyVoiceConfig.setAuditionDuration(param.getAuditionDuration());
            storyVoiceConfig.setDisplayType(param.getDisplayStatus());
            config = JsonUtils.toJson(storyVoiceConfig);
        } else if (storyType == StoryType.PHOTO) {
            StoryPhotoConfig storyPhotoConfig = JsonUtils.findObject(story.getConfig(), StoryPhotoConfig.class);
            storyPhotoConfig.setImageInfo(param.getPhotoConfig().getImage());
            storyPhotoConfig.setStoryText(param.getPhotoConfig().getStoryText());
            storyPhotoConfig.setUnlockingConditions(param.getAvgUnlockingConditions());
            storyPhotoConfig.setUnlockComicId(param.getUnlockComicId());
            config = JsonUtils.toJson(storyPhotoConfig);
        }
        story.setName(param.getName());
        story.setConfig(config);
        story.setLevel(param.getLevel());
        story.setCanRepeat(param.isCanRepeat());
        story.setRewardCostumeId(param.getRewardCostumeId());
        story.setScheduleId(param.getScheduleId());
        storyRepository.updateByPrimaryKeySelective(story);
        List<StoryRoleRelation> roleRelations = storyRoleRelationRepository.selectByStoryId(story.getId());
        List<Integer> roleIdCacheList = Lists.newArrayList();
        roleIdCacheList.addAll(Lists.transform(ListUtils.emptyIfNull(roleRelations), StoryRoleRelation::getRoleId));
        storyRoleRelationRepository.deleteByStoryId(story.getId());
        storyRoleRelationRepository.insertBatch(
                param.getRoleIds().stream().map(roleId -> new StoryRoleRelation().setStoryId(story.getId()).setRoleId(roleId)).collect(Collectors.toList()));
        if (storyType == StoryType.LETTER && zipMaterial != null) {
            upsertLetterContent(zipMaterial, story.getId(), story);
        }
        deleteCache(story.getId());
        roleIdCacheList.addAll(param.getRoleIds());
        deleteRelationCache(roleIdCacheList);
        operation.add("newData", JsonUtils.writeValueAsString(story));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<StoryProbabilityView> getStoryProbability() {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.STORY_PROBABILITY);
        if (keyValueConfig == null) {
            return BizResult.success();
        }
        StoryProbability storyProbability = GsonUtils.tryParseObject(keyValueConfig.getValue(), StoryProbability.class);
        return BizResult.success(StoryProbabilityView.valueOf(storyProbability));
    }

    public BizResult<Void> updateStoryProbability(StoryProbabilityAddOrUpdateParam param) {
        if (param.getTriggerProbability() < 0 || param.getTriggerProbability() > 100) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "概率要设置0~100的整数");
        }
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.STORY_PROBABILITY);
        StoryProbability storyProbability = new StoryProbability();
        BeanUtils.copyProperties(param, storyProbability);
        if (keyValueConfig == null) {
            keyValueConfig = new KeyValueConfig().setKey(KeyValueConfigKeys.STORY_PROBABILITY).setValue(GsonUtils.toJson(storyProbability));
            keyValueConfigRepository.insert(keyValueConfig);
        } else {
            keyValueConfig.setValue(GsonUtils.toJson(storyProbability));
            keyValueConfigRepository.updateByKey(keyValueConfig);
        }
        deleteKeyValueCache(KeyValueConfigKeys.STORY_PROBABILITY);
        return BizResult.success();
    }

    public BizResult<PageView<StoryView>> scheduleStoryList(int scheduleId, int pageNum, int pageSize) {
        final int offset = (pageNum - 1) * pageSize;
        final List<Story> stories = storyRepository.queryByPageAndScheduleId(scheduleId, offset, pageSize);
        final Set<Integer> storyIds = stories.stream().map(Story::getId).collect(Collectors.toSet());
        final List<StoryRoleRelation> storyRoleRelations = storyRoleRelationRepository.selectByStoryIds(storyIds);
        final Map<Integer, List<StoryRoleRelation>> storyRoleRelationMap = storyRoleRelations.stream()
                .collect(Collectors.groupingBy(StoryRoleRelation::getStoryId));
        Set<Integer> configRoleIds = stories.stream()
                .filter(item -> item.getType().equals(StoryType.ACTION.getCode()))
                .map(Story::getConfig)
                .map(item -> JsonUtils.findObject(item, StoryActionConfig.class))
                .map(StoryActionConfig::getAnimationConfig)
                .filter(Objects::nonNull)
                .map(StoryActionConfig.AnimationConfig::getRoleAnimationConfigs)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(StoryActionConfig.RoleAnimationConfig::getRoleId)
                .collect(Collectors.toSet());
        Set<Integer> relationRoleIds = storyRoleRelations.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toSet());
        relationRoleIds.addAll(configRoleIds);
        final List<Role> roles = roleRepository.queryByIds(relationRoleIds);
        final Map<Integer, Role> roleMap = roles.stream().collect(Collectors.toMap(Role::getId, role -> role));
        final List<Costume> costumes = costumeRepository.queryByIds(stories.stream().map(Story::getRewardCostumeId).collect(Collectors.toSet()));
        final Map<Integer, Costume> costumeMap = costumes.stream().collect(Collectors.toMap(Costume::getId, costume -> costume));
        final List<StoryView> storyViews = stories.stream().map(story -> {
            final StoryView storyView = new StoryView();
            storyView.setId(story.getId());
            storyView.setName(story.getName());
            storyView.setLevel(story.getLevel());
            storyView.setType(story.getType());
            storyView.setStatus(story.getStatus());
            storyView.setScheduleId(story.getScheduleId());
            final List<StoryRoleRelation> storyRoleRelationsByStory = storyRoleRelationMap.get(story.getId());
            if (CollectionUtils.isNotEmpty(storyRoleRelationsByStory)) {
                storyView.setRoleIds(storyRoleRelationsByStory.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toList()));
                storyView.setRoleNames(storyRoleRelationsByStory.stream()
                        .map(storyRoleRelation -> roleMap.get(storyRoleRelation.getRoleId()).getName())
                        .collect(Collectors.toList()));
            }
            MaterialComponent.setMaterial(story, storyView);
            storyView.setRewardCostumeId(story.getRewardCostumeId());
            storyView.setRewardCostumeName(story.getRewardCostumeId() == 0 || !costumeMap.containsKey(story.getRewardCostumeId())
                    ? null
                    : costumeMap.get(story.getRewardCostumeId()).getName());
            storyView.setCanRepeat(story.isCanRepeat());
            final String config = story.getConfig();
            if (StoryType.ACTION.getCode().equals(story.getType()) || StoryType.EGG.getCode().equals(story.getType()) || StoryType.DAILY.getCode()
                    .equals(story.getType())) {
                StoryActionConfig storyActionConfig = JsonUtils.fromJson(config, StoryActionConfig.class);
                storyView.setObtainCopywriting(storyActionConfig.getObtainCopywriting());
                storyView.setCollectionImage(storyActionConfig.getCollectionImage());
                storyView.setThumbnail(storyActionConfig.getThumbnail());
                storyView.setAnimationConfig(storyActionConfig, roleMap);
            } else if (StoryType.LETTER.getCode().equals(story.getType())) {
                StoryLetterConfig storyLetterConfig = JsonUtils.fromJson(config, StoryLetterConfig.class);
                storyView.setObtainCopywriting(storyLetterConfig.getObtainCopywriting());
                storyView.setCollectionImage(storyLetterConfig.getCollectionImage());
            } else if (StoryType.AVG.getCode().equals(story.getType())) {
                StoryAvgConfig storyAvgConfig = JsonUtils.fromJson(config, StoryAvgConfig.class);
                storyView.setAvgChapterId(storyAvgConfig.getAvgChapterId());
                storyView.setAvgUnlockingConditions(storyAvgConfig.getUnlockingConditions());
                storyView.setCollectionImage(storyAvgConfig.getCollectionImage());
            }
            return storyView;
        }).collect(Collectors.toList());
        final int count = storyRepository.count();
        return BizResult.success(PageView.form(count, storyViews));
    }

    private BizResult<StoryActionConfig.AnimationConfig> delActionOrEggConfigAnimationConfig(StoryAddOrUpdateParam param, String storyCode) {
        if (param.getAnimationConfig() == null) {
            return BizResult.success();
        }
        StoryAddOrUpdateParam.AnimationConfigParam animationConfig = param.getAnimationConfig();
        StoryActionConfig.AnimationConfig animationConfigBean = animationConfig.toBean();
        List<StoryActionConfig.RoleAnimationConfig> roleAnimationConfigs = animationConfigBean.getRoleAnimationConfigs();
        Set<Integer> roleIds = roleAnimationConfigs.stream().map(StoryActionConfig.RoleAnimationConfig::getRoleId).collect(Collectors.toSet());
        if (StoryType.ACTION.getCode().equals(storyCode)) {
            if (!new HashSet<>(param.getRoleIds()).containsAll(roleIds)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "选择的角色和动画配置的角色不一致");
            }
            if (null == animationConfigBean.getAnimationJointConfig().getJointType()) {
                animationConfigBean.getAnimationJointConfig().setJointType(AnimationJointType.COSTUME_ANIMATION.getCode());
            }
            int animationType = animationConfigBean.getType();
            Integer usageScene = animationConfigBean.getScene();
            int animationJointType = animationConfigBean.getAnimationJointConfig().getJointType();
            int roleAnimationConfigsSize = CollectionUtils.size(roleAnimationConfigs);
            if (animationJointType == AnimationJointType.COSTUME_ANIMATION.getCode()) {
                if (AnimationConfigType.SINGLE.getCode() == animationType) {
                    if (roleAnimationConfigsSize != 1) {
                        return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "单人剧情只能有一个角色动画配置");
                    }
                } else if (AnimationConfigType.DOUBLE.getCode() == animationType) {
                    if (roleAnimationConfigsSize != 2) {
                        return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "双人剧情需要两个角色动画配置");
                    }
                }
            }
            if (Objects.equals(StoryType.UsageSceneType.UNKNOWN, StoryType.UsageSceneType.getByCode(usageScene))) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "动作剧情使用场景配置错误");
            }
            if (Objects.equals(AnimationJointType.UNKNOWN, AnimationJointType.getByCode(animationJointType))) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "动作剧情动画拼接方式错误");
            }
            if (Objects.equals(StoryType.UsageSceneType.ROLE_DETAILS_PAGE.getCode(), usageScene) && !Objects.equals(animationType,
                    AnimationConfigType.SINGLE.getCode())) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色详情页的剧情人数只能是单人");
            }
            if (Objects.equals(AnimationJointType.FURNITURE_ANIMATION.getCode(), animationJointType) && (!Objects.equals(
                    StoryType.UsageSceneType.HOME_SCENE_PAGE.getCode(), usageScene) || Objects.isNull(
                    animationConfigBean.getAnimationJointConfig().getFurnitureSpine()))) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具动画拼接只支持在家园场景使用,需要上传家具动画");
            }
            if (Objects.equals(StoryType.UsageSceneType.HOME_SCENE_PAGE.getCode(), usageScene)) {
                animationConfigBean.setSpacingX(-1);
                animationConfigBean.setSpacingY(-1);
            }
        } else if (StoryType.DAILY.getCode().equals(storyCode)) {
            if (!param.getRoleIds().containsAll(roleIds)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "选择的角色和动画配置的角色不一致");
            }
            int roleAnimationConfigsSize = CollectionUtils.size(roleAnimationConfigs);
            int type = animationConfigBean.getType();
            if (AnimationConfigType.SINGLE.getCode() == type) {
                if (roleAnimationConfigsSize != 1) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "单人剧情只能有一个角色动画配置");
                }
            } else if (AnimationConfigType.DOUBLE.getCode() == type) {
                if (roleAnimationConfigsSize != 2) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "双人剧情需要两个角色动画配置");
                }
            }
        } else if (StoryType.EGG.getCode().equals(storyCode)) {
            Integer roleGroupId = animationConfig.getRoleGroupId();
            List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupId(roleGroupId);
            if (CollectionUtils.size(roleGroupRelations) != 2) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组中的角色数量必须为2");
            }
            //找到排序最小的角色
            RoleGroupRelation roleGroupRelation = roleGroupRelations.stream().min(Comparator.comparingInt(RoleGroupRelation::getOrderNum)).get();
            int roleId = roleGroupRelation.getRoleId();
            animationConfigBean.setRoleGroupId(roleGroupId);
            animationConfigBean.setMainRoleId(roleId);
        }
        return BizResult.success(animationConfigBean);
    }

    private void deleteKeyValueCache(String key) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.KEY_VALUE_CONFIG.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.KEY_VALUE_CONFIG.getKeyPattern(), key);
        redisClient.del(cacheKey);
    }

    private void deleteCache(int storyId) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.STORY_INFO.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.STORY_INFO.getKeyPattern(), storyId);
        redisClient.del(cacheKey);
        //删除信件剧情相关缓存
        redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.STORY_BY_SCHEDULE_ID.getReadWriteVip());
        Story story = storyRepository.selectByPrimaryKey(storyId);
        redisClient.del(KeyGenerator.generate(CacheConfig.STORY_BY_SCHEDULE_ID.getKeyPattern(), story.getType(), story.getScheduleId()));
    }

    private void deleteRelationCache(int storyId) {
        List<StoryRoleRelation> roleRelations = storyRoleRelationRepository.selectByStoryId(storyId);
        List<Integer> roleIdCacheList = Lists.transform(ListUtils.emptyIfNull(roleRelations), StoryRoleRelation::getRoleId);
        deleteRelationCache(roleIdCacheList);
    }

    private void deleteRelationCache(List<Integer> roleIds) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.STORY_BY_ROLE_ID.getReadWriteVip());
        if (CollectionUtils.isNotEmpty(roleIds)) {
            List<String> keys = roleIds.stream().map(e -> KeyGenerator.generate(CacheConfig.STORY_BY_ROLE_ID.getKeyPattern(), e)).collect(Collectors.toList());
            redisClient.del(keys.toArray(new String[0]));
        }
    }

    public BizResult<IPreviewDetailView> preview(StoryPreviewParam param, StoryType type) {
        final Integer storyId = param.getStoryId();
        if (type == StoryType.LETTER && storyId != null && storyId > 0) {
            StoryLetter storyLetter = letterContentDAO.findByStoryId(storyId);
            if (storyLetter != null) {
                log.debug("preview param:{} storyLetter:{}", param, storyLetter);
                final StoryLetterContent storyLetterContent = storyLetter.getLetterContent();
                return BizResult.success(LetterDetailPreviewView.valueOf(storyLetterContent));
            }
        }
        final String materialKey = param.getZipMaterial().getKey();
        final String outFilePath = MaterialConstants.TMP_STORY + System.currentTimeMillis() + "/";
        final BizResult<IPreviewDetailView> result;
        try {
            List<File> files = materialComponent.unzipMaterial(materialKey, outFilePath);
            files = files.stream().filter(e -> !e.getPath().contains("MACOSX")).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(files)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "解压为空或解压失败");
            }
            final Map<String, String> file2UriMap = uploadFiles(files, String.format(STORY_TMP_FILE_KEY, System.currentTimeMillis()));
            if (type == StoryType.LETTER) {
                result = getLetterPreviewView(files, file2UriMap);
            } else if (type == StoryType.ACTION) {
                String domain = CdnUtil.getDefaultDomainWithBackSlash();
                final MaterialParam backgroundMaterial = param.getBackgroundMaterial();
                ImageInfo background = null;
                if (backgroundMaterial != null) {
                    String backgroundUrl = domain + backgroundMaterial.getKey();
                    final String outFileName = outFilePath + backgroundMaterial.getName();
                    FileUtils.downloadFile(backgroundUrl, outFileName);
                    try {
                        final BufferedImage backgroundBufferImage = ImageIO.read(new File(outFileName));
                        background = new ImageInfo().setUrl(backgroundMaterial.getKey())
                                .setWidth(backgroundBufferImage.getWidth())
                                .setHeight(backgroundBufferImage.getHeight());
                    } catch (IOException e) {
                        log.error("读取背景图片失败,backgroundUrl:{}", backgroundUrl, e);
                        throw new RuntimeException(e);
                    }
                }
                final MaterialParam bgmMaterial = param.getBgmMaterial();
                String bgmUrl = bgmMaterial == null ? null : domain + bgmMaterial.getKey();
                final MaterialParam dialogMaterial = param.getDialogMaterial();
                String dialogUrl = dialogMaterial == null ? null : dialogMaterial.getKey();
                result = getActionPreviewView(file2UriMap, background, bgmUrl, dialogUrl);
            } else {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "未知的类型:" + type);
            }
        } finally {
            FileUtils.deleteDirectory(outFilePath);
        }
        return result;
    }

    private BizResult<IPreviewDetailView> getActionPreviewView(Map<String, String> file2UriMap, ImageInfo background, String bgmUrl, String dialogUrl) {
        String domain = CdnUtil.getDefaultDomainWithBackSlash();
        ActionDetailPreviewView actionDetailPreviewView = new ActionDetailPreviewView();
        ActionDetailPreviewView.SpinePreviewView spinePreviewView = new ActionDetailPreviewView.SpinePreviewView();
        actionDetailPreviewView.setSpine(spinePreviewView);
        List<ImageInfoView> pngs = new ArrayList<>();
        spinePreviewView.setPngs(pngs);
        for (Map.Entry<String, String> entry : file2UriMap.entrySet()) {
            final String filePath = entry.getKey();
            final String fileUri = entry.getValue();
            if (filePath.endsWith(".atlas") || filePath.endsWith(".atlas.txt")) {
                spinePreviewView.setAtlasUrl(domain + fileUri);
            }
            if (filePath.endsWith(".json")) {
                spinePreviewView.setJsonUrl(domain + fileUri);
            }
            if (filePath.endsWith(".png")) {
                try {
                    final BufferedImage bufferedImage = ImageIO.read(new File(filePath));
                    pngs.add(new ImageInfoView().setUrl(fileUri)
                            .setDisplayUrl(domain + fileUri)
                            .setWidth(bufferedImage.getWidth())
                            .setHeight(bufferedImage.getHeight()));
                } catch (Exception e) {
                    log.error("读取图片失败,path:{}, uri:{}", filePath, fileUri, e);
                    throw new RuntimeException("读取图片失败");
                }
            }
        }
        if (CollectionUtils.isEmpty(spinePreviewView.getPngs()) || StringUtil.isBlank(spinePreviewView.getAtlasUrl()) || StringUtil.isBlank(
                spinePreviewView.getJsonUrl())) {
            log.error("骨骼动画文件不完整,spinePreviewView:{}, file2UriMap:{}", spinePreviewView, file2UriMap);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "骨骼动画文件不完整");
        }

        if (background != null) {
            actionDetailPreviewView.setBackground(ImageInfoView.valueOf(background));
        }
        if (StringUtils.isNotBlank(bgmUrl)) {
            actionDetailPreviewView.setBgmUrl(domain + bgmUrl);
        }
        if (StringUtils.isNotBlank(dialogUrl)) {
            actionDetailPreviewView.setDialogueUrl(domain + dialogUrl);
        }
        return BizResult.success(actionDetailPreviewView);
    }

    @NotNull
    private BizResult<IPreviewDetailView> getLetterPreviewView(List<File> files, Map<String, String> file2UriMap) {
        final Optional<File> excelFileOptional = files.stream().filter(file -> file.getName().endsWith(".xlsx") && !file.isHidden()).findAny();
        if (!excelFileOptional.isPresent()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "未找到excel文件");
        }
        final File excelFile = excelFileOptional.get();
        try (FileInputStream fis = new FileInputStream(excelFile)) {
            final StoryLetterContent storyLetterContent = readExcel(fis, file2UriMap);
            return BizResult.success(LetterDetailPreviewView.valueOf(storyLetterContent));
        } catch (Exception e) {
            log.error("读取excel文件失败，file2UriMap:{}", file2UriMap, e);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), e.getMessage());
        }
    }

    @NotNull
    private static ImageInfo getImageInfo(String filePath, String fileUri) {
        ImageInfo imageInfo = new ImageInfo().setUrl(fileUri);
        try (FileInputStream fis = new FileInputStream(filePath)) {
            final BufferedImage bufferedImage = ImageIO.read(fis);
            imageInfo.setWidth(bufferedImage.getWidth());
            imageInfo.setHeight(bufferedImage.getHeight());
        } catch (IOException e) {
            log.error("读取图片宽高失败,path:{}, uri:{}", filePath, fileUri, e);
            throw new RuntimeException("读取图片宽高失败");
        }
        return imageInfo;
    }

    /**
     * @param files
     * @return key为路径+文件名，value为上传后的文件地址
     */
    private Map<String, String> uploadFiles(List<File> files, String uploadPath) {
        if (CollectionUtils.isEmpty(files)) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMapWithExpectedSize(files.size());
        for (File file : files) {
            if (file.isDirectory()) {
                continue;
            }
            String fileKey = uploadPath + file.getName();
            if (file.getAbsolutePath().contains(MaterialConstants.LETTER_CONTENT_IMAGE_DIR) || file.getAbsolutePath()
                    .contains(MaterialConstants.LETTER_AVATAR_IMAGE_DIR)) {
                fileKey = uploadPath + file.getParentFile().getName() + File.separator + file.getName();
            }
            final String absolutePath = file.getAbsolutePath();
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, absolutePath, fileKey);
            result.put(absolutePath, fileKey);
        }
        return result;
    }

    private StoryLetterContent readExcel(InputStream inputStream, Map<String, String> file2UriMap) throws IOException, InvalidFormatException {

        Map<String, ImageInfo> excelImage2UriMap = file2UriMap.entrySet()
                .stream()
                .filter(entry -> entry.getKey().contains(MaterialConstants.LETTER_CONTENT_IMAGE_DIR))
                .collect(Collectors.toMap(entry -> StringUtils.substringAfterLast(entry.getKey(), MaterialConstants.LETTER_CONTENT_IMAGE_DIR + "/"),
                        entry -> getImageInfo(entry.getKey(), entry.getValue())));
        Workbook workbook = WorkbookFactory.create(inputStream);
        final Sheet sheet = workbook.getSheetAt(0);
        int firstRowNum = sheet.getFirstRowNum() + 1;
        int lastRowNum = sheet.getLastRowNum();
        StoryLetterContent storyLetterContent = new StoryLetterContent();
        List<StoryLetterContent.LetterDialogue> letterDialogueContents = new ArrayList<>();
        storyLetterContent.setLetterDialogues(letterDialogueContents);
        Set<Integer> roleIds = new HashSet<>();
        // 获取对话内容及相关图片
        for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
            Row row = sheet.getRow(rowNum);
            log.info("row:{}, column1:{}, column2:{}, column3:{}, column4:{}", rowNum, row.getCell(0), row.getCell(1), row.getCell(2), row.getCell(3));
            if (row == null || ALL_COLUMN.stream().allMatch(column -> row.getCell(column).getCellTypeEnum() == CellType.BLANK)) {
                break;
            }
            int roleId = (int) row.getCell(EXCEL_COLUMN_ROLE_ID).getNumericCellValue();
            String roleName = row.getCell(EXCEL_COLUMN_ROLE_NAME_NUM).getStringCellValue();
            if (StringUtils.isBlank(roleName)) {
                log.error("对话角色名称为空,rowNum:{}, roleId:{}", rowNum, roleId);
                throw new IllegalArgumentException("对话角色名称为空");
            }
            roleIds.add(roleId);
            String content = row.getCell(EXCEL_COLUMN_CONTENT_NUM).getStringCellValue();
            if (StringUtils.isBlank(content)) {
                log.error("对话内容为空,rowNum:{}, roleName:{}, content:{}", rowNum, roleName, content);
                throw new IllegalArgumentException("对话内容为空");
            }
            String type = row.getCell(EXCEL_COLUMN_TYPE).getStringCellValue();
            final LetterDialogueType letterDialogueType = LetterDialogueType.ofType(type);
            if (letterDialogueType == null) {
                log.error("对话类型错误,rowNum:{}, roleName:{}, content:{} type:{}", rowNum, roleName, content, type);
                throw new IllegalArgumentException("对话类型错误:" + type);
            }
            StoryLetterContent.LetterDialogue letterDialogue = new StoryLetterContent.LetterDialogue();
            letterDialogue.setRoleId(roleId);
            letterDialogue.setRoleName(roleName);
            letterDialogue.setType(letterDialogueType.getCode());
            if (letterDialogueType == LetterDialogueType.TEXT) {
                StoryLetterContent.TextDialogueContent textDialogueContent = new StoryLetterContent.TextDialogueContent();
                textDialogueContent.setText(content);
                letterDialogue.setLetterDialogueContent(textDialogueContent);
            } else if (letterDialogueType == LetterDialogueType.IMAGE) {
                final ImageInfo imageInfo = excelImage2UriMap.get(content);
                if (imageInfo == null) {
                    log.error("未找到对话图片, content:{}", content);
                    throw new IllegalArgumentException("未找到对话图片:" + content);
                }
                StoryLetterContent.ImageDialogueContent imageContent = new StoryLetterContent.ImageDialogueContent().setImage(imageInfo);
                letterDialogue.setLetterDialogueContent(imageContent);
            } else if (letterDialogueType == LetterDialogueType.OPTION) {
                StoryLetterContent.OptionDialogueContent optionDialogueContent = new StoryLetterContent.OptionDialogueContent();
                optionDialogueContent.setOptions(JsonUtils.findList(content, String.class));
                letterDialogue.setLetterDialogueContent(optionDialogueContent);
            }
            letterDialogueContents.add(letterDialogue);
        }

        // 获取头像图片
        List<StoryLetterContent.RoleAvatar> roleAvatars = new ArrayList<>();
        storyLetterContent.setRoleAvatars(roleAvatars);
        Map<String, ImageInfo> avatar2UriMap = file2UriMap.entrySet()
                .stream()
                .filter(entry -> entry.getKey().contains(MaterialConstants.LETTER_AVATAR_IMAGE_DIR))
                .collect(Collectors.toMap(entry -> StringUtils.substringAfterLast(entry.getKey(), MaterialConstants.LETTER_AVATAR_IMAGE_DIR + "/"),
                        entry -> getImageInfo(entry.getKey(), entry.getValue())));
        // 头像图片/roleName.png or 头像图片/roleName.jpg
        roleIds.forEach(roleId -> {
            ImageInfo roleNameImage = avatar2UriMap.get(roleId + ".png");
            if (roleNameImage == null) {
                roleNameImage = avatar2UriMap.get(roleId + ".jpg");
            }
            if (roleNameImage == null) {
                log.error("未找到角色头像图片,roleName:{}", roleId);
                return;
            }
            StoryLetterContent.RoleAvatar roleAvatarView = new StoryLetterContent.RoleAvatar();
            roleAvatarView.setRoleId(roleId);
            roleAvatarView.setAvatar(roleNameImage);
            roleAvatars.add(roleAvatarView);
        });
        if (CollectionUtils.size(roleAvatars) != CollectionUtils.size(roleIds)) {
            log.error("角色头像图片数量不匹配,roleAvatars:{},roleNames:{}", roleAvatars, roleIds);
            throw new IllegalArgumentException("角色头像图片数量不匹配");
        }
        return storyLetterContent;
    }

    public BizResult<Void> publish(int id) {
        final Story story = storyRepository.selectByPrimaryKey(id);
        if (story == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "剧情不存在");
        }
        storyRepository.updateStatusById(id, CommonStatus.ONLINE.getCode());
        deleteCache(id);
        deleteRelationCache(id);
        Operation operation = Operation.of(OperationConstants.OperateSubType.STORY_PUBLISH).add("id", story.getId());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> offline(int id) {
        final Story story = storyRepository.selectByPrimaryKey(id);
        if (story == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "剧情不存在");
        }
        storyRepository.updateStatusById(id, CommonStatus.NOT_ONLINE.getCode());
        deleteCache(id);
        deleteRelationCache(id);
        Operation operation = Operation.of(OperationConstants.OperateSubType.STORY_OFFLINE).add("id", story.getId());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<List<StoryIdNameView>> getActionStoryIdAndName(int roleId) {
        //根据roleId查询storyId
        List<StoryRoleRelation> storyRoleRelations = storyRoleRelationRepository.selectByRoleId(roleId);
        //从StoryRoleRelation中筛选出storyId
        List<Integer> storyIds = storyRoleRelations.stream().map(StoryRoleRelation::getStoryId).collect(Collectors.toList());
        //根据storyIds查询story
        Map<Integer, Story> storyMap = storyRepository.queryStoriesByIds(storyIds);
        //从上面的 map 中筛选出type是action的 story,然后获取storyid 和name
        List<StoryIdNameView> storyIdNameViews = storyMap.values()
                .stream()
                .filter(story -> StoryType.ACTION.getCode() == story.getType())
                .map(story -> new StoryIdNameView().setId(story.getId()).setName(story.getName()))
                .collect(Collectors.toList());
        return BizResult.success(storyIdNameViews);
    }

    /**
     * 剧情中仅支持选择互动剧情里的标记为【家园场景】的动作剧情
     * 格式为：剧情ID-剧情名称-角色信息
     *
     * @param pageNum
     * @param pageSize
     * @param type
     * @param roleId
     * @param status
     * @return
     */
    public BizResult<PageView<StoryHomeView>> homeList(int pageNum, int pageSize, String type, Integer roleId, Integer status, Integer homeType) {
        try {
            final int offset = (pageNum - 1) * pageSize;
            List<Story> stories = storyRepository.queryByHomeTypePage(offset, pageSize, type, status, homeType);
            final int count = storyRepository.countByHomeType(type, status, homeType);
            if (CollectionUtils.isEmpty(stories) || count == 0) {
                return BizResult.success(PageView.form(0, Collections.emptyList()));
            }
            final Set<Integer> storyIds = stories.stream().map(Story::getId).collect(Collectors.toSet());
            final List<StoryRoleRelation> storyRoleRelations = storyRoleRelationRepository.selectByStoryIds(storyIds);
            final Map<Integer, List<StoryRoleRelation>> storyRoleRelationMap = storyRoleRelations.stream()
                    .collect(Collectors.groupingBy(StoryRoleRelation::getStoryId));
            Set<Integer> configRoleIds = stories.stream()
                    .filter(item -> item.getType().equals(StoryType.ACTION.getCode()))
                    .map(Story::getConfig)
                    .map(item -> JsonUtils.findObject(item, StoryActionConfig.class))
                    .map(StoryActionConfig::getAnimationConfig)
                    .filter(Objects::nonNull)
                    .map(StoryActionConfig.AnimationConfig::getRoleAnimationConfigs)
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .map(StoryActionConfig.RoleAnimationConfig::getRoleId)
                    .collect(Collectors.toSet());
            Set<Integer> relationRoleIds = storyRoleRelations.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toSet());
            relationRoleIds.addAll(configRoleIds);
            final List<Role> roles = roleRepository.queryByIds(relationRoleIds);
            final Map<Integer, Role> roleMap = roles.stream().collect(Collectors.toMap(Role::getId, role -> role));

            final List<StoryHomeView> storyHomeViews = stories.stream().map(story -> {
                final StoryHomeView storyHomeView = new StoryHomeView();
                storyHomeView.setId(story.getId());
                storyHomeView.setName(story.getName());
                StoryActionConfig storyActionConfig = JsonUtils.findObject(story.getConfig(), StoryActionConfig.class);
                if(storyActionConfig != null
                    && storyActionConfig.getAnimationConfig() != null
                    && storyActionConfig.getAnimationConfig().getAnimationJointConfig() != null){
                    storyHomeView.setJointType(storyActionConfig.getAnimationConfig().getAnimationJointConfig().getJointType());
                }
                List<StoryHomeView.RoleInfoView> roleInfoViews = new ArrayList<>();

                final List<StoryRoleRelation> storyRoleRelationsByStory = storyRoleRelationMap.get(story.getId());
                if (CollectionUtils.isNotEmpty(storyRoleRelationsByStory)) {
                    storyRoleRelationsByStory.forEach(storyRoleRelation -> {
                        final StoryHomeView.RoleInfoView roleInfoView = new StoryHomeView.RoleInfoView();
                        roleInfoView.setRoleId(storyRoleRelation.getRoleId());
                        roleInfoView.setRoleName(
                                ObjectUtil.isEmpty(roleMap.get(storyRoleRelation.getRoleId())) ? "" : roleMap.get(storyRoleRelation.getRoleId()).getName());
                        roleInfoViews.add(roleInfoView);
                    });
                    List<String> roleNames = storyRoleRelationsByStory.stream()
                            .map(storyRoleRelation -> roleMap.get(storyRoleRelation.getRoleId()))
                            .filter(Objects::nonNull)
                            .map(Role::getName)
                            .collect(Collectors.toList());
                    storyHomeView.setRoleNames(roleNames);
                }
                storyHomeView.setRoleInfoViews(roleInfoViews);

                return storyHomeView;
            }).sorted(Comparator.comparingInt(StoryHomeView::getId).reversed()).collect(Collectors.toList());
            PageView<StoryHomeView> pageView = PageView.form(count, storyHomeViews);
            return BizResult.success(pageView);
        } catch (Exception e) {
            log.error("homeList 查询失败", e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }
    }
}
