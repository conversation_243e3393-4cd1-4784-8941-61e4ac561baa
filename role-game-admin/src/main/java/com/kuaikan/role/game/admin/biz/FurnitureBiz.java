package com.kuaikan.role.game.admin.biz;

import cn.hutool.core.util.ObjectUtil;
import com.badlogic.gdx.graphics.Texture;
import com.badlogic.gdx.tools.texturepacker.TexturePacker;
import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.model.param.FurnitureAcquisitionWayUpdateParam;
import com.kuaikan.role.game.admin.model.param.FurnitureAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.FurnitureQueryParam;
import com.kuaikan.role.game.admin.model.view.FurnitureView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.repository.*;
import com.kuaikan.role.game.admin.utils.FileUtils;
import com.kuaikan.role.game.admin.utils.TexturePackerConverter;
import com.kuaikan.role.game.api.bean.*;
import com.kuaikan.role.game.api.enums.FurnitureAcquisitionWayType;
import com.kuaikan.role.game.api.enums.FurnitureStatus;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.testng.collections.Lists;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.kuaikan.role.game.admin.component.QiniuComponent.FREE_BUCKET_NAME;

@Slf4j
@Service
public class FurnitureBiz {

    @Resource
    private FurnitureRepository furnitureRepository;

    @Resource
    private FurnitureGroupRelationRepository furnitureGroupRelationRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private StoryRepository storyRepository;

    @Resource
    private StoryRoleRelationRepository storyRoleRelationRepository;

    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;

    @Resource
    private FurnitureGroupRoleGroupRelationRepository furnitureGroupRoleGroupRelationRepository;

    @Resource
    private RedDotService redDotService;

    @Resource
    private FurnitureGroupRepository furnitureGroupRepository;

    @Resource
    private QiniuComponent qiniuComponent;

    public static final String ATLAS_NAME = "_furnitureGroup%d";

    public static final String FURNITURE_SPINE_IMAGE_PATH = "/data/material/furnitureSpineImage/";

    public static final String SLASH = "/";

    public static final String ATLAS_FILE_SUFFIX = ".atlas";
    public static final String PNG_FILE_SUFFIX = ".png";
    public static final String PLIST_FILE_SUFFIX = ".plist";

    public static final String IN_IMG = "in";
    public static final String OUT_IMG = "out";

    private static final int MAX_WIDTH = 2048;
    private static final int MAX_HEIGHT = 2048;

    public static final String FURNITURE_NAME_SPLIT = "_";

    @Transactional(rollbackFor = Exception.class)
    public BizResult<FurnitureView> addOrUpdate(FurnitureAddOrUpdateParam param) {
        log.info("addOrUpdate is param : {}", param);
        Operation operation;
        FurnitureView furnitureView;
        boolean isDuplicateRoleGroupStory = checkDuplicateRoleGroupStory(param);
        if (!isDuplicateRoleGroupStory) {
            return BizResult.result(RoleGameResponse.DUPLICATE_ROLE_GROUP_STORY);
        }
        if (!param.checkParams()) {
            return BizResult.result(RoleGameResponse.CHECK_PARAMS_IS_NOT);
        }
        if (ObjectUtil.isEmpty(param.getId())) {
            Furniture furniture = param.toBean();
            furniture.setOperator(AuthContext.getCurrentUser().getName());
            int id = furnitureRepository.insert(furniture);
            log.info("furniture insert is id : {}", id);
            if (ObjectUtil.isEmpty(id) || id <= 0) {
                throw new RuntimeException("furniture insert error");
            }
            // 查询新增后的完整数据（包含自增ID和默认字段）
            Furniture newFurniture = furnitureRepository.queryById(id);

            // 构建新增日志（仅记录 newData）
            operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_ADD_OR_UPDATE)
                    .add("id", id)
                    .add("newData", JsonUtils.writeValueAsString(newFurniture));
            OperateLogUtils.asyncRecord(operation);

            // 新增家具时，关联套组
            if (ObjectUtil.isNotEmpty(param.getFurnitureGroupId())) {
                FurnitureGroupRelation furnitureGroupRelation = new FurnitureGroupRelation();
                furnitureGroupRelation.setFurnitureGroupId(param.getFurnitureGroupId());
                furnitureGroupRelation.setFurnitureId(id);
                FurnitureGroupRelation.Config config = new FurnitureGroupRelation.Config();
                if (ObjectUtil.isNotEmpty(param.getMapLocation())) {
                    Point location = new Point();
                    location.setX(param.getMapLocation().getX());
                    location.setY(param.getMapLocation().getY());
                    config.setLocation(location);
                }
                furnitureGroupRelation.setConfig(config);
                int furnitureGroupRelationId = furnitureGroupRelationRepository.save(furnitureGroupRelation);
                log.info("furnitureGroupRelation insert is id : {}", furnitureGroupRelationId);
                if (furnitureGroupRelationId <= 0) {
                    throw new RuntimeException("furnitureGroupRelation insert error");
                }
            }
            // 新建就是上架,绑定了角色组和套组就是正式上线
            if (FurnitureStatus.UP_FOR_LISTING.getCode().equals(param.getStatus())) {
                FurnitureGroupRelation furnitureGroupRelation = furnitureGroupRelationRepository.getFurnitureGroupRelationByFurnitureId(furniture.getId());
                Integer furnitureGroupId = Optional.ofNullable(furnitureGroupRelation).map(FurnitureGroupRelation::getFurnitureGroupId).orElse(null);
                List<FurnitureGroupRoleGroupRelation> relationList = furnitureGroupRoleGroupRelationRepository.getByFurnitureGroupIds(
                        Collections.singletonList(furnitureGroupId));
                Set<Integer> roleGroupIds = Optional.ofNullable(relationList)
                        .map(list -> list.stream().map(FurnitureGroupRoleGroupRelation::getRoleGroupId).collect(Collectors.toSet()))
                        .orElse(null);
                if (null != furnitureGroupId && CollectionUtils.isNotEmpty(roleGroupIds)) {
                    redDotService.sendAddNewFurnitureEvent(furniture.getId(), furnitureGroupId, roleGroupIds);
                }
            }
            furnitureView = new FurnitureView();
            furnitureView.setId(id);
            furnitureRepository.deleteCache(furniture.getId());
        } else {
            Furniture furniture = furnitureRepository.queryById(param.getId());
            if (ObjectUtil.isEmpty(furniture)) {
                throw new RuntimeException("furniture is null");
            }
            // 记录旧数据
            operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_ADD_OR_UPDATE)
                    .add("id", param.getId())
                    .add("oldData", JsonUtils.writeValueAsString(furniture));

            Furniture furnitureUpdate = param.toBean();
            furnitureUpdate.setId(furniture.getId());
            furnitureUpdate.setUpdatedAt(new Date());
            furnitureUpdate.setOperator(AuthContext.getCurrentUser().getName());
            int furnitureUpdateCount = furnitureRepository.updateByPrimaryKeySelective(furnitureUpdate);
            // 查询更新后的新数据
            Furniture newFurniture = furnitureRepository.queryById(param.getId());
            operation.add("newData", JsonUtils.writeValueAsString(newFurniture));

            log.info("furniture update is count : {}", furnitureUpdateCount);
            if (furnitureUpdateCount <= 0) {
                log.info("furniture not update ");
            }
            if (ObjectUtil.isNotEmpty(param.getFurnitureGroupId())) {

                FurnitureGroupRelation getFurnitureGroupRelation = furnitureGroupRelationRepository.getFurnitureGroupRelationByFurnitureId(furniture.getId());
                // 编辑家具时，关联套组
                FurnitureGroupRelation furnitureGroupRelation = new FurnitureGroupRelation();
                furnitureGroupRelation.setFurnitureGroupId(param.getFurnitureGroupId());
                furnitureGroupRelation.setFurnitureId(param.getId());
                FurnitureGroupRelation.Config config = new FurnitureGroupRelation.Config();
                if (ObjectUtil.isNotEmpty(param.getMapLocation())) {
                    Point location = new Point();
                    location.setX(param.getMapLocation().getX());
                    location.setY(param.getMapLocation().getY());
                    config.setLocation(location);
                }
                furnitureGroupRelation.setConfig(config);
                if (ObjectUtil.isEmpty(getFurnitureGroupRelation)) {
                    int furnitureGroupRelationUpdateCount = furnitureGroupRelationRepository.save(furnitureGroupRelation);
                    log.info("furnitureGroupRelation save is id : {}", furnitureGroupRelationUpdateCount);
                    if (furnitureGroupRelationUpdateCount <= 0) {
                        log.info("furnitureGroupRelation not save ");
                    }
                } else {
                    int furnitureGroupRelationUpdateCount = furnitureGroupRelationRepository.updateByFurnitureId(furnitureGroupRelation);
                    log.info("furnitureGroupRelation update is count : {}", furnitureGroupRelationUpdateCount);
                    if (furnitureGroupRelationUpdateCount <= 0) {
                        log.info("furnitureGroupRelation not update ");
                    }
                }

            } else {
                // 删除家具关联套组
                int furnitureGroupRelationDeleteCount = furnitureGroupRelationRepository.deleteByFurnitureId(furniture.getId());
                log.info("furnitureGroupRelation delete is count : {}", furnitureGroupRelationDeleteCount);
            }

            // 异步记录日志（确保操作完成后执行）
            OperateLogUtils.asyncRecord(operation);

            
            Furniture furnitureNew = furnitureRepository.queryById(param.getId());
            List<FurnitureView> furnitureViews = getFurnitureViews(Lists.newArrayList(furnitureNew));
            furnitureView = furnitureViews.get(0);
            furnitureRepository.deleteCache(furniture.getId());
        }

        // 生成家具合图
        if ( !handleFurnitureGroupAtlas(param.getFurnitureGroupId()) ) {
            return BizResult.result(RoleGameResponse.FURNITURE_MERGE_ERROR_ADD_OR_UPDATE);
        }

        return BizResult.success(furnitureView);
    }


     /**
     * 生成spine图片
     * @param furnitureGroupId 家具组ID
     */
    public boolean handleFurnitureGroupAtlas(Integer furnitureGroupId) {
        if (Objects.isNull(furnitureGroupId)) {
            return true;
        }

        try {
            // 生成spine图片。
            List<FurnitureGroupRelation> furnitureGroupRelations = furnitureGroupRelationRepository.getFurnitureGroupRelationByFurnitureGroupId(furnitureGroupId);
            if (CollectionUtils.isNotEmpty(furnitureGroupRelations)) {
                List<Integer> furnitureIdList = furnitureGroupRelations.stream().map(FurnitureGroupRelation::getFurnitureId).collect(Collectors.toList());
                List<Furniture> furnitureList = furnitureRepository.getBeanByFurnitureIds(furnitureIdList);
                if (CollectionUtils.isNotEmpty(furnitureList)) {
                    long currentTimeMillis = System.currentTimeMillis();
                    String basicPath = FURNITURE_SPINE_IMAGE_PATH + currentTimeMillis + File.separator;
                    String inImageDir = basicPath + IN_IMG;
                    String outImageDir = basicPath + OUT_IMG;
                    String pre = "role/game/material/furniture/group/";

                    // 下载文件到文件夹 inDir
                    for (Furniture furniture : furnitureList) {
                        String url = furniture.getConfig().getImage().getUrl();
                        if (StringUtils.isNotBlank(url)) {
                            FileUtils.downloadFile(CdnUtil.getDefaultDomainWithBackSlash() + url,inImageDir + File.separator + furniture.getId() + FURNITURE_NAME_SPLIT +  url.substring(url.lastIndexOf("/") + 1));
                        }
                    }

                    // 打包参数
                    TexturePacker.Settings settings = buildSetting(MAX_WIDTH, MAX_HEIGHT, null);
                    // 打包
                    String atlasName = String.format(currentTimeMillis + ATLAS_NAME, furnitureGroupId);
                    TexturePacker.process(settings, inImageDir, outImageDir, atlasName);

                    // 等待执行打包完成
                    waitForOutputFiles(outImageDir);

                    // 生成plist文件
                    generatePlist(outImageDir);

                    // 上传到七牛
                    File directory = new File(outImageDir);

                    FurnitureGroup.Config.AtlasFileInfo atlasFileInfo = new FurnitureGroup.Config.AtlasFileInfo();
                    List<FurnitureGroup.Config.PlistFileInfo> plistFileInfos = Lists.newArrayList();
                    if (directory.exists() && directory.isDirectory()) {
                        for (File file : Objects.requireNonNull(directory.listFiles())) {
                            if (file.getName().endsWith(PNG_FILE_SUFFIX)) {
                                FurnitureGroup.Config.PlistFileInfo plistFileInfo = new FurnitureGroup.Config.PlistFileInfo();
                                String name = file.getName();
                                String pngKey = pre + furnitureGroupId + File.separator + name;
                                qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, file, pngKey);
                                plistFileInfo.setPngFile(pngKey);
                                
                                String key = name.substring(0, name.lastIndexOf(PNG_FILE_SUFFIX));
                                File plistFile = new File(outImageDir + File.separator + key + PLIST_FILE_SUFFIX);
                                String plistKey = pre + furnitureGroupId + File.separator + key + PLIST_FILE_SUFFIX;
                                qiniuComponent.uploadWithXmlStream(FREE_BUCKET_NAME, plistFile, plistKey);
                                plistFileInfo.setPlistFile(plistKey);

                                plistFileInfos.add(plistFileInfo);
                            } else if (file.getName().endsWith(ATLAS_FILE_SUFFIX)) {
                                String name = file.getName();
                                String atlasKey =  pre + furnitureGroupId + File.separator + name;
                                qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, file, atlasKey);
                                atlasFileInfo.setAtlasFile(atlasKey);
                            }
                        }
                    }
                    atlasFileInfo.setPlistFileList(plistFileInfos);


                    FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(furnitureGroupId);
                    if (Objects.nonNull(furnitureGroup)) {
                        FurnitureGroup.Config groupConfig = furnitureGroup.getConfig();
                        groupConfig.setAtlasFileInfo(atlasFileInfo);
                        furnitureGroup.setConfig(groupConfig);
                        furnitureGroup.setUpdatedAt(new Date());
                        furnitureGroupRepository.updateByPrimaryKeySelective(furnitureGroup);
                    }

                    FileUtils.deleteFile(new File(basicPath));
                }
            }
        } catch (Exception e) {
            log.error("handleFurnitureGroupAtlas method error, furnitureGroupId = {}", furnitureGroupId, e);
            return false;
        }


        return true;
    }

    // 生成plist文件
    private void generatePlist(String outImageDir) {

        File outImageDirFile = new File(outImageDir);
        if (!outImageDirFile.exists()) {
            throw new RuntimeException("atlas file directory does not exist");
        }

        for (File file : Objects.requireNonNull(outImageDirFile.listFiles())) {
            if (file.getName().endsWith(ATLAS_FILE_SUFFIX)) {
                String fileString = FileUtils.readJsonFile(file.getAbsolutePath());

                if (StringUtils.EMPTY.equals(fileString)) {
                    throw new RuntimeException("atlas file is empty");
                }

                Map<String, String> plistMap = TexturePackerConverter.convertToPlist(fileString);

                for (String pngKey : plistMap.keySet()) {
                    String key = pngKey.substring(0, pngKey.lastIndexOf(PNG_FILE_SUFFIX));
                    String filePath = outImageDir + File.separator + key + PLIST_FILE_SUFFIX;
                    try {
                        FileUtils.writeFile(plistMap.get(pngKey), filePath);
                    } catch (IOException e) {
                        log.error("write file error, file path {}", filePath, e);
                    }
                }

                // 只有一个atlas文件
                break;
            }
        }

    }

    private void waitForOutputFiles(String outDir) {
        File outDirFile = new File(outDir);
        long startTime = System.currentTimeMillis();

        while (outDirFile.listFiles() == null || Objects.requireNonNull(outDirFile.listFiles()).length < 2) {
            if (System.currentTimeMillis() - startTime > 60000) {
                throw new RuntimeException("Timeout waiting for output files");
            }
            log.info("等待替换图集完成...");
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                throw new RuntimeException("sleep error", e);
            }
        }
    }

    public TexturePacker.Settings buildSetting(int maxWidth, int maxHeight, Float scale) {
        TexturePacker.Settings settings = new TexturePacker.Settings();
        settings.maxWidth = maxWidth;
        settings.maxHeight = maxHeight;
        settings.filterMin = Texture.TextureFilter.Linear;
        settings.filterMag = Texture.TextureFilter.Linear;
        settings.combineSubdirectories = true;
        settings.premultiplyAlpha = false;

        // 核心修复设置 ↓
        settings.paddingX = 8;              // 必须添加内边距
        settings.paddingY = 8;              // 必须添加内边距
        settings.bleed = true;             // 扩散边缘像素
        settings.duplicatePadding = true;  // 复制边缘而非留空
        settings.alphaThreshold = 0;       // 禁用阈值保留半透明

        // 添加边缘扩展防止颜色渗出
        settings.edgePadding = true;       // 额外边缘保护
        settings.bleedIterations = 2;      // 多次边缘扩散

        // 使用更精确的打包算法
        settings.fast = false;             // 禁用快速模式
        settings.grid = false;             // 使用自由布局

        if (Objects.nonNull(scale)) {
            settings.scale = new float[] {scale};
        }
        return settings;
    }

    public void setParam(FurnitureQueryParam param) {
        // 检查 page 和 pageSize 是否为 null
        if (param.getPage() == null || param.getPageSize() == null) {
            throw new IllegalArgumentException("Page and pageSize cannot be null");
        }
        // 确保 page 大于等于 1
        if (param.getPage() < 1) {
            throw new IllegalArgumentException("Page number must be greater than or equal to 1");
        }

        int offset = param.getPageSize() * (param.getPage() - 1);

        param.setOffset(offset);

        if (param.getOrderColumn() == null) {
            param.setOrderColumn(1); // updated_at 列
        }
        if (param.getOrderType() == null) {
            param.setOrderType(param.getOrderColumn() == 1 ? 2 : 1); // 倒序
        }
    }

    public BizResult<PageResult<FurnitureView>> page(FurnitureQueryParam param) {
        log.info("page is param : {}", param);
        setParam(param);

        int totalCount = furnitureRepository.count(param.getId(), param.getName(), param.getType(), null, param.getOperator(),
                param.getStatus(), param.getCreatedAt(), param.getUpdatedAt(), param.getOffset(), param.getFurnitureGroupId());

        if (totalCount == 0) {
            return BizResult.success(new PageResult<>());
        }
        List<Furniture> furnitureList = furnitureRepository.queryByPage(param.getPage(), param.getPageSize(), param.getId(), param.getName(), param.getType(), null,
                param.getOperator(), param.getStatus(), param.getCreatedAt(), param.getUpdatedAt(), param.getOffset(),
                param.getOrderColumn(), param.getOrderType(),
                param.getFurnitureGroupId());
        List<FurnitureView> furnitureViews = getFurnitureViews(furnitureList);

        PageResult<FurnitureView> pageResult = new PageResult<>();
        pageResult.setList(furnitureViews);
        pageResult.setPageNum(param.getPage());
        pageResult.setPageSize(param.getPageSize());
        pageResult.setTotalCount(totalCount);
        return BizResult.success(pageResult);
    }

    @NotNull
    private List<FurnitureView> getFurnitureViews(List<Furniture> furnitureList) {
        List<Furniture.StoryInfo> storyInfos = furnitureList.stream()
                .map(Furniture::getConfig)
                .map(Furniture.Config::getStoryInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        Set<Integer> storyIds = storyInfos.stream().map(Furniture.StoryInfo::getStoryId).collect(Collectors.toSet());
        Set<Integer> roleIds = storyInfos.stream()
                .map(Furniture.StoryInfo::getRolePoints)
                .flatMap(List::stream)
                .map(Furniture.StoryTriggerPos::getRoleId)
                .collect(Collectors.toSet());
        Map<Integer, Story> storyMap = storyRepository.queryStoriesByIds(storyIds);
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, role -> role));

        Map<Integer, List<FurnitureGroupRelation>> integerListMap = new HashMap<>();
        Map<Integer, FurnitureGroup> furnitureGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(furnitureList)) {
            List<FurnitureGroupRelation> furnitureGroupRelationList = furnitureGroupRelationRepository.queryByFurnitureIds(furnitureList.stream().map(Furniture::getId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(furnitureGroupRelationList)) {

                integerListMap.putAll( furnitureGroupRelationList.stream().collect(Collectors.groupingBy(FurnitureGroupRelation::getFurnitureId)));

                List<FurnitureGroup> furnitureGroupList = furnitureGroupRepository.queryByIds(furnitureGroupRelationList.stream().map(FurnitureGroupRelation::getFurnitureGroupId).collect(Collectors.toList()));
                furnitureGroupMap.putAll(furnitureGroupList.stream().collect(Collectors.toMap(FurnitureGroup::getId, Function.identity())));
            }
        }

        return furnitureList.stream().map(item -> FurnitureView.valueOf(item, storyMap, roleMap, integerListMap, furnitureGroupMap)).collect(Collectors.toList());
    }

    public BizResult<String> updateStatus(FurnitureAddOrUpdateParam param) {
        log.info("publish is param : {}", param);
        Furniture furniture = furnitureRepository.queryById(param.getId());
        if (ObjectUtil.isEmpty(furniture)) {
            return BizResult.result(RoleGameResponse.FURNITURE_NOT_EXIST);
        }
        // 记录旧状态
        int oldStatus = furniture.getStatus();
        if (FurnitureStatus.IT_HAS_BEEN_TAKEN_OFF_THE_SHELVES.getCode().equals(param.getStatus())) {
            //判断是否被套组使用，如果是，提示：家具已被【xxxx】套组使用，请先取消关联关系，如果未被使用，直接下架
            String furnitureGroupName = furnitureGroupRelationRepository.getBeanByFurnitureId(furniture.getId());
            if (ObjectUtil.isNotEmpty(furnitureGroupName)) {
                String hint = "家具已被【" + furnitureGroupName + "】套组使用，请先取消关联关系";
                return BizResult.result(null, RoleGameResponse.FURNITURE_SET_IS_OCCUPIED.getCode(), hint);
            }
        }
        Furniture furnitureUpdate = new Furniture();
        furnitureUpdate.setStatus(param.getStatus());
        furnitureUpdate.setId(furniture.getId());
        furnitureUpdate.setUpdatedAt(new Date());
        furnitureRepository.updateByPrimaryKeySelective(furnitureUpdate);
        // 家具关联了套组和角色组就是正式上线,发送家具上新事件
        if (FurnitureStatus.UP_FOR_LISTING.getCode().equals(param.getStatus())) {
            FurnitureGroupRelation furnitureGroupRelation = furnitureGroupRelationRepository.getFurnitureGroupRelationByFurnitureId(furniture.getId());
            Integer furnitureGroupId = Optional.ofNullable(furnitureGroupRelation).map(FurnitureGroupRelation::getFurnitureGroupId).orElse(null);
            List<FurnitureGroupRoleGroupRelation> relationList = furnitureGroupRoleGroupRelationRepository.getByFurnitureGroupIds(
                    Collections.singletonList(furnitureGroupId));
            Set<Integer> roleGroupIds = Optional.ofNullable(relationList)
                    .map(list -> list.stream().map(FurnitureGroupRoleGroupRelation::getRoleGroupId).collect(Collectors.toSet()))
                    .orElse(null);
            if (null != furnitureGroupId && CollectionUtils.isNotEmpty(roleGroupIds)) {
                redDotService.sendAddNewFurnitureEvent(furniture.getId(), furnitureGroupId, roleGroupIds);
            }
        }
        // 构建状态变更日志
        Operation operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_STATUS_UPDATE)
                .add("id", param.getId())
                .add("oldStatus", oldStatus)
                .add("newStatus", param.getStatus());

        // 异步记录日志
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<FurnitureView> detail(Integer id) {
        log.info("detail is id : {}", id);
        Furniture furniture = furnitureRepository.queryById(id);
        if (ObjectUtil.isEmpty(furniture)) {
            return BizResult.result(RoleGameResponse.FURNITURE_IS_NULL);
        }
        List<FurnitureView> furnitureViews = getFurnitureViews(Lists.newArrayList(furniture));
        FurnitureView furnitureView = furnitureViews.get(0);
        return BizResult.success(furnitureView);
    }

    public Map<Integer, Integer> getCountByFurnitureGroupIds(List<Integer> furnitureGroupIds) {
        if (furnitureGroupIds.isEmpty()) {
            return null;
        }
        List<FurnitureGroupRelation> relations = furnitureGroupRelationRepository.getFurnitureByGroupIds(furnitureGroupIds);
        if (relations.isEmpty()) {
            return null;
        }
        Map<Integer, List<FurnitureGroupRelation>> listMap = relations.stream().collect(Collectors.groupingBy(FurnitureGroupRelation::getFurnitureGroupId));

        Map<Integer, Integer> map = new HashMap<>();
        listMap.entrySet().forEach(entry -> {
            int size = entry.getValue().size();
            map.put(entry.getKey(), size);
        });
        return map;
    }

    public BizResult list() {
        List<Furniture> furnitureList = furnitureRepository.queryAll();
        List<FurnitureView> furnitureViews = getFurnitureViews(furnitureList);
        return ObjectUtil.isEmpty(furnitureList) ? BizResult.success(new ArrayList<>()) : BizResult.success(furnitureViews);
    }

    public BizResult<List<FurnitureView>> batchDetail(List<Integer> ids) {
        try {

            List<Furniture> furnitureList = furnitureRepository.queryByIds(ids);
            if (ObjectUtil.isEmpty(furnitureList)) {
                return BizResult.success(new ArrayList<>());
            }
            Map<Integer, Integer> furnitureGroupIdMap = furnitureGroupRelationRepository.getFurnitureGroupIdByFurnitureIds(ids);
            List<FurnitureView> furnitureViews = getFurnitureViews(furnitureList);
            furnitureViews = furnitureViews.stream().peek(furnitureView -> {
                Integer furnitureGroupId = furnitureGroupIdMap.get(furnitureView.getId());
                furnitureView.setFurnitureGroupId(ObjectUtil.isEmpty(furnitureGroupId) ? " " : furnitureGroupId.toString());
            }).collect(Collectors.toList());

            return BizResult.success(furnitureViews);
        } catch (Exception e) {
            log.error("batchDetail error", e);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR);
        }

    }

    public BizResult<Void> updateAcquisitionWay(FurnitureAcquisitionWayUpdateParam param) {
        List<FurnitureAcquisitionWayUpdateParam.FurnitureAcquisitionWayParam> acquisitionWaysParam = param.getAcquisitionWays();
        Furniture furniture = furnitureRepository.queryById(param.getFurnitureId());
        if (ObjectUtil.isEmpty(furniture)) {
            return BizResult.result(RoleGameResponse.FURNITURE_NOT_EXIST);
        }
        Furniture.Config config = furniture.getConfig();
        if (ObjectUtil.isEmpty(config)) {
            return BizResult.result(RoleGameResponse.FURNITURE_CONFIG_NOT_EXIST);
        }
        
        Furniture.FurnitureAcquisitionWayInfoModel acquisitionWaysModel = new Furniture.FurnitureAcquisitionWayInfoModel();
        acquisitionWaysModel.setCornerMark(param.getCornerMark());
        List<Furniture.FurnitureAcquisitionWay> furnitureAcquisitionWays = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(acquisitionWaysParam)) {
            furnitureAcquisitionWays = acquisitionWaysParam.stream().map(acquisitionWayParam -> {
                Furniture.FurnitureAcquisitionWay furnitureAcquisitionWay = new Furniture.FurnitureAcquisitionWay();
                FurnitureAcquisitionWayType furnitureAcquisitionWayType = FurnitureAcquisitionWayType.getByCode(acquisitionWayParam.getType());
                if (ObjectUtil.isEmpty(furnitureAcquisitionWayType)) {
                    throw new RuntimeException("FurnitureAcquisitionWayType not exist, type:" + acquisitionWayParam.getType());
                }
                furnitureAcquisitionWay.setType(furnitureAcquisitionWayType.getCode());
                furnitureAcquisitionWay.setAction(acquisitionWayParam.getAction());
                furnitureAcquisitionWay.setStartEffectiveTime(acquisitionWayParam.getStartEffectiveTime());
                furnitureAcquisitionWay.setEndEffectiveTime(acquisitionWayParam.getEndEffectiveTime());
                furnitureAcquisitionWay.setDescription(acquisitionWayParam.getDescription());
                return furnitureAcquisitionWay;
            }).collect(Collectors.toList());
        }
        acquisitionWaysModel.setAcquisitionWays(furnitureAcquisitionWays);
        config.setAcquisitionWayInfo(acquisitionWaysModel);
        furniture.setConfig(config);
        furnitureRepository.updateByPrimaryKeySelective(furniture);
        Operation operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_ACQUISITION_WAY_UPDATE)
                .add("furnitureId", param.getFurnitureId())
                .add("acquisitionWays", furnitureAcquisitionWays);
        OperateLogUtils.asyncRecord(operation);
        log.info("updateAcquisitionWay is success,furnitureId:{},acquisitionWays:{}", param.getFurnitureId(), furnitureAcquisitionWays);
        return BizResult.success();
    }

    private boolean checkDuplicateRoleGroupStory(FurnitureAddOrUpdateParam furnitureAddOrUpdateParam) {
        List<FurnitureAddOrUpdateParam.StoryInfo> storyInfoList = furnitureAddOrUpdateParam.getStoryInfoList();
        if (ObjectUtil.isEmpty(storyInfoList)) {
            return true;
        }
        // 判断 storyIds 是否存在重复
        Set<Integer> storyIds = storyInfoList.stream().map(FurnitureAddOrUpdateParam.StoryInfo::getStoryId).collect(Collectors.toSet());
        if (storyInfoList.size() != storyIds.size()) {
            return false;
        }
        List<StoryRoleRelation> storyRoleRelations = storyRoleRelationRepository.selectByStoryIds(storyIds);
        if (ObjectUtil.isEmpty(storyRoleRelations)) {
            return true;
        }
        Map<Integer,List<Integer>> storyToRoleMap = storyRoleRelations.stream().collect(Collectors.groupingBy(StoryRoleRelation::getStoryId, Collectors.mapping(StoryRoleRelation::getRoleId, Collectors.toList())));
        log.info("checkDuplicateRoleGroupStory storyRoleRelations:{}", storyRoleRelations);
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByRoleIds(storyRoleRelations.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toList()));
        Map<Integer,Integer> roleToRoleGroupMap = roleGroupRelations.stream().collect(Collectors.toMap(RoleGroupRelation::getRoleId, RoleGroupRelation::getRoleGroupId));
        Set<Integer> roleGroupSet = new HashSet<>();
        for(Integer storyId : storyIds){
            List<Integer> roleIds = storyToRoleMap.get(storyId);
            if(ObjectUtil.isNotEmpty(roleIds)){
                Integer roleGroupId = roleToRoleGroupMap.get(roleIds.get(0));
                if(roleGroupSet.contains(roleGroupId)){
                    return false;
                }
                roleGroupSet.add(roleGroupId);
            }
        }
        return true;
    }
}
