package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.com.google.common.collect.Sets;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;

import com.kuaikan.comic.admin.utils.TopicSearchUtils;
import com.kuaikan.comic.bean.Topic;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.enums.TopicStatus;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.game.common.util.PageUtil;
import com.kuaikan.role.game.admin.common.BattleUploadType;
import com.kuaikan.role.game.admin.component.battle.ConfigExcelUploadFactory;
import com.kuaikan.role.game.admin.model.excel.CardBattlePracticeTaskBuffData;
import com.kuaikan.role.game.admin.model.excel.PracticeBattleStageConfExcelDto;
import com.kuaikan.role.game.admin.model.param.BattlePageQueryParam;
import com.kuaikan.role.game.admin.model.param.BattleTaskAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.battle.BattleTaskView;
import com.kuaikan.role.game.admin.model.view.battle.BattleTopicView;
import com.kuaikan.role.game.admin.model.view.battle.BizResultWithHost;
import com.kuaikan.role.game.admin.model.view.battle.PageWrapper;
import com.kuaikan.role.game.admin.repository.BattleTaskRepo;
import com.kuaikan.role.game.admin.utils.ExcelUtils;
import com.kuaikan.role.game.admin.utils.HostUtil;
import com.kuaikan.role.game.admin.utils.WaitCouponTopicSearchUtils;
import com.kuaikan.role.game.api.bean.cardbattle.BattleTask;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardIdModelConfig;
import com.kuaikan.role.game.api.constant.CardBattleResponseCodeMsg;
import com.kuaikan.role.game.api.service.cardbattle.CardBattleDataFixService;

/**
 * 卡牌战斗-独立战斗关卡设置biz
 *
 *<AUTHOR>
 *@date 2024/8/29
 */
@Service
@Slf4j
public class BattleTaskBiz {

    @Resource
    private BattleTaskRepo battleTaskRepo;
    @Resource
    private CardBattleDataFixService cardBattleDataFixService;
    @Resource
    private ConfigExcelUploadFactory configExcelUploadFactory;

    public BizResultWithHost<PageWrapper<BattleTaskView>> getBattleTaskList(BattlePageQueryParam param) {
        PageWrapper<BattleTaskView> result;
        if (StringUtils.isNotBlank(param.getId())) {
            BattleTask battleTask = battleTaskRepo.getBattleTaskById(param.getId());
            if (battleTask == null) {
                result = PageWrapper.of(0, Lists.newArrayList());
            } else {
                BattleTaskView dto = BattleTaskView.convert(battleTask);
                result = PageWrapper.of(1, Lists.newArrayList(dto));
            }
        } else {
            int page = param.getPage();
            int pageSize = param.getPageSize();
            int type = param.getType();
            int total = battleTaskRepo.countBattleTask(type);
            List<BattleTask> battleTasks = battleTaskRepo.listBattleTask(pageSize, page, type);
            int totalPage = PageUtil.getTotalPage(total, pageSize);
            List<BattleTaskView> views = Lists.newArrayList();
            for (BattleTask battleTask : battleTasks) {
                views.add(BattleTaskView.convert(battleTask));
            }
            result = PageWrapper.of(totalPage, Lists.newArrayList(views));
        }
        return BizResultWithHost.success(result).setHost(HostUtil.getHost());
    }

    public BizResult<PageWrapper<BattleTopicView>> getBattleTaskLimitTopicList(int page, int pageSize, String topicId) {
        if (StringUtils.isEmpty(topicId)) {
            return BizResult.success(PageWrapper.of(0, Lists.newArrayList()));
        }
        List<Topic> topicList = TopicSearchUtils.searchByPage(topicId, ImmutableSet.of(TopicStatus.PUBLISHED), page, pageSize,
                TopicSearchUtils.SearchField.TOPIC_ID_TOPIC_TITLE.getCode()).getList();
        if (CollectionUtils.isEmpty(topicList)) {
            return BizResult.success(PageWrapper.of(0, Lists.newArrayList()));
        }
        Set<Integer> battleTopicIds = getBattleTopicIds();

        List<Topic> filteredTopicList = topicList.stream().filter(topic -> battleTopicIds.contains(topic.getId())).collect(Collectors.toList());
        List<BattleTopicView> views = Lists.newArrayList();
        for (Topic topic : filteredTopicList) {
            views.add(BattleTopicView.convert(topic));
        }
        return BizResult.success(PageWrapper.of(pageSize, Lists.newArrayList(views)));
    }

    public Object createBattleTask(BattleTaskAddOrUpdateParam param) {
        BattleTask battleTask = param.toBattleTask();
        battleTaskRepo.createBattleTask(battleTask);
        log.info("Create battleTask success, battleTaskInfo:{}, Operator:{}", battleTask, battleTask.getOperator());
        return BizResult.success();
    }

    public Object updateBattleTask(BattleTaskAddOrUpdateParam param) {
        BattleTask oldBattleTask = battleTaskRepo.getBattleTaskById(param.getId());
        if (oldBattleTask == null) {
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_TASK_NOT_EXIST);
        }
        if (param.getWaitFreeTopicIds() != null && oldBattleTask.getStatus() == 1) {
            //判断当前生效的关卡是否已经有配置过该专题
            for (int topicId : param.getWaitFreeTopicIds()) {
                if (checkBattleTaskWaitFreeTopicValid(topicId, param.getClassifyId(), param.getId())) {
                    return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_TASK_WAIT_FREE_TOPIC_VALID);
                }
                // 清理缓存
                cardBattleDataFixService.refreshTopicWaitFreeBattleTask(topicId);
            }
        }
        BattleTask battleTask = param.toBattleTask();
        boolean ret = battleTaskRepo.updateBattleTask(battleTask);
        if (ret) {
            log.info("Update battleTask success, battleTaskId:{}, battleTaskInfo:{}, Operator:{}", battleTask.getId(), battleTask, battleTask.getOperator());
            return BizResult.success();
        } else {
            log.error("Update battleTask failed, battleTaskId:{}, battleTaskInfo:{}, Operator:{}", battleTask.getId(), battleTask, battleTask.getOperator());
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_TASK_UPDATE_FAIL);
        }
    }

    public Object updateBattleTaskStatus(String id, int status) {
        BattleTask battleTask = battleTaskRepo.getBattleTaskById(id);
        if (battleTask == null) {
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_TASK_NOT_EXIST);
        }
        if (CollectionUtils.isNotEmpty(battleTask.getWaitFreeTopicIds())) {
            if (battleTask.getStatus() == 0 && status == 1) {
                //判断当前生效的关卡是否已经有配置过该专题
                for (Integer topicId : battleTask.getWaitFreeTopicIds()) {
                    if (checkBattleTaskWaitFreeTopicValid(topicId, battleTask.getClassifyId(), id)) {
                        return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_TASK_WAIT_FREE_TOPIC_VALID);
                    }
                    // 清理缓存
                    cardBattleDataFixService.refreshTopicWaitFreeBattleTask(topicId);
                }
            }
        }
        battleTaskRepo.updateBattleTaskStatus(id, status);
        return BizResult.success();
    }

    public BizResult<PageWrapper<BattleTopicView>> getBattleTaskWaitFreeTopicList(int page, int pageSize, String topicId, HttpServletRequest request) {
        if (StringUtils.isEmpty(topicId)) {
            return BizResult.success(PageWrapper.of(0, Lists.newArrayList()));
        }
        List<BattleTopicView> views = Lists.newArrayList();
        if (StringUtils.isNumeric(topicId)) {
            List<Topic> topicList = TopicSearchUtils.searchByPage(topicId, ImmutableSet.of(TopicStatus.PUBLISHED), page, pageSize,
                    TopicSearchUtils.SearchField.TOPIC_ID.getCode()).getList();
            if (CollectionUtils.isEmpty(topicList)) {
                return BizResult.success(PageWrapper.of(0, Lists.newArrayList()));
            }
            for (Topic topic : topicList) {
                String topicTitle = topic.getTitle();
                views = getBattleTaskWaitCouponTopicListByTitle(topicTitle, request).stream()
                        .filter(battleTopicView -> battleTopicView.getId() == Integer.parseInt(topicId))
                        .collect(Collectors.toList());
            }
        } else {
            views = getBattleTaskWaitCouponTopicListByTitle(topicId, request).stream()
                    .filter(view -> view.getName().contains(topicId))
                    .collect(Collectors.toList());
        }
        return BizResult.success(PageWrapper.of(pageSize, Lists.newArrayList(views)));
    }

    private boolean checkBattleTaskWaitFreeTopicValid(int topicId, Integer classifyId, String id) {
        List<BattleTask> battleTaskTopicList = battleTaskRepo.getAllValidBattleTasks(classifyId);
        List<Integer> battleTaskValidTopicList = Lists.newArrayList();
        for (BattleTask battleTask : battleTaskTopicList) {
            if (battleTask.getId().equals(id)) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(battleTask.getWaitFreeTopicIds())) {
                battleTaskValidTopicList.addAll(battleTask.getWaitFreeTopicIds());
            }
        }
        return battleTaskValidTopicList.contains(topicId);
    }

    private List<BattleTopicView> getBattleTaskWaitCouponTopicListByTitle(String topicId, HttpServletRequest request) {
        List<BattleTopicView> waitCouponTopicList = WaitCouponTopicSearchUtils.searchByPage(topicId, request);
        if (CollectionUtils.isEmpty(waitCouponTopicList)) {
            return Lists.newArrayList();
        }
        return waitCouponTopicList;
    }

    private Set<Integer> getBattleTopicIds() {
        List<CardBattleCardIdModelConfig> cardBattleCardIdModelConfigList = battleTaskRepo.getCardBattleCardIdModelConfigs();
        if (CollectionUtils.isEmpty(cardBattleCardIdModelConfigList)) {
            return Sets.newHashSet();
        }
        return cardBattleCardIdModelConfigList.stream().map(CardBattleCardIdModelConfig::getTopicId).collect(Collectors.toSet());
    }

    public Object uploadStageConfExcelFile(MultipartFile file) {
        try {
            List<PracticeBattleStageConfExcelDto> list = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.PRACTICE_BATTLE_STAGE_CONF,
                    PracticeBattleStageConfExcelDto.class);
            log.info("Upload stage conf excel file success, list:{}", JSON.toJSON(list));
            return BizResult.success(list);
        } catch (Exception e) {
            log.error("Upload stage conf excel file failed, error:{}", e.getMessage());
        }
        return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "上传失败");
    }

    public void downloadStageConfExcelFile(HttpServletResponse response, String taskId) {
        BattleTask battleTask = battleTaskRepo.getBattleTaskById(taskId);
        if (battleTask == null) {
            return;
        }
        List<BattleTask.StageConfig> stageConfList = battleTask.getStageConfList();
        ExcelUtils.writeExcel("各阶段伤害对应奖励数量配置", stageConfList, PracticeBattleStageConfExcelDto.class, response);
    }

    public Object uploadBuffStageConfExcelFile(MultipartFile file) {
        try {
            List<CardBattlePracticeTaskBuffData> list = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.PRACTICE_BATTLE_BUFF,
                    CardBattlePracticeTaskBuffData.class);
            log.info("Upload Buff conf excel file success, list:{}", JSON.toJSON(list));
            return BizResult.success(list.stream().map(CardBattlePracticeTaskBuffData::toBuffLevelConf).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("Upload Buff conf excel file failed, error:{}", e.getMessage());
        }
        return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "上传失败");
    }

    public void downloadBuffStageConfExcelFile(HttpServletResponse response, String taskId) {
        BattleTask battleTask = battleTaskRepo.getBattleTaskById(taskId);
        if (battleTask == null) {
            return;
        }
        List<BattleTask.BuffLevelConf> BuffStageConfList = battleTask.getPracticeBuffConf().getBuffStageConfList();
        List<CardBattlePracticeTaskBuffData> collect = BuffStageConfList.stream().map(CardBattlePracticeTaskBuffData::toExcelDto).collect(Collectors.toList());
        ExcelUtils.writeExcel("各阶段buff效果配置", collect, CardBattlePracticeTaskBuffData.class, response);
    }
}
