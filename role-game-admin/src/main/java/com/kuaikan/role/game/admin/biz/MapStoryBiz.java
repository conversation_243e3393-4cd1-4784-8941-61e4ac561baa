package com.kuaikan.role.game.admin.biz;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.model.param.map.MapStoryAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.map.MapStoryView;
import com.kuaikan.role.game.admin.repository.MapStoryRelationRepository;
import com.kuaikan.role.game.admin.repository.MapStoryRepository;
import com.kuaikan.role.game.api.bean.MapStory;
import com.kuaikan.role.game.api.bean.MapStoryAiTaskConfig;
import com.kuaikan.role.game.api.bean.MapStoryRelation;
import com.kuaikan.role.game.api.bean.MapStoryRelationConfig;
import com.kuaikan.role.game.api.bean.MapStoryRoleRelation;
import com.kuaikan.role.game.api.bean.MapStoryVoiceConfig;
import com.kuaikan.role.game.api.enums.MapStoryType;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.api.util.GsonUtils;
import com.kuaikan.role.game.common.bean.MapCity;
import com.kuaikan.role.game.common.bean.MapStoryConfig;

/**
 *<AUTHOR>
 *@date 2025/6/30
 */
@Component
@Slf4j
public class MapStoryBiz {

    @Resource
    private MapStoryRepository mapStoryRepository;

    @Resource
    private MapStoryRelationRepository mapStoryRelationRepository;

    @Resource
    private RedDotService redDotService;

    public BizResult<PageView<MapStoryView>> getMapStoryList(int pageNum, int pageSize, int mapId, int type) {
        final int offset = (pageNum - 1) * pageSize;
        List<MapStory> stories = mapStoryRepository.queryByPage(offset, pageSize, mapId, type);
        List<MapStoryView> storyViews = stories.stream().map(MapStoryView::valueOf).collect(Collectors.toList());
        storyViews.stream().forEach(e -> mapStoryRepository.fillTopicRoleIds(e));
        final int count = mapStoryRepository.countByCondition(type, mapId);
        return BizResult.success(PageView.form(count, storyViews));
    }

    public BizResult<MapStoryView> getById(int id) {
        Optional<MapStoryView> mapStoryView = mapStoryRepository.selectById(id);
        if (mapStoryView.isPresent()) {
            return BizResult.success(mapStoryView.get());
        } else {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "地图剧情ID不存在");
        }
    }

    public void insertMapStoryRelations(MapCity mapCity, List<MapStoryConfig> mapStoryConfigList) {
        int mapId = mapCity.getId();
        if (CollectionUtils.isEmpty(mapStoryConfigList)) {
            // 如果没有关联剧情，则需要把原来的关联关系全部删除掉
            mapStoryRelationRepository.deleteByMapId(mapId);
            mapStoryRelationRepository.deleteCacheByMapId(mapId);
            return;
        }
        List<MapStoryRelation> paramStoryRelations = new ArrayList<>();
        for (int i = 0; i < mapStoryConfigList.size(); i++) {
            MapStoryConfig mapStoryConfig = mapStoryConfigList.get(i);
            for (int j = 0; j < mapStoryConfig.getLibItems().size(); j++) {
                MapStoryConfig.MapStoryLibItem item = mapStoryConfig.getLibItems().get(j);
                for (int k = 0; k < item.getList().size(); k++) {
                    MapStoryConfig.MapStoryItem storyItem = item.getList().get(k);
                    MapStoryRelationConfig extraConfig = new MapStoryRelationConfig();
                    extraConfig.setWeight(storyItem.getWeight());
                    paramStoryRelations.add(new MapStoryRelation().setMapId(mapId)
                            .setLibraryId(item.getLibraryId())
                            .setTag(mapStoryConfig.getTag())
                            .setMapStoryId(storyItem.getMapStoryId())
                            .setConfig(GsonUtils.toJson(extraConfig))
                            .setTagNum(i)
                            .setLibNum(j)
                            .setOrderNum(k));
                }
            }
        }
        // 对比已有的关联剧情和传入的关联剧情，进行增删改, 按mapStoryId判断
        Map<Integer, MapStoryRelation> newRelationsMap = paramStoryRelations.stream().collect(Collectors.toMap(MapStoryRelation::getMapStoryId, e -> e));
        // 查询地图已有的关联剧情
        List<MapStoryRelation> mapStoryRelations = mapStoryRelationRepository.selectByMapId(mapId);
        Set<Integer> existingMapStoryIds = new HashSet<>();
        // 找出需要删除的，
        if (CollectionUtils.isNotEmpty(mapStoryRelations)) {
            existingMapStoryIds = mapStoryRelations.stream().map(MapStoryRelation::getMapStoryId).collect(Collectors.toSet());
            List<Integer> toDeleteIds = mapStoryRelations.stream()
                    .filter(e -> !newRelationsMap.containsKey(e.getMapStoryId()))
                    .map(MapStoryRelation::getId)
                    .collect(Collectors.toList());
            mapStoryRelationRepository.deleteByIds(toDeleteIds);
        }
        // 新增的配置, 设置红点信息
        AtomicReference<Boolean> addStory = new AtomicReference<>(false);
        Set<String> newDotTags = new HashSet<>();
        for (MapStoryRelation newRelation : paramStoryRelations) {
            if (!existingMapStoryIds.contains(newRelation.getMapStoryId())) {
                redDotService.sendAddMapStoryEvent(mapId, newRelation.getMapStoryId());
                // 地图红点上新
                addStory.set(true);
                // tag 上新
                newDotTags.add(newRelation.getTag());
            }
        }
        // 地图上新红点
        if (addStory.get()) {
            redDotService.sendMapHomeAddStoryEvent(mapId);
        }
        // tag上新
        for (String tag : newDotTags) {
            redDotService.sendAddMapStoryTagEvent(mapId, tag);
        }
        mapStoryRelationRepository.insertBatch(paramStoryRelations);
        mapStoryRelationRepository.deleteCacheByMapId(mapId);
    }

    public void fillMapStoryConfigList(MapCity mapCity) {
        int mapId = mapCity.getId();
        // 查询地图关联的所有剧情
        List<MapStory> mapStories = mapStoryRepository.selectByMapId(mapId);
        if (CollectionUtils.isEmpty(mapStories)) {
            // 如果没有关联剧情，则不需要填充
            return;
        }
        Map<Integer, MapStory> mapStoryMap = mapStories.stream().collect(Collectors.toMap(MapStory::getId, Function.identity()));
        List<MapStoryRelation> mapStoryRelations = mapStoryRelationRepository.selectByMapId(mapId);
        if (CollectionUtils.isNotEmpty(mapStoryRelations)) {
            List<MapStoryConfig> mapStoryConfigList = new ArrayList<>();
            mapStoryRelations.stream()
                    .collect(Collectors.groupingBy(MapStoryRelation::getTag, LinkedHashMap::new, Collectors.toList()))
                    .forEach((tag, list) -> {
                        MapStoryConfig mapStoryConfig = new MapStoryConfig().setTag(tag);
                        List<MapStoryConfig.MapStoryLibItem> libItems = new ArrayList<>();
                        list.stream()
                                .collect(Collectors.groupingBy(MapStoryRelation::getLibraryId, LinkedHashMap::new, Collectors.toList()))
                                .forEach((libraryId, relations) -> {
                                    MapStoryConfig.MapStoryLibItem libItem = new MapStoryConfig.MapStoryLibItem();
                                    libItem.setLibraryId(libraryId);
                                    List<MapStoryConfig.MapStoryItem> storyItems = relations.stream().map(e -> {
                                        MapStoryConfig.MapStoryItem item = new MapStoryConfig.MapStoryItem();
                                        MapStory mapStory = mapStoryMap.get(e.getMapStoryId());
                                        item.setMapStoryId(e.getMapStoryId());
                                        item.setWeight(e.getWeight());
                                        item.setName(mapStory == null ? "" : mapStory.getName());
                                        return item;
                                    }).collect(Collectors.toList());
                                    libItem.setList(storyItems);
                                    libItems.add(libItem);
                                });
                        mapStoryConfig.setLibItems(libItems);
                        mapStoryConfigList.add(mapStoryConfig);
                    });
            mapCity.getConfig().setMapStoryConfigList(mapStoryConfigList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(MapStoryAddOrUpdateParam param) {
        Integer mapStoryId = param.getId();
        if (mapStoryId != null && mapStoryId > 0) {
            return doUpdate(param);
        } else {
            return doAdd(param);
        }
    }

    private BizResult<Void> doAdd(MapStoryAddOrUpdateParam param) {
        MapStory mapStory = buildMapStoryByParam(param);
        mapStoryRepository.insert(mapStory);
        if (CollectionUtils.isNotEmpty(param.getTopicRoleIds())) {
            List<MapStoryRoleRelation> relations = buildMapStoryRoleRelation(mapStory.getId(), param.getTopicRoleIds());
            mapStoryRepository.insertStoryRoleRelations(relations);
        }
        return BizResult.success();
    }

    private BizResult<Void> doUpdate(MapStoryAddOrUpdateParam param) {
        MapStory mapStoryDb = mapStoryRepository.queryById(param.getId());
        if (!Objects.equals(mapStoryDb.getMapId(), param.getMapId())) {
            MapStoryRelation mapStoryRelation = mapStoryRelationRepository.queryByMapIdAndStoryId(mapStoryDb.getMapId(), mapStoryDb.getId());
            if (Objects.nonNull(mapStoryRelation)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), String.format("剧情已经在地图id=%d中使用，如要修改请先解除关联关系", mapStoryDb.getMapId()));
            }
        }

        MapStory mapStory = buildMapStoryByParam(param);
        mapStory.setId(param.getId());
        mapStoryRepository.update(mapStory);
        List<MapStoryRoleRelation> relations = buildMapStoryRoleRelation(mapStory.getId(), param.getTopicRoleIds());
        mapStoryRepository.updateStoryRoleRelations(mapStory.getId(), relations);
        // 删除对应地图的配置缓存
        mapStoryRelationRepository.deleteCacheByMapId(param.getMapId());
        return BizResult.success();
    }

    private MapStory buildMapStoryByParam(MapStoryAddOrUpdateParam param) {
        MapStory mapStory = new MapStory();
        mapStory.setMapId(param.getMapId());
        mapStory.setName(param.getName());
        mapStory.setType(param.getType());
        mapStory.setAvgChapterId(0);
        if (Objects.nonNull(param.getCoverImage()) && StringUtils.isNotBlank(param.getCoverImage().getUrl())) {
            mapStory.setCoverImage(param.getCoverImage().getUrl());
        } else {
            mapStory.setCoverImage("");
        }
        // 设置个默认值
        mapStory.setConfig(JsonUtils.toJson(new MapStoryVoiceConfig()));
        if (MapStoryType.AVG.getCode() == mapStory.getType()) {
            mapStory.setAvgChapterId(param.getAvgChapterId());
        } else if (MapStoryType.AUDIO.getCode() == mapStory.getType()) {
            MapStoryVoiceConfig config = new MapStoryVoiceConfig();
            config.setConfigFileKey(param.getMp3Config().getConfigFileKey());
            config.setConfigFileName(param.getMp3Config().getConfigFileName());
            mapStory.setConfig(JsonUtils.toJson(config));
        } else if (MapStoryType.AI_TASK.getCode() == mapStory.getType()) {
            MapStoryAiTaskConfig storyConfig = new MapStoryAiTaskConfig();
            storyConfig.setAiTaskCardId(param.getAiTaskCardId());
            mapStory.setConfig(JsonUtils.toJson(storyConfig));
        }
        if (CollectionUtils.isNotEmpty(param.getUnlockConditions())) {
            mapStory.setUnlockConditions(param.getUnlockConditions().stream().map(condition -> {
                MapStory.UnlockCondition unlockCondition = new MapStory.UnlockCondition();
                unlockCondition.setConditionType(condition.getConditionType());
                unlockCondition.setCoinId(condition.getCoinId());
                unlockCondition.setCoinCount(condition.getCoinCount());
                unlockCondition.setAttributeKey(condition.getAttributeKey());
                unlockCondition.setAttributeValue(condition.getAttributeValue());
                return unlockCondition;
            }).collect(Collectors.toList()));
        }
        if (MapStoryType.AVG.getCode() == mapStory.getType() || MapStoryType.AUDIO.getCode() == mapStory.getType()) {
            mapStory.setSummaryContent(param.getSummaryContent());
        } else {
            mapStory.setSummaryContent("");
        }
        // todo 后续删除相关字段
        mapStory.setLibraryId(0);
        mapStory.setTag("");
        mapStory.setWeight(0);
        mapStory.setObtainCopywriting("");
        mapStory.setOrderNum(0);

        mapStory.setStatus(0);
        return mapStory;
    }

    private List<MapStoryRoleRelation> buildMapStoryRoleRelation(int mapStoryId, List<Integer> topicRoleIds) {
        if (CollectionUtils.isEmpty(topicRoleIds)) {
            return new ArrayList<>();
        }
        List<MapStoryRoleRelation> relations = new ArrayList<>();
        for (int i = 0; i < topicRoleIds.size(); i++) {
            relations.add(new MapStoryRoleRelation(mapStoryId, topicRoleIds.get(i), i));
        }
        return relations;
    }
}
