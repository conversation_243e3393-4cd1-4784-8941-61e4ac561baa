package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.coupon.CouponProcessorSelector;
import com.kuaikan.role.game.admin.model.param.BaseCouponAddParam;
import com.kuaikan.role.game.admin.model.view.RoleAdoptCouponView;
import com.kuaikan.role.game.admin.repository.RoleAdoptCouponConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupAdoptCouponRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleAdoptCouponConfig;
import com.kuaikan.role.game.api.enums.CouponConfigType;
import com.kuaikan.role.game.api.enums.CouponStatusType;
import com.kuaikan.role.game.common.bean.RoleGroupAdoptCouponRelation;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;

/**
 * <AUTHOR>
 * @date 2024/10/29 09:53
 */
@Service
@Slf4j
public class RoleAdoptCouponBiz {

    @Resource
    private RoleAdoptCouponConfigRepository roleAdoptCouponConfigRepository;
    @Resource
    private RoleGroupAdoptCouponRelationRepository roleGroupAdoptCouponRelationRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private CouponProcessorSelector couponProcessorSelector;

    public BizResult<PageView<RoleAdoptCouponView>> list(int pageNum, int pageSize) {
        final int count = roleAdoptCouponConfigRepository.count();
        if (count == 0) {
            return BizResult.success(PageView.empty());
        }

        final int offset = (pageNum - 1) * pageSize;
        final List<RoleAdoptCouponConfig> roleAdoptCouponConfigs = roleAdoptCouponConfigRepository.queryByPage(offset, pageSize);
        if (roleAdoptCouponConfigs.isEmpty()) {
            return BizResult.success(PageView.empty());
        }

        final Map<Integer, List<RoleGroupRelation>> roleGroupRelationMap = roleGroupRelationRepository.queryByGroupIds(
                roleGroupAdoptCouponRelationRepository.queryByCouponIds(
                                roleAdoptCouponConfigs.stream().map(RoleAdoptCouponConfig::getId).collect(Collectors.toList()))
                        .stream()
                        .map(RoleGroupAdoptCouponRelation::getRoleGroupId)
                        .collect(Collectors.toSet())).stream().collect(Collectors.groupingBy(RoleGroupRelation::getRoleGroupId));

        final Map<Integer, Role> roleMap = roleRepository.queryByIds(
                        roleGroupRelationMap.values().stream().flatMap(List::stream).map(RoleGroupRelation::getRoleId).collect(Collectors.toSet()))
                .stream()
                .collect(Collectors.toMap(Role::getId, Function.identity()));

        final Map<Integer, List<Integer>> coupon2RoleGroupIds = roleGroupAdoptCouponRelationRepository.queryByCouponIds(
                        roleAdoptCouponConfigs.stream().map(RoleAdoptCouponConfig::getId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(RoleGroupAdoptCouponRelation::getCouponId,
                        Collectors.mapping(RoleGroupAdoptCouponRelation::getRoleGroupId, Collectors.toList())));

        final List<RoleAdoptCouponView> roleAdoptCouponViews = roleAdoptCouponConfigs.stream().map(config -> {
            Integer couponId = config.getId();
            Integer roleGroupId = coupon2RoleGroupIds.get(couponId).get(0);
            List<RoleGroupRelation> roleGroupRelation = roleGroupRelationMap.get(roleGroupId);
            return RoleAdoptCouponView.valueOf(config, roleGroupRelation, roleMap);
        }).collect(Collectors.toList());

        return BizResult.success(PageView.form(count, roleAdoptCouponViews));
    }

    /** 角色折扣券 */
    public BizResult<String> adoptCouponCreate(MultipartFile file, BaseCouponAddParam param) {
        BizResult<String> result;
        try {
            result = couponProcessorSelector.getCouponProcessor(param).addCouponAndSendMsg(file, param);
        } catch (Exception e) {
            log.error("adoptCouponCreate error", e);
            result = BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "新增领养券失败");
        }
        String fileName = null;
        if (null != file && !file.isEmpty()) {
            fileName = file.getOriginalFilename();
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_ADOPT_COUPON_ADD_OR_UPDATE)
                .add("fileName", fileName)
                .add("param", JsonUtils.writeValueAsString(param))
                .add("couponType", CouponConfigType.ADOPT_ROLE.getCode());
        OperateLogUtils.asyncRecord(operation);
        return result;
    }

    public BizResult<Void> publish(int id) {
        RoleAdoptCouponConfig couponConfig = roleAdoptCouponConfigRepository.selectById(id);
        if (Objects.isNull(couponConfig)) {
            log.error("adopt coupon config is not exist, id={}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "折扣券配置信息不存在");
        }
        if (!couponConfig.getStatus().equals(CouponStatusType.INIT.getCode()) && !couponConfig.getStatus().equals(CouponStatusType.OFFLINE.getCode())) {
            log.error("adopt coupon config status not offline , id={}, status={}", id, couponConfig.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "折扣券非已下架或初始状态");
        }
        couponConfig.setStatus(CouponStatusType.ONLINE.getCode());
        roleAdoptCouponConfigRepository.update(couponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ONLINE_COUPON)
                .add("couponId", couponConfig.getId())
                .add("couponType", CouponConfigType.ADOPT_ROLE.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> offline(int id) {
        RoleAdoptCouponConfig couponConfig = roleAdoptCouponConfigRepository.selectById(id);
        if (Objects.isNull(couponConfig)) {
            log.error("adopt coupon config is not exist, id={}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "折扣券配置信息不存在");
        }
        if (!couponConfig.getStatus().equals(CouponStatusType.ONLINE.getCode())) {
            log.error("adopt coupon config status not online , id={}, status={}", id, couponConfig.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "折扣券非上架状态");
        }
        couponConfig.setStatus(CouponStatusType.OFFLINE.getCode());
        roleAdoptCouponConfigRepository.update(couponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.OFFLINE_COUPON)
                .add("couponId", couponConfig.getId())
                .add("couponType", CouponConfigType.ADOPT_ROLE.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> update(int id, int status) {
        RoleAdoptCouponConfig couponConfig = roleAdoptCouponConfigRepository.selectById(id);
        couponConfig.setProgress(status);
        roleAdoptCouponConfigRepository.update(couponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_ADOPT_COUPON_ADD_OR_UPDATE)
                .add("couponId", couponConfig.getId())
                .add("status", status)
                .add("couponType", CouponConfigType.ADOPT_ROLE.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }
}
