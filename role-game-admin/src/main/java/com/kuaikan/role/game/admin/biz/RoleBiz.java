package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.admin.component.QiniuComponent.FREE_BUCKET_NAME;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.bean.Topic;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.service.TopicService;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.MaterialConstants;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.component.MaterialComponent;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.config.ApolloConfig;
import com.kuaikan.role.game.admin.model.param.AdoptAnimationUpdateParam;
import com.kuaikan.role.game.admin.model.param.ReactionAfterFeedingUpdateParam;
import com.kuaikan.role.game.admin.model.param.RoleAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.RoleCostumeConfigParam;
import com.kuaikan.role.game.admin.model.param.SpineMaterialParam;
import com.kuaikan.role.game.admin.model.param.UpdateRoleUnhealthyParam;
import com.kuaikan.role.game.admin.model.view.AdoptAnimationView;
import com.kuaikan.role.game.admin.model.view.CostumeView;
import com.kuaikan.role.game.admin.model.view.ReactionAfterFeedingView;
import com.kuaikan.role.game.admin.model.view.RoleCostumeConfigView;
import com.kuaikan.role.game.admin.model.view.RoleUnhealthyView;
import com.kuaikan.role.game.admin.model.view.RoleView;
import com.kuaikan.role.game.admin.model.view.SceneView;
import com.kuaikan.role.game.admin.model.view.StoryView;
import com.kuaikan.role.game.admin.model.view.TrapConfigZipFileUrlView;
import com.kuaikan.role.game.admin.repository.CostumeRepository;
import com.kuaikan.role.game.admin.repository.RoleBubbleRepository;
import com.kuaikan.role.game.admin.repository.RoleBuildingScheduleConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleCostumeRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupSceneRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.repository.SceneRepository;
import com.kuaikan.role.game.admin.repository.StoryRepository;
import com.kuaikan.role.game.admin.repository.StoryRoleRelationRepository;
import com.kuaikan.role.game.admin.template.numerical.NumericalTemplateFactory;
import com.kuaikan.role.game.admin.template.reader.ExcelReader;
import com.kuaikan.role.game.admin.template.reader.ExcelReaderFactory;
import com.kuaikan.role.game.admin.utils.FileUtils;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleBubbleConfig;
import com.kuaikan.role.game.api.bean.RoleBuildingScheduleConfig;
import com.kuaikan.role.game.api.bean.RoleCostumeRelation;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.bean.Scene;
import com.kuaikan.role.game.api.bean.Story;
import com.kuaikan.role.game.api.bean.StoryAvgConfig;
import com.kuaikan.role.game.api.bean.StoryRoleRelation;
import com.kuaikan.role.game.api.bo.ExcelReaderResult;
import com.kuaikan.role.game.api.bo.FailMessage;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.api.enums.CostumeStatus;
import com.kuaikan.role.game.api.enums.ExcelReaderType;
import com.kuaikan.role.game.api.enums.NumericalConfigType;
import com.kuaikan.role.game.api.enums.RoleBubbleType;
import com.kuaikan.role.game.api.enums.RoleStatus;
import com.kuaikan.role.game.api.enums.SceneStatus;
import com.kuaikan.role.game.api.enums.StoryType;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;
import com.kuaikan.role.game.common.bean.RoleGroupSceneRelation;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.common.enums.RoleBubbleContentType;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
@Service
@Slf4j
public class RoleBiz {

    public static final String ROLE_TRAP_CONFIG_FILE_PATH = "role/game/zip/roleTrap/%d/";
    public static final String ROLE_TRAP_CONFIG_LOCAL_FILE_PATH = "/data/roleTrap/%d/";
    public static final String BUBBLE_IMG_TMP_FILE_KEY = "role/game/bubble/os/%d/";

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private RoleCostumeRelationRepository roleCostumeRelationRepository;

    @Resource
    private CostumeRepository costumeRepository;

    @Resource
    private SceneRepository sceneRepository;

    @Resource
    private SceneBiz sceneBiz;

    @Resource
    private TopicService topicService;

    @Resource
    private StoryRepository storyRepository;
    @Resource
    private ApolloConfig apolloConfig;

    @Resource
    private StoryRoleRelationRepository storyRoleRelationRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private RoleGroupSceneRelationRepository roleGroupSceneRelationRepository;
    @Resource
    private RoleGroupRepository roleGroupRepository;
    @Resource
    private RoleBuildingScheduleConfigRepository roleBuildingScheduleConfigRepository;
    @Resource
    private RedDotService redDotService;
    @Resource
    private QiniuComponent qiniuComponent;
    @Resource
    private NumericalBiz numericalBiz;
    @Resource
    private RoleBubbleRepository roleBubbleRepository;
    @Resource
    private MaterialComponent materialComponent;
    @Resource
    private NumericalTemplateFactory numericalTemplateFactory;
    @Resource
    private ExcelReaderFactory excelReaderFactory;

    public BizResult<List<FailMessage>> addOrUpdate(RoleAddOrUpdateParam roleAddOrUpdateParam) throws IOException {
        if (roleAddOrUpdateParam.isParamIllegal()) {
            log.error("addOrUpdate param is illegal, roleAddOrUpdateParam:{}", roleAddOrUpdateParam);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "参数不合法");
        }
        int topicId = roleAddOrUpdateParam.getTopicId();
        Topic topic = topicService.getTopicById(topicId);
        String topicName = Optional.ofNullable(topic).map(Topic::getTitle).orElse("");
        int id = roleAddOrUpdateParam.getId();
        Operation operation;
        List<FailMessage> failMessages = Lists.newArrayList();
        Role result = null;
        if (id > 0) {
            Role role = roleRepository.queryById(id);
            if (role == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
            }
            failMessages = saveBubbleConfig(id, roleAddOrUpdateParam.getOsMaterialUrl(), RoleBubbleType.OS, RoleBubbleConfig.DEFAULT_TARGET_ID);
            failMessages.addAll(saveTrapBubbleConfig(id, roleAddOrUpdateParam.getTrapMaterialUrl()));
            if (CollectionUtils.isNotEmpty(failMessages)) {
                return BizResult.result(failMessages, RoleGameResponse.ROLE_EXCEL_CONFIG_FAIL);
            }
            Role.Config config = role.getConfig();
            if (config == null) {
                config = new Role.Config();
            }
            config.setIntroductionImage(roleAddOrUpdateParam.getIntroductionImage())
                    .setPersonalityLabel(roleAddOrUpdateParam.getPersonalityLabel())
                    .setPersonalityLabelUnlock(roleAddOrUpdateParam.getPersonalityLabelUnlock())
                    .setPersonalityTabImage(roleAddOrUpdateParam.getPersonalityTabImage())
                    .setUnhealthyStateMaterial(SpineMaterialParam.toMaterialInfo(roleAddOrUpdateParam.getUnhealthyStateMaterial()))
                    .setRoleTags(roleAddOrUpdateParam.getRoleTags())
                    .setRoleIntroduction(roleAddOrUpdateParam.getRoleIntroduction())
                    .setRelatedRoleTags(roleAddOrUpdateParam.getRelatedRoleTags());
            Role update = role.setName(roleAddOrUpdateParam.getName())
                    .setTopicId(roleAddOrUpdateParam.getTopicId())
                    .setTopicName(topicName)
                    .setImage(roleAddOrUpdateParam.getImage())
                    .setAvatar(roleAddOrUpdateParam.getAvatar())
                    .setConfig(config);
            result = update;
            roleRepository.updateByPrimaryKeySelective(update);
            operation = Operation.of(OperationConstants.OperateSubType.ROLE_ADD_OR_UPDATE)
                    .add("oldData", JsonUtils.writeValueAsString(role))
                    .add("newData", JsonUtils.writeValueAsString(update));
        } else {
            Role.Config config = new Role.Config().setIntroductionImage(roleAddOrUpdateParam.getIntroductionImage())
                    .setPersonalityLabel(roleAddOrUpdateParam.getPersonalityLabel())
                    .setPersonalityLabelUnlock(roleAddOrUpdateParam.getPersonalityLabelUnlock())
                    .setPersonalityTabImage(roleAddOrUpdateParam.getPersonalityTabImage())
                    .setUnhealthyStateMaterial(SpineMaterialParam.toMaterialInfo(roleAddOrUpdateParam.getUnhealthyStateMaterial()));
            List<Role.Config.DimensionConfig> dimensionConfigs = ListUtils.emptyIfNull(roleAddOrUpdateParam.getDimensionConfigs())
                    .stream()
                    .map(e -> new Role.Config.DimensionConfig().setDimensionType(e.getDimensionType()).setInitLevel(e.getInitLevel()))
                    .collect(Collectors.toList());
            config.setDimensionConfigs(dimensionConfigs);
            config.setRoleTags(roleAddOrUpdateParam.getRoleTags())
                    .setRoleIntroduction(roleAddOrUpdateParam.getRoleIntroduction())
                    .setRelatedRoleTags(roleAddOrUpdateParam.getRelatedRoleTags());
            Role role = new Role().setName(roleAddOrUpdateParam.getName())
                    .setTopicId(roleAddOrUpdateParam.getTopicId())
                    .setTopicName(topicName)
                    .setImage(roleAddOrUpdateParam.getImage())
                    .setStatus(RoleStatus.NOT_ONLINE.getCode())
                    .setAvatar(roleAddOrUpdateParam.getAvatar())
                    .setConfig(config);
            roleRepository.insert(role);
            result = role;
            id = role.getId();
            failMessages = saveBubbleConfig(id, roleAddOrUpdateParam.getOsMaterialUrl(), RoleBubbleType.OS, RoleBubbleConfig.DEFAULT_TARGET_ID);
            failMessages.addAll(saveTrapBubbleConfig(id, roleAddOrUpdateParam.getTrapMaterialUrl()));
            if (CollectionUtils.isNotEmpty(failMessages)) {
                return BizResult.result(failMessages, RoleGameResponse.ROLE_EXCEL_CONFIG_FAIL);
            }
            operation = Operation.of(OperationConstants.OperateSubType.ROLE_ADD_OR_UPDATE).add("newData", JsonUtils.writeValueAsString(role));
        }
        deleteRoleCache(result);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    private List<FailMessage> saveBubbleConfig(int roleId, String bubbleMaterialUrl, RoleBubbleType roleBubbleType, int targetId) throws IOException {
        if (StringUtils.isEmpty(bubbleMaterialUrl)) {
            //为空则走删除逻辑
            RoleBubbleConfig roleBubbleConfig = roleBubbleRepository.selectByRoleIdAndTypeAndTargetId(roleId, roleBubbleType.getCode(), targetId);
            if (roleBubbleConfig != null) {
                roleBubbleRepository.delete(roleBubbleConfig.getId());
            }
        }
        String outFilePath = MaterialConstants.TMP_BUBBLE + System.currentTimeMillis() + roleId;
        List<File> files = materialComponent.unzipMaterial(bubbleMaterialUrl, outFilePath);
        Map<String, String> file2UriMap = uploadImageFilesByName(files, String.format(BUBBLE_IMG_TMP_FILE_KEY, System.currentTimeMillis()));

        File excelFile = files.stream().filter(e -> e.getName().endsWith("xlsx")).findFirst().orElse(null);
        if (excelFile == null) {
            return Lists.newArrayList();
        }
        ExcelReader excelReader;
        if (RoleBubbleType.OS == roleBubbleType) {
            excelReader = excelReaderFactory.getInstance(ExcelReaderType.ROLE_OS_BUBBLE);
        } else {
            excelReader = excelReaderFactory.getInstance(ExcelReaderType.REACTION);
        }
        if (excelReader == null) {
            log.error("excelReader is null，type:{}，roleId:{}", roleBubbleType, roleId);
            return Lists.newArrayList();
        }
        ExcelReader.ReaderContext context = ExcelReader.ReaderContext.builder().roleId(roleId).build();
        BizResult<ExcelReaderResult> result = excelReader.readExcelConfig(excelFile, context);
        if (CollectionUtils.isNotEmpty(result.getData().getFailMessages())) {
            return result.getData().getFailMessages();
        }
        List<RoleBubbleConfig.Bubble> bubbleList = result.getData().getResultDataList();
        for (RoleBubbleConfig.Bubble bubble : bubbleList) {
            if (bubble.getContentType() != RoleBubbleContentType.Image.getCode()) {
                continue;
            }
            if (StringUtils.isEmpty(file2UriMap.get(bubble.getContent().toLowerCase()))) {
                FailMessage imgFailMessage = new FailMessage().setMessage(String.format("图片%s不存在", bubble.getContent())).setColumn("");
                return Lists.newArrayList(imgFailMessage);
            }
            bubble.setContent(file2UriMap.get(bubble.getContent()));
        }
        RoleBubbleConfig roleBubbleConfig = roleBubbleRepository.selectByRoleIdAndTypeAndTargetId(roleId, roleBubbleType.getCode(), targetId);
        if (roleBubbleConfig == null) {
            roleBubbleConfig = new RoleBubbleConfig().setRoleId(roleId)
                    .setType(roleBubbleType.getCode())
                    .setTargetId(targetId)
                    .setZipFileUrl(bubbleMaterialUrl)
                    .setBubbleList(bubbleList);
            roleBubbleRepository.insert(roleBubbleConfig);
        } else {
            roleBubbleConfig.setBubbleList(bubbleList);
            roleBubbleConfig.setZipFileUrl(roleBubbleConfig.getZipFileUrl());
            roleBubbleRepository.update(roleBubbleConfig);
        }
        log.info("saveBubbleConfig success, roleId:{}, roleBubbleType:{}, targetId:{}, roleBubbleConfig:{}", roleId, roleBubbleType, targetId,
                roleBubbleConfig);
        return Lists.newArrayList();
    }

    /**
     * @param files
     * @return key为路径+文件名，value为上传后的文件地址
     */
    private Map<String, String> uploadImageFiles(List<File> files, String uploadPath) {
        if (CollectionUtils.isEmpty(files)) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMap();
        for (File file : files) {
            if (file.isDirectory()) {
                continue;
            }
            if (!file.getName().endsWith("png") && !file.getName().endsWith("jpg") && !file.getName().endsWith("jpeg")) {
                continue;
            }
            String fileKey = uploadPath + file.getName();
            final String absolutePath = file.getAbsolutePath();
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, absolutePath, fileKey);
            result.put(absolutePath, fileKey);
        }
        return result;
    }

    private Map<String, String> uploadImageFilesByName(List<File> files, String uploadPath) {
        if (CollectionUtils.isEmpty(files)) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMap();
        for (File file : files) {
            if (file.isDirectory()) {
                continue;
            }
            if (!file.getName().toLowerCase().endsWith("png") && !file.getName().toLowerCase().endsWith("jpg") && !file.getName()
                    .toLowerCase()
                    .endsWith("jpeg")) {
                continue;
            }
            String fileKey = uploadPath + file.getName();
            final String absolutePath = file.getAbsolutePath();
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, absolutePath, fileKey);
            result.put(file.getName().toLowerCase(), fileKey);
        }
        return result;
    }

    public BizResult<List<RoleView>> queryByNameLike(String name) {
        final List<Role> roles = roleRepository.queryByNameLike(name);
        if (CollectionUtils.isEmpty(roles)) {
            return BizResult.success(Lists.newArrayList());
        }
        List<Integer> roleIds = Lists.transform(roles, Role::getId);
        List<RoleBubbleConfig> bubbleConfigs = roleBubbleRepository.selectByRoleIds(roleIds);
        Map<Integer, RoleBubbleConfig> roleIdOsBubbleMap = bubbleConfigs.stream()
                .filter(e -> e.getType() == RoleBubbleType.OS.getCode())
                .collect(Collectors.toMap(RoleBubbleConfig::getRoleId, e -> e, (newVal, oldVal) -> oldVal));
        Map<Integer, RoleBubbleConfig> roleIdTrapBubbleMap = bubbleConfigs.stream()
                .filter(e -> e.getType() == RoleBubbleType.TRAP.getCode())
                .collect(Collectors.toMap(RoleBubbleConfig::getRoleId, e -> e, (newVal, oldVal) -> oldVal));
        final List<RoleView> roleViews = roles.stream()
                .map(role -> RoleView.valueOf(role, Maps.newHashMap(), Maps.newHashMap(), Maps.newHashMap(), roleIdTrapBubbleMap.get(role.getId()),
                        roleIdOsBubbleMap.get(role.getId())))
                .collect(Collectors.toList());
        return BizResult.success(roleViews);
    }

    public BizResult<PageView<RoleView>> list(int pageNum, int pageSize) {
        int count = roleRepository.count();
        if (count == 0) {
            return BizResult.success(PageView.empty());
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Role> roles = roleRepository.queryByPage(offset, pageSize);
        Set<Integer> roleIds = roles.stream().map(Role::getId).collect(Collectors.toSet());
        Map<Integer, Long> costumeCountMap = costumeRepository.getRolePublishedCostumeCountMap(roleIds);
        Map<Integer, Long> roleStoryCountMap = storyRepository.getRoleStoryCountMap(roleIds);
        List<RoleBubbleConfig> bubbleConfigs = roleBubbleRepository.selectByRoleIds(roleIds);
        Map<Integer, RoleBubbleConfig> roleIdOsBubbleMap = bubbleConfigs.stream()
                .filter(e -> e.getType() == RoleBubbleType.OS.getCode())
                .collect(Collectors.toMap(RoleBubbleConfig::getRoleId, e -> e, (newVal, oldVal) -> oldVal));
        Map<Integer, RoleBubbleConfig> roleIdTrapBubbleMap = bubbleConfigs.stream()
                .filter(e -> e.getType() == RoleBubbleType.TRAP.getCode())
                .collect(Collectors.toMap(RoleBubbleConfig::getRoleId, e -> e, (newVal, oldVal) -> oldVal));
        List<RoleView> result = roles.stream()
                .map(role -> RoleView.valueOf(role, costumeCountMap, roleStoryCountMap, Maps.newHashMap(), roleIdTrapBubbleMap.get(role.getId()),
                        roleIdOsBubbleMap.get(role.getId())))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(count, result));
    }

    public BizResult<Void> whiteListCheck(int roleId) {
        Role role = roleRepository.queryById(roleId);
        final BizResult<Void> validateResult = validateRole(role);
        if (!validateResult.isSuccess()) {
            return validateResult;
        }
        roleRepository.updateByPrimaryKeySelective(role.setStatus(RoleStatus.WHITE_LIST_CHECK.getCode()));
        deleteRoleCache(role);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.ROLE_WHITE_LIST).add("id", roleId));
        return BizResult.success();
    }

    public BizResult<Void> publish(int roleId) {
        Role role = roleRepository.queryById(roleId);
        final BizResult<Void> validateResult = validateRole(role);
        if (!validateResult.isSuccess()) {
            return validateResult;
        }
        roleRepository.updateByPrimaryKeySelective(role.setStatus(RoleStatus.ONLINE.getCode()));
        redDotService.sendAddRoleEvent();
        deleteRoleCache(role);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.ROLE_PUBLISH).add("id", roleId));
        return BizResult.success();
    }

    public BizResult<Void> offline(int roleId) {
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        roleRepository.updateByPrimaryKeySelective(role.setStatus(RoleStatus.NOT_ONLINE.getCode()));
        deleteRoleCache(role);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.ROLE_OFFLINE).add("id", roleId));
        return BizResult.success();
    }

    private BizResult<Void> validateRole(Role role) {
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        if (role.getDefaultCostumeId() == 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "请设置默认装扮");
        }
        final int roleId = role.getId();
        RoleGroupRelation roleGroupRelation = roleGroupRelationRepository.queryByRoleId(roleId);
        if (roleGroupRelation == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "上架前请先配置角色组");
        }
        List<RoleBuildingScheduleConfig> roleBuildingScheduleConfigs = roleBuildingScheduleConfigRepository.queryByRoleId(roleId);
        if (CollectionUtils.isEmpty(roleBuildingScheduleConfigs)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "上架前请先配置角色的日程影响心情值和疲劳值");
        }
        RoleGroup roleGroup = roleGroupRepository.queryById(roleGroupRelation.getRoleGroupId());
        if (roleGroup == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组不存在");
        }
        if (roleGroup.getDefaultSceneId() == 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色组未设置默认场景");
        }
        Costume costume = costumeRepository.queryById(role.getDefaultCostumeId());
        if (costume == null || costume.getStatus() != CostumeStatus.ON_SHELF.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "默认装扮不存在或已下架");
        }
        final List<StoryRoleRelation> storyRoleRelations = storyRoleRelationRepository.selectByRoleId(roleId);
        if (CollectionUtils.isEmpty(storyRoleRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "没有关联的互动剧情");
        }
        final Map<Integer, Story> storyMap = storyRepository.queryStoriesByIds(
                storyRoleRelations.stream().map(StoryRoleRelation::getStoryId).collect(Collectors.toSet()));
        // 至少有一个剧情的状态是已上架
        if (storyMap.values().stream().noneMatch(story -> story.getStatus() == CommonStatus.ONLINE.getCode())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "没有上架的互动剧情");
        }
        Scene scene = sceneRepository.selectByPrimaryKey(roleGroup.getDefaultSceneId());
        if (scene == null || scene.getStatus() != SceneStatus.ON_SHELF.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "默认场景不存在或已下架");
        }
        //校验角色不健康状态相关配置是否已配置
        Role.Config config = role.getConfig();
        if (CollectionUtils.isEmpty(config.getHappyAnimations()) || CollectionUtils.isEmpty(config.getUnhappyAnimations()) || CollectionUtils.isEmpty(
                config.getUnhealthyTalkTexts()) || CollectionUtils.isEmpty(config.getUnhealthyPokeTexts())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不健康状态相关配置未配置");
        }

        roleRepository.updateByPrimaryKeySelective(role.setStatus(RoleStatus.ONLINE.getCode()));
        redDotService.sendAddRoleEvent();
        deleteRoleCache(role);
        OperateLogUtils.asyncRecord(Operation.of(OperationConstants.OperateSubType.ROLE_PUBLISH).add("id", roleId));
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateSort(int roleId, int order) {
        List<Role> roles = roleRepository.queryAll();
        Role role = roles.stream().filter(item -> item.getId() == roleId).findAny().orElse(null);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        if (order == 0) {
            role.setOrderNum(order);
            roleRepository.updateByPrimaryKeySelective(role);
            deleteRoleCache(role);
            return BizResult.success();
        }
        int oldOrder = role.getOrderNum();
        if (order > oldOrder) {
            roleRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(order);
            role.setOrderNum(order);
            roleRepository.updateByPrimaryKeySelective(role);
        } else if (order < oldOrder) {
            Role sameOrderRole = roles.stream().filter(item -> item.getOrderNum() == order).findAny().orElse(null);
            if (sameOrderRole != null) {
                sameOrderRole.setOrderNum(oldOrder);
                roleRepository.updateByPrimaryKeySelective(sameOrderRole);
            }
            role.setOrderNum(order);
            roleRepository.updateByPrimaryKeySelective(role);
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_SORT_UPDATE)
                .add("id", roleId)
                .add("oldOrder", oldOrder)
                .add("newOrder", order);
        OperateLogUtils.asyncRecord(operation);
        deleteRoleCache(role);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateCostumeSort(int roleId, int costumeId, int order) {
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByRoleId(roleId);
        RoleCostumeRelation roleCostumeRelation = roleCostumeRelations.stream().filter(item -> item.getCostumeId() == costumeId).findFirst().orElse(null);
        if (roleCostumeRelation == null) {
            return BizResult.result(RoleGameResponse.ROLE_COSTUME_RELATION_NOT_EXIST);
        }
        if (order == 0) {
            roleCostumeRelation.setOrderNum(order);
            roleCostumeRelationRepository.updateByPrimaryKeySelective(roleCostumeRelation);
            return BizResult.success();
        }
        int oldOrder = roleCostumeRelation.getOrderNum();
        if (order > oldOrder) {
            roleCostumeRelationRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(order, roleId);
            roleCostumeRelation.setOrderNum(order);
            roleCostumeRelationRepository.updateByPrimaryKeySelective(roleCostumeRelation);
        } else if (order < oldOrder) {
            RoleCostumeRelation sameOrderRoleCostumeRelation = roleCostumeRelations.stream().filter(item -> item.getOrderNum() == order).findAny().orElse(null);
            if (sameOrderRoleCostumeRelation != null) {
                sameOrderRoleCostumeRelation.setOrderNum(oldOrder);
                roleCostumeRelationRepository.updateByPrimaryKeySelective(sameOrderRoleCostumeRelation);
            }
            roleCostumeRelation.setOrderNum(order);
            roleCostumeRelationRepository.updateByPrimaryKeySelective(roleCostumeRelation);
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_COSTUME_SORT_UPDATE)
                .add("id", roleId)
                .add("costumeId", costumeId)
                .add("oldOrder", oldOrder)
                .add("newOrder", order);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateStorySort(int roleId, int storyId, int orderNum) {
        List<StoryRoleRelation> storyRoleRelations = storyRoleRelationRepository.selectByRoleId(roleId);
        StoryRoleRelation relation = storyRoleRelations.stream().filter(item -> item.getStoryId() == storyId).findFirst().orElse(null);
        if (relation == null) {
            return BizResult.result(RoleGameResponse.ROLE_STORY_RELATION_NOT_EXIST);
        }
        if (orderNum == 0) {
            storyRoleRelationRepository.updateOrderNum(orderNum, roleId, storyId);
            return BizResult.success();
        }
        if (orderNum > relation.getOrderNum()) {
            storyRoleRelationRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(orderNum, roleId);
            storyRoleRelationRepository.updateOrderNum(orderNum, roleId, storyId);
        } else if (orderNum < relation.getOrderNum()) {
            storyRoleRelations.stream()
                    .filter(item -> item.getOrderNum() == orderNum)
                    .findAny()
                    .ifPresent(storyRoleRelation -> storyRoleRelationRepository.updateOrderNum(relation.getOrderNum(), roleId, storyRoleRelation.getStoryId()));
            storyRoleRelationRepository.updateOrderNum(orderNum, roleId, storyId);
        }
        return BizResult.success();
    }

    public BizResult<Void> defaultCostume(int roleId, int costumeId) {
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        Optional<RoleCostumeRelation> haveCostume = roleCostumeRelationRepository.queryByRoleId(roleId)
                .stream()
                .filter(item -> item.getCostumeId() == costumeId)
                .findFirst();
        if (!haveCostume.isPresent()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在对应装扮");
        }
        Costume costume = costumeRepository.queryById(costumeId);
        if (costume == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮不存在");
        }
        if (role.getStatus() == RoleStatus.ONLINE.getCode() && costume.getStatus() != CostumeStatus.ON_SHELF.getCode()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "上架角色不能设置下架装扮");
        }
        roleRepository.updateByPrimaryKeySelective(role.setDefaultCostumeId(costumeId));
        deleteRoleCache(role);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_DEFAULT_COSTUME).add("id", roleId).add("costumeId", costumeId);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<PageView<CostumeView>> costumeList(int roleId, int pageNum, int pageSize) {
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        int total = roleCostumeRelationRepository.countByRoleId(roleId);
        if (total == 0) {
            return BizResult.success(PageView.empty());
        }
        int offset = (pageNum - 1) * pageSize;
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryPageByRoleId(roleId, offset, pageSize);
        Set<Integer> costumeIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toSet());
        List<Costume> costumes = costumeRepository.queryByIds(costumeIds);
        Map<Integer, Costume> costumeMap = costumes.stream().collect(Collectors.toMap(Costume::getId, c -> c));
        List<RoleCostumeRelation> costumeId2Role = roleCostumeRelationRepository.queryByCostumeIds(costumeIds);
        Set<Integer> roleIds = costumeId2Role.stream().map(RoleCostumeRelation::getRoleId).collect(Collectors.toSet());
        Map<Integer, Role> roleMap = roleRepository.queryByIds(roleIds).stream().collect(Collectors.toMap(Role::getId, value -> value));
        Map<Integer, Integer> costumeId2RoleIdMap = costumeId2Role.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, RoleCostumeRelation::getRoleId));
        List<CostumeView> result = roleCostumeRelations.stream().map(item -> {
            int defaultCostumeId = role.getDefaultCostumeId();
            Integer costumeId = item.getCostumeId();
            boolean isDefaultCostume = defaultCostumeId == costumeId;
            return CostumeView.valueOf(costumeMap.get(costumeId), costumeId2RoleIdMap, roleMap, item.getOrderNum(), isDefaultCostume);
        }).collect(Collectors.toList());
        return BizResult.success(PageView.form(total, result));
    }

    public BizResult<PageView<SceneView>> sceneList(int roleIdParam, int pageNum, int pageSize) {
        Role role = roleRepository.queryById(roleIdParam);
        if (role == null) {
            return BizResult.result(RoleGameResponse.ROLE_NOT_EXIST);
        }
        RoleGroupRelation roleGroupRelation = roleGroupRelationRepository.queryByRoleId(roleIdParam);
        if (roleGroupRelation == null) {
            return BizResult.result(RoleGameResponse.ROLE_NOT_EXIST);
        }
        int offset = (pageNum - 1) * pageSize;
        int groupId = roleGroupRelation.getRoleGroupId();
        List<RoleGroupSceneRelation> roleGroupSceneRelations = roleGroupSceneRelationRepository.queryPageByGroupId(groupId, offset, pageSize);
        Map<Integer, Integer> sceneOrderNumMap = roleGroupSceneRelations.stream()
                .collect(Collectors.toMap(RoleGroupSceneRelation::getSceneId, RoleGroupSceneRelation::getOrderNum));
        List<Integer> sceneIds = roleGroupSceneRelations.stream().map(RoleGroupSceneRelation::getSceneId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sceneIds)) {
            return BizResult.success(PageView.empty());
        }
        List<Scene> scenes = sceneRepository.queryBySceneIds(sceneIds);
        RoleGroup roleGroup = roleGroupRepository.queryById(groupId);
        List<SceneView> sceneViews = sceneBiz.getSceneViews(sceneIds, scenes, sceneOrderNumMap, roleGroup)
                .stream()
                .sorted(Comparator.comparing(SceneView::getOrderNum).thenComparing(Comparator.comparing(SceneView::getCreateTime).reversed()))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(roleGroupSceneRelationRepository.countByRoleGroupId(groupId), sceneViews));
    }

    public BizResult<PageView<StoryView>> storyList(int roleId, int pageNum, int pageSize) {
        int total = storyRoleRelationRepository.countByRoleId(roleId);
        if (total == 0) {
            return BizResult.success(PageView.empty());
        }
        int offset = (pageNum - 1) * pageSize;

        List<StoryRoleRelation> storyRoleRelationList = storyRoleRelationRepository.queryByPageAndRoleId(roleId, offset, pageSize);
        Set<Integer> storyIds = storyRoleRelationList.stream().map(StoryRoleRelation::getStoryId).collect(Collectors.toSet());

        List<StoryRoleRelation> storyAllRelations = storyRoleRelationRepository.selectByStoryIds(storyIds);
        Map<Integer, Story> storyMap = storyRepository.queryStoriesByIds(storyIds);

        Map<Integer, List<StoryRoleRelation>> storyRoleRelationMap = storyAllRelations.stream().collect(Collectors.groupingBy(StoryRoleRelation::getStoryId));
        List<Role> roles = roleRepository.queryByIds(storyAllRelations.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toSet()));
        Map<Integer, Role> roleMap = roles.stream().collect(Collectors.toMap(Role::getId, role -> role));

        List<Costume> costumes = costumeRepository.queryByIds(storyMap.values().stream().map(Story::getRewardCostumeId).collect(Collectors.toSet()));
        Map<Integer, Costume> costumeMap = costumes.stream().collect(Collectors.toMap(Costume::getId, costume -> costume));
        List<StoryView> storyViews = storyRoleRelationList.stream().map(relation -> {
            Story story = storyMap.get(relation.getStoryId());
            if (story == null) {
                log.error("story not found, storyId:{}", relation.getStoryId());
                return null;
            }
            StoryView storyView = new StoryView().setId(story.getId())
                    .setName(story.getName())
                    .setLevel(story.getLevel())
                    .setType(story.getType())
                    .setStatus(story.getStatus())
                    .setRewardCostumeId(story.getRewardCostumeId())
                    .setRewardCostumeName(story.getRewardCostumeId() == 0 ? null : costumeMap.get(story.getRewardCostumeId()).getName())
                    .setCanRepeat(story.isCanRepeat())
                    .setScheduleId(story.getScheduleId());
            List<StoryRoleRelation> storyRoleRelationsByStory = storyRoleRelationMap.get(story.getId());
            if (CollectionUtils.isNotEmpty(storyRoleRelationsByStory)) {
                storyView.setRoleIds(storyRoleRelationsByStory.stream().map(StoryRoleRelation::getRoleId).collect(Collectors.toList()))
                        .setRoleNames(storyRoleRelationsByStory.stream()
                                .map(storyRoleRelation -> roleMap.get(storyRoleRelation.getRoleId()).getName())
                                .collect(Collectors.toList()));
            }
            if (story.getType().equals(StoryType.AVG.getCode())) {
                StoryAvgConfig storyAvgConfig = JsonUtils.findObject(story.getConfig(), StoryAvgConfig.class);
                storyView.setAvgChapterId(storyAvgConfig.getAvgChapterId());
                storyView.setAvgUnlockingConditions(storyAvgConfig.getUnlockingConditions());
            }
            storyView.setOrderNum(relation.getOrderNum());
            return storyView;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return BizResult.success(PageView.form(total, storyViews));
    }

    public BizResult<RoleCostumeConfigView> queryCostumeConfig(int roleId) {
        List<RoleCostumeRelation> roleCostumeRelations = roleCostumeRelationRepository.queryByRoleId(roleId);
        if (CollectionUtils.isEmpty(roleCostumeRelations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在对应装扮");
        }
        final Map<Integer, RoleCostumeRelation> roleCostumeRelationMap = roleCostumeRelations.stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));
        Set<Integer> costumeIds = roleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toSet());
        List<Costume> costumes = costumeRepository.queryByIds(costumeIds)
                .stream()
                .filter(item -> item.getStatus() == CostumeStatus.ON_SHELF.getCode())
                .collect(Collectors.toList());
        RoleCostumeConfigView roleCostumeConfigView = new RoleCostumeConfigView();
        roleCostumeConfigView.setConfigs(costumes.stream()
                .map(costume -> {
                    RoleCostumeRelation roleCostumeRelation = roleCostumeRelationMap.get(costume.getId());
                    return RoleCostumeConfigView.Config.valueOf(costume, roleCostumeRelation.getConfig(), null);
                })
                .sorted(Comparator.comparing(RoleCostumeConfigView.Config::getLevel)
                        .reversed()
                        .thenComparing(Comparator.comparing(RoleCostumeConfigView.Config::getId).reversed()))
                .collect(Collectors.toList()));

        return BizResult.success(roleCostumeConfigView);
    }

    public BizResult<Void> updateCostumeConfig(RoleCostumeConfigParam param) {
        final List<RoleCostumeConfigParam.Config> configs = param.getConfigs();
        if (CollectionUtils.isEmpty(configs)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮配置不能为空");
        }
        final Map<Integer, RoleCostumeRelation> roleCostumeRelationMap = roleCostumeRelationRepository.queryByRoleId(param.getRoleId())
                .stream()
                .collect(Collectors.toMap(RoleCostumeRelation::getCostumeId, Function.identity()));
        BigDecimal totalProbability = BigDecimal.ZERO;
        final List<RoleCostumeRelation> needUpdateRoleCostumeRelations = new ArrayList<>();
        for (RoleCostumeConfigParam.Config config : configs) {
            final RoleCostumeRelation roleCostumeRelation = roleCostumeRelationMap.get(config.getCostumeId());
            if (roleCostumeRelation == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "装扮与角色关系不存在");
            }
            if (config.getProbability() < 0 || config.getProbability() > 100d) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "概率范围错误");
            }
            totalProbability = totalProbability.add(BigDecimal.valueOf(config.getProbability()));

            RoleCostumeRelation update = new RoleCostumeRelation().setCostumeId(roleCostumeRelation.getCostumeId())
                    .setRoleId(roleCostumeRelation.getRoleId())
                    .setConfig(new RoleCostumeRelation.Config().setLotteryProbability(config.getProbability())
                            .setCanLottery(config.isCanLottery())
                            .setCanCompose(config.isCanCompose()));
            needUpdateRoleCostumeRelations.add(update);
        }
        if (totalProbability.compareTo(BigDecimal.valueOf(100)) != 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "概率总和不等于100");
        }
        roleCostumeRelationRepository.updateBatchConfig(needUpdateRoleCostumeRelations);
        deleteCostumeCache(needUpdateRoleCostumeRelations.stream().map(RoleCostumeRelation::getCostumeId).collect(Collectors.toList()), param.getRoleId());
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_COSTUME_CONFIG_UPDATE)
                .add("roleId", param.getRoleId())
                .add("configs", needUpdateRoleCostumeRelations.stream().map(RoleCostumeRelation::getConfig).collect(Collectors.toList()));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    private void deleteRoleCache(Role role) {
        if (role == null) {
            return;
        }
        int roleId = role.getId();
        int topicId = role.getTopicId();
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(CacheConfig.ROLE_INFO.getReadWriteVip());
        String key1 = KeyGenerator.generate(CacheConfig.ROLE_INFO.getKeyPattern(), roleId);
        String key2 = KeyGenerator.generate(CacheConfig.ALL_ROLE_INFO.getKeyPattern());
        String key3 = KeyGenerator.generate(CacheConfig.TOPIC_ROLE_LIST.getKeyPattern(), topicId);
        List<String> keys = new ArrayList<>();
        keys.add(key1);
        keys.add(key2);
        keys.add(key3);
        clusterClientByName.del(keys.toArray(new String[0]));
    }

    private void deleteCostumeCache(Collection<Integer> costumeIds, int roleId) {
        LettuceClusterClient clusterClientByName = LettuceClusterUtil.getClusterClientByName(CacheConfig.ROLE_COSTUME_RELATION_BY_COSTUME_ID.getReadWriteVip());
        List<String> keys = costumeIds.stream()
                .map(id -> KeyGenerator.generate(CacheConfig.ROLE_COSTUME_RELATION_BY_COSTUME_ID.getKeyPattern(), id))
                .collect(Collectors.toList());
        keys.add(KeyGenerator.generate(CacheConfig.ROLE_COSTUME_RELATION_BY_ROLE_ID.getKeyPattern(), roleId));
        clusterClientByName.del(keys.toArray(new String[0]));
        log.info("delete costume cache, costumeIds:{}", costumeIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<List<FailMessage>> updateTrapConfig(MultipartFile file, Integer roleId) {
        if (file == null) {
            return BizResult.result(RoleGameResponse.BAD_REQUEST.getCode(), "文件不能为空");
        }
        if (roleId == null) {
            return BizResult.result(RoleGameResponse.BAD_REQUEST.getCode(), "角色id不能为空");
        }
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(RoleGameResponse.BAD_REQUEST.getCode(), "角色不存在");
        }
        String path = String.format(ROLE_TRAP_CONFIG_FILE_PATH, roleId) + file.getOriginalFilename();
        String localPath = String.format(ROLE_TRAP_CONFIG_LOCAL_FILE_PATH, roleId) + file.getOriginalFilename();
        try {
            boolean isSuccess = FileUtils.saveMultipartFile(file, localPath);
            if (!isSuccess) {
                return BizResult.result(RoleGameResponse.BAD_REQUEST.getCode(), "文件保存失败");
            }
            List<File> unzipFileList = FileUtils.unzip(localPath);
            if (CollectionUtils.isEmpty(unzipFileList)) {
                return BizResult.result(RoleGameResponse.BAD_REQUEST.getCode(), "文件解压失败");
            }
            List<String> imageExtensions = Lists.newArrayList(".png", ".jpg", ".jpeg", ".gif", ".bmp"); // 添加需要的图片扩展名
            List<String> imageNameList = unzipFileList.stream().filter(f -> {
                String fileName = f.getName().toLowerCase();
                return imageExtensions.stream().anyMatch(fileName::endsWith);
            }).map(File::getName).collect(Collectors.toList());

            List<File> imageList = unzipFileList.stream().filter(f -> {
                String fileName = f.getName().toLowerCase();
                return imageExtensions.stream().anyMatch(fileName::endsWith);
            }).collect(Collectors.toList());

            List<File> excelList = unzipFileList.stream().filter(f -> f.getName().toLowerCase().endsWith(".xlsx")).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(excelList)) {
                return BizResult.result(RoleGameResponse.ROLE_EXCEL_CONFIG_FAIL.getCode(), "缺少excel文件");
            }
            File excelFile = excelList.get(0);
            if (excelList.size() != 1) {
                excelFile = excelList.stream().filter(f -> f.getName().equals("城市受困表格模板.xlsx")).findFirst().orElse(null);
            }
            if (excelFile == null) {
                return BizResult.result(RoleGameResponse.ROLE_EXCEL_CONFIG_FAIL.getCode(), "缺少命名为城市受困表格模板.xlsx的excel文件");
            }
            RoleBubbleConfig oldData = roleBubbleRepository.selectByRoleIdAndTypeAndTargetId(roleId, RoleBubbleType.TRAP.getCode(),
                    RoleBubbleConfig.DEFAULT_TARGET_ID);
            List<FailMessage> failMessages = numericalBiz.updateTrapConfig(excelFile, roleId, imageNameList, imageList);
            if (CollectionUtils.isNotEmpty(failMessages)) {
                return BizResult.success(failMessages);
            }
            String url = qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, file.getBytes(), path);
            RoleBubbleConfig roleBubbleConfig = roleBubbleRepository.selectByRoleIdAndTypeAndTargetId(roleId, RoleBubbleType.TRAP.getCode(),
                    RoleBubbleConfig.DEFAULT_TARGET_ID);
            roleBubbleConfig.setZipFileUrl(url);
            roleBubbleRepository.update(roleBubbleConfig);
            Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_TRAP_CONFIG_UPDATE)
                    .add("id", roleId)
                    .add("oldData", JsonUtils.writeValueAsString(oldData))
                    .add("newData", JsonUtils.writeValueAsString(roleBubbleConfig));
            OperateLogUtils.asyncRecord(operation);
        } catch (IOException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
        } finally {
            FileUtils.deleteDirectory(localPath);
        }
        return BizResult.success();
    }

    private List<FailMessage> saveTrapBubbleConfig(int roleId, String trapMaterialUrl) {
        if (StringUtils.isEmpty(trapMaterialUrl)) {
            //为空则走删除逻辑
            RoleBubbleConfig roleBubbleConfig = roleBubbleRepository.selectByRoleIdAndTypeAndTargetId(roleId, RoleBubbleType.TRAP.getCode(),
                    RoleBubbleConfig.DEFAULT_TARGET_ID);
            if (roleBubbleConfig != null) {
                roleBubbleRepository.delete(roleBubbleConfig.getId());
            }
            return Lists.newArrayList();
        }
        String outFilePath = MaterialConstants.TMP_TRAP_BUBBLE + System.currentTimeMillis() + roleId;
        List<File> unzipFileList = materialComponent.unzipMaterialWithUTF8(trapMaterialUrl, outFilePath);
        if (CollectionUtils.isEmpty(unzipFileList)) {
            return Lists.newArrayList(new FailMessage().setMessage("文件解压失败").setNumericalConfigType(NumericalConfigType.ROLE_TRAP.getDesc()));
        }
        List<String> imageExtensions = Lists.newArrayList(".png", ".jpg", ".jpeg", ".gif", ".bmp");
        List<String> imageNameList = unzipFileList.stream().filter(f -> {
            String fileName = f.getName().toLowerCase();
            return imageExtensions.stream().anyMatch(fileName::endsWith);
        }).map(File::getName).collect(Collectors.toList());

        List<File> imageList = unzipFileList.stream().filter(f -> {
            String fileName = f.getName().toLowerCase();
            return imageExtensions.stream().anyMatch(fileName::endsWith);
        }).collect(Collectors.toList());

        List<File> excelList = unzipFileList.stream().filter(f -> f.getName().toLowerCase().endsWith(".xlsx")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(excelList)) {
            return Lists.newArrayList(new FailMessage().setMessage("缺少excel文件").setNumericalConfigType(NumericalConfigType.ROLE_TRAP.getDesc()));
        }
        File excelFile = excelList.get(0);
        if (excelList.size() != 1) {
            excelFile = excelList.stream().filter(f -> f.getName().equals("城市受困表格模板.xlsx")).findFirst().orElse(null);
        }
        if (excelFile == null) {
            return Lists.newArrayList(
                    new FailMessage().setMessage("缺少命名为城市受困表格模板.xlsx的excel文件").setNumericalConfigType(NumericalConfigType.ROLE_TRAP.getDesc()));
        }
        List<FailMessage> failMessages = Lists.newArrayList();
        try {
            failMessages = numericalBiz.updateTrapConfig(excelFile, roleId, imageNameList, imageList);
        } catch (IOException e) {
            log.error("update trap config error", e);
            return Lists.newArrayList(new FailMessage().setMessage("文件上传失败").setNumericalConfigType(NumericalConfigType.ROLE_TRAP.getDesc()));
        }
        if (CollectionUtils.isNotEmpty(failMessages)) {
            failMessages.forEach(failMessage -> failMessage.setNumericalConfigType(NumericalConfigType.ROLE_TRAP.getDesc()));
            return failMessages;
        }
        RoleBubbleConfig roleBubbleConfig = roleBubbleRepository.selectByRoleIdAndTypeAndTargetId(roleId, RoleBubbleType.TRAP.getCode(),
                RoleBubbleConfig.DEFAULT_TARGET_ID);
        roleBubbleConfig.setZipFileUrl(trapMaterialUrl);
        roleBubbleRepository.update(roleBubbleConfig);
        return Lists.newArrayList();
    }

    public BizResult<TrapConfigZipFileUrlView> queryTrapConfigZipFileUrl(int roleId) {
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        RoleBubbleConfig roleBubbleConfig = roleBubbleRepository.selectByRoleIdAndTypeAndTargetId(roleId, RoleBubbleType.TRAP.getCode(),
                RoleBubbleConfig.DEFAULT_TARGET_ID);
        if (roleBubbleConfig == null) {
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "受困配置尚未配置");
        }
        return BizResult.success(new TrapConfigZipFileUrlView().setZipFileUrl(CdnUtil.getDefaultDomainWithBackSlash() + roleBubbleConfig.getZipFileUrl()));
    }

    public BizResult<String> queryTrapConfigFileTemplateUrl() {
        String roleTrapFileTemplate = apolloConfig.getRoleTrapFileTemplate();
        return BizResult.success(roleTrapFileTemplate);
    }

    public BizResult<Void> updateRoleUnhealthy(UpdateRoleUnhealthyParam param) {
        Integer roleId = param.getRoleId();
        log.info("updateRoleUnhealthy, roleId:{}", roleId);
        if (roleId == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色id不能为空");
        }
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        List<String> unhealthyPokeTexts = param.getUnhealthyPokeTexts();
        if (CollectionUtils.isEmpty(unhealthyPokeTexts)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "戳一戳文案不能为空");
        }
        List<String> unhealthyTalkTexts = param.getUnhealthyTalkTexts();
        if (CollectionUtils.isEmpty(unhealthyTalkTexts)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "对话不能为空");
        }
        List<String> unhappyAnimations = param.getUnhappyAnimations();
        if (CollectionUtils.isEmpty(unhappyAnimations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不开心动画不能为空");
        }
        List<String> happyAnimations = param.getHappyAnimations();
        if (CollectionUtils.isEmpty(happyAnimations)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色开心动画不能为空");
        }
        Role.Config config = role.getConfig();
        config.setHappyAnimations(happyAnimations);
        config.setUnhappyAnimations(unhappyAnimations);
        config.setUnhealthyPokeTexts(unhealthyPokeTexts);
        config.setUnhealthyTalkTexts(unhealthyTalkTexts);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_UNHEALTHY_CONFIG_UPDATE)
                .add("id", roleId)
                .add("newData", config)
                .add("oldData", role.getConfig());
        roleRepository.updateByPrimaryKeySelective(role.setConfig(config));
        OperateLogUtils.asyncRecord(operation);
        deleteRoleCache(role);
        return BizResult.success();
    }

    public BizResult<RoleUnhealthyView> queryUnhealthy(int roleId) {
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        Role.Config config = role.getConfig();
        if (config == null) {
            log.error("角色配置不存在, roleId:{}", roleId);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色配置不存在");
        }
        return BizResult.success(new RoleUnhealthyView().setRoleId(roleId)
                .setUnhappyAnimations(config.getUnhappyAnimations())
                .setHappyAnimations(config.getHappyAnimations())
                .setUnhealthyTalkTexts(config.getUnhealthyTalkTexts())
                .setUnhealthyPokeTexts(config.getUnhealthyPokeTexts()));
    }

    public BizResult<ReactionAfterFeedingView> getReactionAfterFeeding(int roleId) {
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        List<RoleBubbleConfig> roleBubbleConfigs = roleBubbleRepository.selectByRoleIdAndType(roleId, RoleBubbleType.REACTION.getCode());
        if (CollectionUtils.isEmpty(roleBubbleConfigs)) {
            log.warn("getReactionAfterFeeding roleBubbleConfigs is empty, roleId:{}", roleId);
            return BizResult.success();
        }
        final Map<Integer, RoleBubbleConfig> bubbleConfigMap = roleBubbleConfigs.stream()
                .collect(Collectors.toMap(RoleBubbleConfig::getTargetId, Function.identity()));
        ReactionAfterFeedingView view = ReactionAfterFeedingView.valueOf(bubbleConfigMap);
        return BizResult.success(view);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> editReactionAfterFeeding(ReactionAfterFeedingUpdateParam param) throws IOException {
        int roleId = param.getRoleId();
        Role role = roleRepository.queryById(roleId);
        if (role == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        final List<RoleBubbleConfig> roleBubbleConfigs = roleBubbleRepository.selectByRoleIdAndType(roleId, RoleBubbleType.REACTION.getCode());
        roleBubbleConfigs.forEach(roleBubbleConfig -> roleBubbleRepository.delete(roleBubbleConfig.getId()));
        Operation operation = Operation.of(OperationConstants.OperateSubType.REACTION_AFTER_FEEDING_UPDATE)
                .add("id", roleId)
                .add("oldData", JsonUtils.writeValueAsString(roleBubbleConfigs));
        final List<FailMessage> failMessages = saveBubbleConfig(roleId, param.getReactionAfterFeeding().getCommon().getEatMaterialKey(),
                RoleBubbleType.REACTION, RoleBubbleConfig.DEFAULT_TARGET_ID);
        if (CollectionUtils.isNotEmpty(failMessages)) {
            log.error("editReactionAfterFeeding failMessages:{}", JsonUtils.writeValueAsString(failMessages));
            throw new RuntimeException(JsonUtils.writeValueAsString(failMessages));
        }
        final List<ReactionAfterFeedingUpdateParam.ReactionAfterFeedingConfig.Special> specials = param.getReactionAfterFeeding().getSpecials();
        if (CollectionUtils.isNotEmpty(specials)) {
            for (ReactionAfterFeedingUpdateParam.ReactionAfterFeedingConfig.Special special : specials) {
                final List<FailMessage> failMessagesList = saveBubbleConfig(roleId, special.getEatMaterialKey(), RoleBubbleType.REACTION, special.getFoodId());
                if (CollectionUtils.isNotEmpty(failMessagesList)) {
                    log.error("editReactionAfterFeeding failMessagesList:{}", JsonUtils.writeValueAsString(failMessagesList));
                    throw new RuntimeException(JsonUtils.writeValueAsString(failMessagesList));
                }
            }
        }
        operation.add("newData", JsonUtils.writeValueAsString(roleBubbleRepository.selectByRoleIdAndType(roleId, RoleBubbleType.REACTION.getCode())));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<AdoptAnimationView> queryAdoptAnimation(int roleId) {
        Role role = roleRepository.queryById(roleId);
        if (null == role) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        Role.Config config = role.getConfig();
        if (null == config) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色配置不存在");
        }
        return BizResult.success(AdoptAnimationView.valueOf(config.getAdoptAnimation(), roleId));
    }

    public BizResult<Void> updateAdoptAnimation(AdoptAnimationUpdateParam param) {
        String validatedError = param.validateError();
        if (StringUtils.isNotBlank(validatedError)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), validatedError);
        }
        Role role = roleRepository.queryById(param.getRoleId());
        if (null == role) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "角色不存在");
        }
        Role.Config.AdoptAnimation oldAdoptAnimation = role.getConfig().getAdoptAnimation();
        Role.Config.AdoptAnimation newAdoptAnimation = new Role.Config.AdoptAnimation().setGreetingText(param.getGreetingText())
                .setExpectedCpText(param.getExpectedCpText())
                .setEntranceAnimation(param.getEntranceAnimation())
                .setGreetingAnimation(param.getGreetingAnimation())
                .setExpectedCpAnimation(param.getExpectedCpAnimation());
        Role.Config config = role.getConfig().setAdoptAnimation(newAdoptAnimation);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_ADOPT_ANIMATION_UPDATE)
                .add("roleId", param.getRoleId())
                .add("newData", newAdoptAnimation)
                .add("oldData", oldAdoptAnimation);
        roleRepository.updateByPrimaryKeySelective(role.setConfig(config));
        OperateLogUtils.asyncRecord(operation);
        deleteRoleCache(role);
        return BizResult.success();
    }
}
