package com.kuaikan.role.game.admin.biz;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageInfo;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.ActivityRewardComponent;
import com.kuaikan.role.game.admin.model.param.FurnitureActivityAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.FurnitureActivityView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.model.view.SimpleRewardView;
import com.kuaikan.role.game.admin.repository.FurnitureActivityConfigRepository;
import com.kuaikan.role.game.admin.repository.FurnitureActivityFurnitureGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.FurnitureActivityRuleConfigRepository;
import com.kuaikan.role.game.admin.repository.FurnitureGroupRepository;
import com.kuaikan.role.game.admin.repository.FurnitureGroupRoleGroupRelationRepository;
import com.kuaikan.role.game.api.bean.FurnitureActivityConfig;
import com.kuaikan.role.game.api.bean.FurnitureActivityFurnitureGroupRelation;
import com.kuaikan.role.game.api.bean.FurnitureGroup;
import com.kuaikan.role.game.api.bean.FurnitureGroupRoleGroupRelation;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.api.enums.FurnitureActivityType;

/**
 * <AUTHOR>
 * @date 2025/4/17 18:08
 * @description:
 */

@Slf4j
@Service
public class FurnitureActivityBiz {

    @Resource
    private FurnitureActivityConfigRepository furnitureActivityConfigRepository;
    @Resource
    private ActivityRewardComponent activityRewardComponent;
    @Resource
    private FurnitureGroupRepository furnitureGroupRepository;
    @Resource
    private FurnitureGroupRoleGroupRelationRepository furnitureGroupRoleGroupRelationRepository;
    @Resource
    private FurnitureActivityFurnitureGroupRelationRepository furnitureActivityFurnitureGroupRelationRepository;
    @Resource
    private FurnitureActivityRuleConfigRepository furnitureActivityRuleConfigRepository;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Integer> addOrUpdate(FurnitureActivityAddOrUpdateParam param) {
        Integer furnitureActivityId = param.getId();
        String validatedError = param.validateError();
        if (StringUtils.isNotBlank(validatedError)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), validatedError);
        }
        if (Objects.equals(FurnitureActivityType.FURNITURE_ACTIVITY_LIMIT.getCode(),
                FurnitureActivityType.getByCode(param.getType()).getCode())) {
            if (!isLimitedFurnitureGroup(param.getRoleAnimation())) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "必须选择IP限定的套组");
            }
        }
        int countByRuleId = furnitureActivityRuleConfigRepository.countByRuleId(param.getRuleId());
        if (countByRuleId == 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "规则ID不存在");
        }
        Operation operation;
        if (null != furnitureActivityId && furnitureActivityId > 0) {
            FurnitureActivityConfig furnitureActivityConfig = furnitureActivityConfigRepository
                    .selectById(param.getId());
            if (furnitureActivityConfig == null) {
                return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "家具活动不存在");
            }
            String oldData = JsonUtils.writeValueAsString(furnitureActivityConfig);
            furnitureActivityConfig.setOrderId(param.getOrderId());
            furnitureActivityConfig.setName(param.getName());
            furnitureActivityConfig.setStartAt(param.getStartAt());
            furnitureActivityConfig.setEndAt(param.getEndAt());
            furnitureActivityConfig.setType(param.getType());
            furnitureActivityConfig.setConfig(param.toFurnitureActivityConfig());
            furnitureActivityConfig.setOperator(AuthContext.getCurrentUser().getName());
            furnitureActivityConfigRepository.update(furnitureActivityConfig);
            setRelations(param, furnitureActivityConfig.getId());
            operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_ACTIVITY_ADD_OR_UPDATE)
                    .add("id", furnitureActivityId)
                    .add("oldData", oldData)
                    .add("newData", JsonUtils.writeValueAsString(furnitureActivityConfig));
        } else {
            FurnitureActivityConfig furnitureActivityConfig = new FurnitureActivityConfig();
            furnitureActivityConfig.setName(param.getName());
            furnitureActivityConfig.setStartAt(param.getStartAt());
            furnitureActivityConfig.setEndAt(param.getEndAt());
            furnitureActivityConfig.setType(param.getType());
            furnitureActivityConfig.setConfig(param.toFurnitureActivityConfig());
            furnitureActivityConfig.setOperator(AuthContext.getCurrentUser().getName());
            furnitureActivityConfigRepository.insert(furnitureActivityConfig);
            setRelations(param, furnitureActivityConfig.getId());
            furnitureActivityId = furnitureActivityConfig.getId();
            operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_ACTIVITY_ADD_OR_UPDATE)
                    .add("id", furnitureActivityId)
                    .add("newData", JsonUtils.writeValueAsString(furnitureActivityConfig));
        }
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success(furnitureActivityId);
    }

    private void setRelations(FurnitureActivityAddOrUpdateParam param, int activityId) {
        List<FurnitureActivityFurnitureGroupRelation> relations;
        furnitureActivityFurnitureGroupRelationRepository.deleteByActivityId(activityId);
        if (param.isPickAllFurnitureGroup()) {
            List<FurnitureGroup> furnitureGroups = furnitureGroupRepository.queryAll();
            List<Integer> furnitureGroupIds = furnitureGroups.stream().map(FurnitureGroup::getId)
                    .collect(Collectors.toList());
            relations = furnitureGroupIds.stream()
                    .map(e -> new FurnitureActivityFurnitureGroupRelation().setActivityId(activityId)
                            .setFurnitureGroupId(e))
                    .collect(Collectors.toList());
        } else if (CollectionUtils.isNotEmpty(param.getFurnitureGroupIds())) {
            relations = param.getFurnitureGroupIds()
                    .stream()
                    .map(e -> new FurnitureActivityFurnitureGroupRelation().setActivityId(activityId)
                            .setFurnitureGroupId(e))
                    .collect(Collectors.toList());
        } else {
            throw new IllegalArgumentException("家具活动必须选择家具组");
        }
        furnitureActivityFurnitureGroupRelationRepository.batchSave(relations);
    }

    public BizResult<PageResult<FurnitureActivityView>> list(Integer id, String name, String operator, Integer status,
            int pageNum, int pageSize) {
        PageInfo<FurnitureActivityConfig> furnitureActivityConfigs = furnitureActivityConfigRepository.queryPage(id,
                name, operator, status, pageNum, pageSize);
        if (CollectionUtils.isEmpty(furnitureActivityConfigs.getList())) {
            return BizResult.success(PageResult.emptyPageResult());
        }
        Map<Integer, String> furnitureGroupId2NameMap = furnitureGroupRepository.queryAll()
                .stream()
                .collect(Collectors.toMap(FurnitureGroup::getId, FurnitureGroup::getName));
        Map<Integer, List<Integer>> activityId2FurnitureGroupIdsMap = furnitureActivityFurnitureGroupRelationRepository
                .queryByActivityIds(
                        furnitureActivityConfigs.getList().stream().map(FurnitureActivityConfig::getId)
                                .collect(Collectors.toList()));
        return BizResult.success(PageResult.from(furnitureActivityConfigs,
                furnitureActivityConfig -> FurnitureActivityView.valueOf(furnitureActivityConfig,
                        furnitureGroupId2NameMap, activityId2FurnitureGroupIdsMap)));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> online(int id) {
        FurnitureActivityConfig furnitureActivityConfig = furnitureActivityConfigRepository.selectById(id);
        if (Objects.isNull(furnitureActivityConfig)) {
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "家具活动不存在");
        }
        if (Objects.equals(CommonStatus.ONLINE.getCode(), furnitureActivityConfig.getStatus())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具活动已上线");
        }
        if (Objects.equals(FurnitureActivityType.getByCode(furnitureActivityConfig.getType()).getCode(),
                FurnitureActivityType.FURNITURE_ACTIVITY_NORMAL.getCode())) {
            List<FurnitureActivityConfig> onlineActivityNormal = furnitureActivityConfigRepository
                    .queryOnlineActivityNormal();
            if (CollectionUtils.isNotEmpty(onlineActivityNormal)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "常驻池已存在");
            }
        }
        furnitureActivityConfig.setStatus(CommonStatus.ONLINE.getCode());
        furnitureActivityConfigRepository.update(furnitureActivityConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_ACTIVITY_PUBLISH)
                .add("activityId", furnitureActivityConfig.getId())
                .add("activityName", furnitureActivityConfig.getName());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> offline(int id) {
        FurnitureActivityConfig furnitureActivityConfig = furnitureActivityConfigRepository.selectById(id);
        if (Objects.isNull(furnitureActivityConfig)) {
            return BizResult.result(ResponseCodeMsg.NOT_FOUND.getCode(), "家具活动不存在");
        }
        if (Objects.equals(CommonStatus.NOT_ONLINE.getCode(), furnitureActivityConfig.getStatus())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具活动已下线");
        }
        furnitureActivityConfig.setStatus(CommonStatus.NOT_ONLINE.getCode());
        furnitureActivityConfigRepository.update(furnitureActivityConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_ACTIVITY_OFFLINE)
                .add("activityId", furnitureActivityConfig.getId())
                .add("activityName", furnitureActivityConfig.getName());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<List<SimpleRewardView>> rewardList() {
        return activityRewardComponent.rewardList();
    }

    private boolean isLimitedFurnitureGroup(FurnitureActivityAddOrUpdateParam.RoleAnimation roleAnimation) {
        if (roleAnimation == null || roleAnimation.getFurnitureGroupId() == null) {
            return true;
        }
        List<FurnitureGroupRoleGroupRelation> furnitureGroupRoleGroupRelations = furnitureGroupRoleGroupRelationRepository
                .queryByFurnitureGroupId(
                        roleAnimation.getFurnitureGroupId());
        return CollectionUtils.isNotEmpty(furnitureGroupRoleGroupRelations)
                && Objects.equals(furnitureGroupRoleGroupRelations.size(), 1);
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> mockDraw(Integer activityId) {
        return BizResult.success();
    }

}