package com.kuaikan.role.game.admin.biz;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.Comparator;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.coupon.CouponProcessorSelector;
import com.kuaikan.role.game.admin.model.param.BaseCouponAddParam;
import com.kuaikan.role.game.admin.model.view.RoleTrialCouponView;
import com.kuaikan.role.game.admin.repository.RoleTrialCouponConfigRepository;
import com.kuaikan.role.game.api.bean.RoleTrialCouponConfig;
import com.kuaikan.role.game.api.enums.CouponConfigType;
import com.kuaikan.role.game.api.enums.CouponStatusType;

/**
 * <AUTHOR>
 * @date 2025/5/19 11:19
 */

@Service
@Slf4j
public class RoleTrialCouponBiz {

    @Resource
    private RoleTrialCouponConfigRepository roleTrialCouponConfigRepository;
    @Resource
    private CouponProcessorSelector couponProcessorSelector;

    public BizResult<PageView<RoleTrialCouponView>> list(Integer pageNum, Integer pageSize) {
        final int count = roleTrialCouponConfigRepository.count();
        if (count == 0) {
            return BizResult.success(PageView.empty());
        }
        final int offset = (pageNum - 1) * pageSize;
        final List<RoleTrialCouponConfig> couponConfigs = roleTrialCouponConfigRepository.queryByPage(offset, pageSize);
        if (couponConfigs == null) {
            return BizResult.success(PageView.empty());
        }
        List<RoleTrialCouponView> roleTrialCouponViews = couponConfigs.stream()
                .map(RoleTrialCouponView::valueOf)
                .sorted(Comparator.comparing(RoleTrialCouponView::getCreatedAt)
                        .reversed()
                        .thenComparing(RoleTrialCouponView::getValid, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(count, roleTrialCouponViews));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<String> add(MultipartFile file, BaseCouponAddParam param) {
        BizResult<String> result;
        try {
            result = couponProcessorSelector.getCouponProcessor(param).addCouponAndSendMsg(file, param);
        } catch (Exception e) {
            log.error("add roleTrialCouponCreate error, param={}", param, e);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "新增QQ人试用券失败");
        }
        String fileName = null;
        if (null != file && !file.isEmpty()) {
            fileName = file.getOriginalFilename();
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.ROLE_TRIAL_COUPON_ADD_OR_UPDATE)
                .add("fileName", fileName)
                .add("param", JsonUtils.writeValueAsString(param))
                .add("couponType", CouponConfigType.ROLE_TRIAL.getCode());
        OperateLogUtils.asyncRecord(operation);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> online(int id) {
        RoleTrialCouponConfig couponConfig = roleTrialCouponConfigRepository.selectById(id);
        if (Objects.isNull(couponConfig)) {
            log.error("role trial coupon config is not exist, id={}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "QQ人试用券配置信息不存在");
        }
        if (!couponConfig.getStatus().equals(CouponStatusType.INIT.getCode()) && !couponConfig.getStatus().equals(CouponStatusType.OFFLINE.getCode())) {
            log.error("role trial coupon config status not offline , id={}, status={}", id, couponConfig.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "QQ人试用券非已下架或初始状态");
        }
        couponConfig.setStatus(CouponStatusType.ONLINE.getCode()).setUpdatedAt(new Date());
        roleTrialCouponConfigRepository.update(couponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ONLINE_COUPON)
                .add("couponId", couponConfig.getId())
                .add("couponType", CouponConfigType.ROLE_TRIAL.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> offline(int id) {
        RoleTrialCouponConfig couponConfig = roleTrialCouponConfigRepository.selectById(id);
        if (Objects.isNull(couponConfig)) {
            log.error("role trial coupon config is not exist, id={}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "QQ人试用券配置信息不存在");
        }
        if (!couponConfig.getStatus().equals(CouponStatusType.ONLINE.getCode())) {
            log.error("role trial coupon config status not online , id={}, status={}", id, couponConfig.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "QQ人试用券非上架状态");
        }
        couponConfig.setStatus(CouponStatusType.OFFLINE.getCode()).setUpdatedAt(new Date());
        roleTrialCouponConfigRepository.update(couponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.OFFLINE_COUPON)
                .add("couponId", couponConfig.getId())
                .add("couponType", CouponConfigType.ROLE_TRIAL.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }
}
