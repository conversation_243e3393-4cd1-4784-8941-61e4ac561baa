package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.admin.component.QiniuComponent.FREE_BUCKET_NAME;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.utils.concurrent.ThreadPoolUtils;
import com.kuaikan.role.game.admin.battle.dao.CardBattleBondTicketConfigDao;
import com.kuaikan.role.game.admin.battle.dao.CardBattleCommonConfigDao;
import com.kuaikan.role.game.admin.battle.dao.CardIdModelConfigDao;
import com.kuaikan.role.game.admin.battle.dao.CardModelConfigDao;
import com.kuaikan.role.game.admin.battle.dao.CardPoolConfigDao;
import com.kuaikan.role.game.admin.battle.dao.CardRareConfigDao;
import com.kuaikan.role.game.admin.battle.dao.LevelGrowthConfigDao;
import com.kuaikan.role.game.admin.battle.dao.LevelSpendConfigDao;
import com.kuaikan.role.game.admin.battle.dao.MonsterLevelGrowthConfigDao;
import com.kuaikan.role.game.admin.battle.dao.MonsterModelConfigDao;
import com.kuaikan.role.game.admin.common.BattleUploadType;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.component.battle.ConfigExcelUploadFactory;
import com.kuaikan.role.game.admin.exception.FileRequirementNotMetException;
import com.kuaikan.role.game.admin.model.excel.CardBattleBondTicketExcelDto;
import com.kuaikan.role.game.admin.model.excel.CardIdModelConfigExcelDto;
import com.kuaikan.role.game.admin.model.excel.CardLevelGrowthConfigExcelDto;
import com.kuaikan.role.game.admin.model.excel.CardLevelSpendConfigExcelDto;
import com.kuaikan.role.game.admin.model.excel.CardModelConfigExcelDto;
import com.kuaikan.role.game.admin.model.excel.CardPoolConfigExcelDto;
import com.kuaikan.role.game.admin.model.excel.MonsterLevelGrowthConfigExcelDto;
import com.kuaikan.role.game.admin.model.excel.MonsterModelConfigExcelDto;
import com.kuaikan.role.game.admin.model.view.battle.BattleCardPoolView;
import com.kuaikan.role.game.admin.model.view.battle.BattleCardRareConfigView;
import com.kuaikan.role.game.admin.model.view.battle.BattleCommonConfigView;
import com.kuaikan.role.game.admin.utils.ExcelUtils;
import com.kuaikan.role.game.admin.utils.FileUtils;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleBondTicketConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardIdModelConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardLevelSpendConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardModelConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardPoolConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardRareConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCommonConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLevelGrowthConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleMonsterLevelGrowthConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleMonsterModelConfig;
import com.kuaikan.role.game.api.constant.CardBattleCacheKeyConfig;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleCardRarityEnum;
import com.kuaikan.role.game.api.service.cardbattle.CardBattleDataFixService;

/**
 * 卡牌战斗-数值设置biz
 *
 *<AUTHOR>
 *@date 2024/8/29
 */
@Slf4j
@Service
public class BattleDataConfigBiz {

    private static ExecutorService EXECUTOR = ThreadPoolUtils.newExecutor(4, 4, "card-battle-config-update-thread-pool");

    public static final String SLASH = "/";

    private static final String CARD_BATTLE_FILE_PATH = "/data/card/battle/";

    private static final String FILE_PREFIX = "card_battle/";

    @Resource
    CardBattleCommonConfigDao cardBattleCommonConfigDao;
    @Resource
    CardRareConfigDao cardRareConfigDao;
    @Resource
    CardModelConfigDao cardModelConfigDao;
    @Resource
    MonsterModelConfigDao monsterModelConfigDao;
    @Resource
    LevelGrowthConfigDao levelGrowthConfigDao;
    @Resource
    LevelSpendConfigDao levelSpendConfigDao;
    @Resource
    CardIdModelConfigDao cardIdModelConfigDao;
    @Resource
    CardPoolConfigDao cardPoolConfigDao;
    @Resource
    MonsterLevelGrowthConfigDao monsterLevelGrowthConfigDao;
    @Resource
    ConfigExcelUploadFactory configExcelUploadFactory;
    @Resource
    CardBattleDataFixService cardBattleDataFixService;
    @Resource
    private QiniuComponent qiniuComponent;
    @Resource
    CardBattleBondTicketConfigDao cardBattleBondTicketConfigDao;

    public BizResult<Void> updateCommonConfig(BattleCommonConfigView request) {
        CardBattleCommonConfig record = cardBattleCommonConfigDao.findFirst();
        if (record == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "common config not found");
        }
        BeanUtils.copyProperties(request, record);
        cardBattleCommonConfigDao.updateByPrimaryKey(record);
        cardBattleDataFixService.deleteCommonConfigCache();
        return BizResult.success();
    }

    public BizResult<BattleCommonConfigView> getCommonConfig() {
        CardBattleCommonConfig first = cardBattleCommonConfigDao.findFirst();
        if (first == null) {
            // init
            first = new CardBattleCommonConfig();
            first.setAttrRatio(new BigDecimal("1.3"));
            first.setCardGroupRatio(
                    Lists.newArrayList(new BigDecimal("1.0"), new BigDecimal("0.7"), new BigDecimal("0.6"), new BigDecimal("0.5"), new BigDecimal("0.4")));
            first.setPointLimit(240);
            first.setRoleRatio(new BigDecimal("0.15"));
            first.setCardGroupHpRatio(
                    Lists.newArrayList(new BigDecimal("1.0"), new BigDecimal("1.0"), new BigDecimal("1.0"), new BigDecimal("1.0"), new BigDecimal("1.0")));
            cardBattleCommonConfigDao.insert(first);
        }
        if (CollectionUtils.isEmpty(first.getCardGroupHpRatio())) {
            first.setCardGroupHpRatio(
                    Lists.newArrayList(new BigDecimal("1.0"), new BigDecimal("1.0"), new BigDecimal("1.0"), new BigDecimal("1.0"), new BigDecimal("1.0")));
        }
        BattleCommonConfigView result = new BattleCommonConfigView();
        BeanUtils.copyProperties(first, result);
        return BizResult.success(result);
    }

    public BizResult<List<BattleCardRareConfigView>> getRareConfig() {
        List<CardBattleCardRareConfig> allConfig = cardRareConfigDao.getAll();
        if (CollectionUtils.isEmpty(allConfig)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "rare config not found");
        }
        List<BattleCardRareConfigView> result = allConfig.stream().sorted(Comparator.comparing(CardBattleCardRareConfig::getRareCode)).map(config -> {
            BattleCardRareConfigView configDto = new BattleCardRareConfigView();
            BeanUtils.copyProperties(config, configDto);
            configDto.setRareName(CardBattleCardRarityEnum.parseByCode(config.getRareCode()));
            return configDto;
        }).collect(Collectors.toList());
        return BizResult.success(result);
    }

    public BizResult<Void> updateRareConfig(List<CardBattleCardRareConfig> params) {
        if (CollectionUtils.isEmpty(params)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "bad request");
        }
        params.stream().forEach(config -> cardRareConfigDao.updateByRareCode(config));
        CompletableFuture.runAsync(() -> {
            cardBattleDataFixService.deleteAllRareConfigCache();
            cardBattleDataFixService.uploadModelLevelAttr();

        }, EXECUTOR);
        return BizResult.success();
    }

    public void uploadExcelFile(MultipartFile file, Integer type) throws Exception {
        switch (type) {
            case BattleUploadType.CARD_MODEL:
                // 卡牌战斗模板
                List<CardModelConfigExcelDto> list0 = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.CARD_MODEL,
                        CardModelConfigExcelDto.class);
                if (CollectionUtils.isNotEmpty(list0)) {
                    List<CardBattleCardModelConfig> list = list0.stream().map(CardModelConfigExcelDto::toCardModelConfig).collect(Collectors.toList());
                    uploadCardModelConfigList(list);
                }
                break;
            case BattleUploadType.MONSTER_MODEL:
                // 战斗对象模板
                List<MonsterModelConfigExcelDto> list1 = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.MONSTER_MODEL,
                        MonsterModelConfigExcelDto.class);
                if (CollectionUtils.isNotEmpty(list1)) {
                    List<CardBattleMonsterModelConfig> list = list1.stream().map(MonsterModelConfigExcelDto::toMonsterModelConfig).collect(Collectors.toList());
                    uploadMonsterModelExcel(list);
                }
                break;
            case BattleUploadType.CARD_LEVEL_GROWTH:
                // 卡牌等级成长配置
                List<CardLevelGrowthConfigExcelDto> list2 = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.CARD_LEVEL_GROWTH,
                        CardLevelGrowthConfigExcelDto.class);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list2)) {
                    List<CardBattleLevelGrowthConfig> list = list2.stream()
                            .map(CardLevelGrowthConfigExcelDto::toLevelGrowthConfig)
                            .collect(Collectors.toList());
                    uploadLevelGrowthExcel(list);
                }
                break;
            case BattleUploadType.CARD_LEVEL_SPEND:
                // 卡牌等级消耗配置
                List<CardLevelSpendConfigExcelDto> list3 = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.CARD_LEVEL_SPEND,
                        CardLevelSpendConfigExcelDto.class);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list3)) {
                    List<CardBattleCardLevelSpendConfig> list = list3.stream()
                            .map(CardLevelSpendConfigExcelDto::toCardLevelSendConfig)
                            .collect(Collectors.toList());
                    levelSpendConfigDao.batchInsert(list);
                    CompletableFuture.runAsync(() -> cardBattleDataFixService.deleteCardLevelSpendCache(list), EXECUTOR);
                }
                break;
            case BattleUploadType.CARD_ID_MODEL:
                // 卡牌id模板关系配置
                List<CardIdModelConfigExcelDto> list4 = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.CARD_ID_MODEL,
                        CardIdModelConfigExcelDto.class);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list4)) {
                    List<CardBattleCardIdModelConfig> list = list4.stream().map(CardIdModelConfigExcelDto::toCardIdModelConfig).collect(Collectors.toList());
                    uploadCardIdModelExcel(list);
                }
                break;
            case BattleUploadType.CARD_POOL:
                // 卡牌池配置
                List<CardPoolConfigExcelDto> list5 = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.CARD_POOL,
                        CardPoolConfigExcelDto.class);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list5)) {
                    List<CardBattleCardPoolConfig> list = list5.stream().map(CardPoolConfigExcelDto::toCardPoolConfig).collect(Collectors.toList());
                    uploadCardPoolExcel(list);
                }
                break;
            case BattleUploadType.MONSTER_LEVEL_GROWTH:
                // 战斗对象等级成长配置
                List<MonsterLevelGrowthConfigExcelDto> list6 = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.MONSTER_LEVEL_GROWTH,
                        MonsterLevelGrowthConfigExcelDto.class);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list6)) {
                    List<CardBattleMonsterLevelGrowthConfig> list = list6.stream()
                            .map(MonsterLevelGrowthConfigExcelDto::toMonsterLevelGrowthConfig)
                            .collect(Collectors.toList());
                    uploadMonsterLevelGrowthExcel(list);
                }
                break;
            case BattleUploadType.BOND_TICKET_PRIZE:
                // 羁绊通用门票价格配置
                List<CardBattleBondTicketExcelDto> list7 = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.BOND_TICKET_PRIZE,
                        CardBattleBondTicketExcelDto.class);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list7)) {
                    List<Integer> purchaseCounts = list7.stream().map(CardBattleBondTicketExcelDto::getPurchaseCount).sorted().collect(Collectors.toList());
                    for (int i = 0; i < purchaseCounts.size(); i++) {
                        if (purchaseCounts.get(i) != i + 1) {
                            throw new Exception("purchaseCount必须从1开始且连续，发现不连续的purchaseCount: " + purchaseCounts.get(i));
                        }
                    }
                    List<CardBattleBondTicketConfig> list = list7.stream()
                            .map(CardBattleBondTicketExcelDto::toCardBattleBondTicketConfig)
                            .collect(Collectors.toList());
                    uploadBondTicketExcel(list);
                    CompletableFuture.runAsync(() -> {
                        try {
                            cardBattleDataFixService.deleteBondTicketConfigCache(purchaseCounts.size());
                        } catch (Exception e) {
                            log.error("uploadBondTicketConfigCache error", e);
                        }
                    }, EXECUTOR);
                }
                break;

            default:
                log.debug("uploadExcelFile error type:{}", type);
        }
    }

    public void downloadExcel(HttpServletResponse response, Integer type) {
        switch (type) {
            case BattleUploadType.CARD_MODEL:
                // 查询卡牌模板数据
                List<CardBattleCardModelConfig> response0 = cardModelConfigDao.selectAll();
                if (CollectionUtils.isNotEmpty(response0)) {
                    List<CardModelConfigExcelDto> list = response0.stream().map(CardModelConfigExcelDto::fromCardModelConfig).collect(Collectors.toList());
                    ExcelUtils.writeExcel("CardModel", list, CardModelConfigExcelDto.class, response);
                }
                break;
            case BattleUploadType.MONSTER_MODEL:
                List<CardBattleMonsterModelConfig> response1 = monsterModelConfigDao.getAllConfigs();
                if (CollectionUtils.isNotEmpty(response1)) {
                    List<MonsterModelConfigExcelDto> list = response1.stream()
                            .map(MonsterModelConfigExcelDto::toMonsterModelExcelDto)
                            .collect(Collectors.toList());
                    ExcelUtils.writeExcel("MonsterModel", list, MonsterModelConfigExcelDto.class, response);
                }
                break;
            case BattleUploadType.CARD_LEVEL_GROWTH:
                List<CardBattleLevelGrowthConfig> response2 = levelGrowthConfigDao.selectAll();
                if (CollectionUtils.isNotEmpty(response2)) {
                    List<CardLevelGrowthConfigExcelDto> list = response2.stream()
                            .map(CardLevelGrowthConfigExcelDto::fromLevelGrowthConfig)
                            .collect(Collectors.toList());
                    ExcelUtils.writeExcel("CardLevelGrowth", list, CardLevelGrowthConfigExcelDto.class, response);
                }
                break;
            case BattleUploadType.CARD_LEVEL_SPEND:
                List<CardBattleCardLevelSpendConfig> response3 = levelSpendConfigDao.selectAll();
                if (CollectionUtils.isNotEmpty(response3)) {
                    List<CardLevelSpendConfigExcelDto> list = response3.stream()
                            .map(CardLevelSpendConfigExcelDto::fromCardLevelSendConfig)
                            .collect(Collectors.toList());
                    ExcelUtils.writeExcel("CardLevelSpend", list, CardLevelSpendConfigExcelDto.class, response);
                }
                break;
            case BattleUploadType.CARD_ID_MODEL:
                List<CardBattleCardIdModelConfig> response4 = cardIdModelConfigDao.selectAll();
                if (CollectionUtils.isNotEmpty(response4)) {
                    List<CardIdModelConfigExcelDto> list = response4.stream()
                            .map(CardIdModelConfigExcelDto::fromCardIdModelConfig)
                            .collect(Collectors.toList());
                    ExcelUtils.writeExcel("CardIdModelMap", list, CardIdModelConfigExcelDto.class, response);
                }
                break;
            case BattleUploadType.CARD_POOL:
                List<CardBattleCardPoolConfig> response5 = cardPoolConfigDao.selectAll();
                if (CollectionUtils.isNotEmpty(response5)) {
                    List<CardPoolConfigExcelDto> list = response5.stream().map(CardPoolConfigExcelDto::fromCardPoolConfig).collect(Collectors.toList());
                    ExcelUtils.writeExcel("CardPool", list, CardPoolConfigExcelDto.class, response);
                }
                break;
            case BattleUploadType.MONSTER_LEVEL_GROWTH:
                List<CardBattleMonsterLevelGrowthConfig> response6 = monsterLevelGrowthConfigDao.selectAll();
                if (CollectionUtils.isNotEmpty(response6)) {
                    List<MonsterLevelGrowthConfigExcelDto> list = response6.stream()
                            .map(MonsterLevelGrowthConfigExcelDto::fromMonsterLevelGrowthConfig)
                            .collect(Collectors.toList());
                    ExcelUtils.writeExcel("MonsterLevelGrowth", list, MonsterLevelGrowthConfigExcelDto.class, response);
                }
                break;
            case BattleUploadType.BOND_TICKET_PRIZE:
                List<CardBattleBondTicketConfig> response7 = selectAllBondTicketConfig();
                if (CollectionUtils.isNotEmpty(response7)) {
                    List<CardBattleBondTicketExcelDto> list = response7.stream()
                            .map(CardBattleBondTicketExcelDto::fromCardBattleBondTicketConfig)
                            .collect(Collectors.toList());
                    ExcelUtils.writeExcel("CardBattleBondTicket", list, CardBattleBondTicketExcelDto.class, response);
                }
                break;

            default:
                log.debug("downloadExcel error type:{}", type);
        }
    }

    public BizResult<List<CardBattleMonsterModelConfig>> getMonsterModelConfigs() {
        List<CardBattleMonsterModelConfig> allConfig = monsterModelConfigDao.getAllConfigs();
        if (CollectionUtils.isEmpty(allConfig)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "monster model config not found");
        }
        return BizResult.success(allConfig);
    }

    public BizResult<List<CardBattleCardModelConfig>> getCardModelConfigs() {
        List<CardBattleCardModelConfig> allConfig = cardModelConfigDao.selectAll();
        if (org.springframework.util.CollectionUtils.isEmpty(allConfig)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "card model config not found");
        }
        return BizResult.success(allConfig);
    }

    private void uploadCardModelConfigList(List<CardBattleCardModelConfig> params) {
        Set<Integer> modelIds = params.stream().map(CardBattleCardModelConfig::getModelId).collect(Collectors.toSet());
        List<CardBattleCardModelConfig> cardBattleCardModelConfigs = cardModelConfigDao.selectAll();
        List<Long> needUpdateIds = cardBattleCardModelConfigs.stream()
                .filter(config -> !modelIds.contains(config.getModelId()))
                .map(CardBattleCardModelConfig::getId)
                .collect(Collectors.toList());
        cardModelConfigDao.batchInsert(params);
        if (CollectionUtils.isNotEmpty(needUpdateIds)) {
            cardModelConfigDao.updateInvalidStatusByIds(needUpdateIds);
        }
        CompletableFuture.runAsync(() -> {
            cardBattleDataFixService.deleteCardModelConfigCache(modelIds);
            cardBattleDataFixService.uploadModelLevelAttr();
        }, EXECUTOR);
    }

    public BizResult<Void> uploadCardIdModelExcel(List<CardBattleCardIdModelConfig> params) {
        if (CollectionUtils.isEmpty(params)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST);
        }

        Map<String, CardBattleCardIdModelConfig> paramMap = params.stream()
                .collect(Collectors.toMap(CardBattleCardIdModelConfig::getCardId, Function.identity(), (k1, k2) -> k1));

        List<CardBattleCardIdModelConfig> allConfig = cardIdModelConfigDao.selectAll();
        Set<String> cardIds = allConfig.stream().map(CardBattleCardIdModelConfig::getCardId).collect(Collectors.toSet());
        // 需要更新为失效状态的id
        List<Long> invalidIds = new ArrayList<>();
        // 需要更新为失效状态的cardId
        List<String> invalidCardIds = new ArrayList<>();
        // 新增的cardId,modelId
        List<CardBattleCardIdModelConfig> newAddCardIds = new ArrayList<>();
        // 需要更新的cardId,modelId
        List<CardBattleCardIdModelConfig> changeCardList = new ArrayList<>();

        for (CardBattleCardIdModelConfig config : allConfig) {
            if (paramMap.containsKey(config.getCardId())) {
                CardBattleCardIdModelConfig newConfig = paramMap.get(config.getCardId());
                if (newConfig.getModelId() != config.getModelId() || newConfig.getAttr() != config.getAttr()) {
                    changeCardList.add(newConfig);
                }
            } else {
                invalidIds.add(config.getId());
                invalidCardIds.add(config.getCardId());
            }
        }
        // 先填充专题id
        params = cardBattleDataFixService.fillCardIdModelTopicId(params);

        // 再获取要更新的配置
        for (CardBattleCardIdModelConfig config : params) {
            if (!cardIds.contains(config.getCardId())) {
                newAddCardIds.add(config);
            }
            cardIds.add(config.getCardId());
        }

        if (CollectionUtils.isNotEmpty(invalidIds)) {
            cardIdModelConfigDao.updateInvalidStatusByIds(invalidIds);
        }
        cardIdModelConfigDao.batchInsert(params);
        CompletableFuture.runAsync(() -> {
            try {
                cardBattleDataFixService.deleteCardIdConfigCache(cardIds);
                cardBattleDataFixService.updateUserBattleCards(invalidCardIds, newAddCardIds, changeCardList);
            } catch (Exception e) {
                log.error("updateUserBattleCards error", e);
            }
            deleteCache();
        }, EXECUTOR);
        return BizResult.success();
    }

    private void uploadMonsterModelExcel(List<CardBattleMonsterModelConfig> params) {
        List<CardBattleMonsterModelConfig> allConfig = monsterModelConfigDao.getAllConfigs();
        Set<Integer> modelIds = params.stream().map(CardBattleMonsterModelConfig::getModelId).collect(Collectors.toSet());
        List<Long> needUpdateIds = allConfig.stream()
                .filter(config -> !modelIds.contains(config.getModelId()))
                .map(CardBattleMonsterModelConfig::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needUpdateIds)) {
            monsterModelConfigDao.updateInvalidStatusByIds(needUpdateIds);
        }
        monsterModelConfigDao.batchInsert(params);
    }

    private void uploadLevelGrowthExcel(List<CardBattleLevelGrowthConfig> params) {
        levelGrowthConfigDao.batchInsert(params);
        Set<Integer> levels = params.stream().map(CardBattleLevelGrowthConfig::getLevel).collect(Collectors.toSet());
        CompletableFuture.runAsync(() -> {
            cardBattleDataFixService.deleteCardLevelGrowthCache(levels);
            cardBattleDataFixService.uploadModelLevelAttr();
        }, EXECUTOR);
    }

    public void uploadCardPoolExcel(List<CardBattleCardPoolConfig> params) {
        List<CardBattleCardPoolConfig> cardBattleCardPoolConfigs = cardPoolConfigDao.selectAll();
        Set<String> keys = params.stream().map(this::getCardPoolKey).collect(Collectors.toSet());
        List<Long> needUpdateIds = cardBattleCardPoolConfigs.stream()
                .filter(config -> !keys.contains(getCardPoolKey(config)))
                .map(CardBattleCardPoolConfig::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needUpdateIds)) {
            cardPoolConfigDao.updateInvalidStatusByIds(needUpdateIds);
        }
        cardPoolConfigDao.batchInsert(params);
        CompletableFuture.runAsync(() -> {
            List<Integer> poolIds = params.stream().map(item -> item.getPoolId()).distinct().collect(Collectors.toList());
            cardBattleDataFixService.deleteCardPoolCache(poolIds);
        }, EXECUTOR);
    }

    private String getCardPoolKey(CardBattleCardPoolConfig config) {
        return String.format("%s_%s", config.getPoolId(), config.getCardId());
    }

    public void uploadMonsterLevelGrowthExcel(List<CardBattleMonsterLevelGrowthConfig> params) {
        monsterLevelGrowthConfigDao.batchInsert(params);
        CompletableFuture.runAsync(() -> {
            cardBattleDataFixService.deleteMonsterLevelGrowthCache(params);
        });
    }

    public BizResult<List<BattleCardPoolView>> getCardPoolIdList() {
        List<CardBattleCardPoolConfig> allPoolIds = cardPoolConfigDao.selectAllCardPoolIdList();
        if (CollectionUtils.isEmpty(allPoolIds)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "card pool id not found");
        }
        return BizResult.success(allPoolIds.stream().map(BattleCardPoolView::from).collect(Collectors.toList()));
    }

    public BizResult<List<String>> uploadImagesZip(MultipartFile file) {
        String fileOutPath = CARD_BATTLE_FILE_PATH + System.currentTimeMillis() + SLASH + file.getOriginalFilename();
        try {
            if (file == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST);
            }
            boolean isSuccess = FileUtils.saveMultipartFile(file, fileOutPath);
            if (!isSuccess) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
            }
            List<File> unzipFileList = FileUtils.unzip(fileOutPath);
            if (CollectionUtils.isEmpty(unzipFileList)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件解压失败");
            }
            List<String> strList = checkAndUploadImageList(unzipFileList);
            return BizResult.success(strList);
            //            battleDataConfigBiz.uploadExcelFile(file, type);
        } catch (FileRequirementNotMetException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), e.getMessage());
        } catch (Exception e) {
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), e.getMessage());
        } finally {
            FileUtils.deleteDirectory(fileOutPath);
        }
    }

    private List<String> checkAndUploadImageList(List<File> files) throws FileRequirementNotMetException {
        List<File> pngFiles = files.stream().filter(file -> ".png".equals(file.getName().substring(file.getName().indexOf(".")))).collect(Collectors.toList());
        if (CollectionUtils.size(pngFiles) < 2) {
            throw new FileRequirementNotMetException("至少包含两个图片文件");
        }
        List<String> keyList = Lists.newArrayList();
        for (File file : pngFiles) {
            String name = file.getName();
            String key = FILE_PREFIX + System.currentTimeMillis() + SLASH + name;
            key = qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, file, key);
            if (StringUtils.isEmpty(key)) {
                throw new FileRequirementNotMetException("图片文件上传失败");
            }
            keyList.add(key);
        }
        return keyList;
    }

    private void deleteCache() {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CardBattleCacheKeyConfig.CARD_BATTLE_LIST.getReadWriteVip());
        String cacheKey = CardBattleCacheKeyConfig.CARD_BATTLE_LIST.getKeyPattern();
        redisClient.del(cacheKey);
    }

    private void uploadBondTicketExcel(List<CardBattleBondTicketConfig> list) {
        cardBattleBondTicketConfigDao.batchInsert(list);
    }

    private List<CardBattleBondTicketConfig> selectAllBondTicketConfig() {
        List<CardBattleBondTicketConfig> cardBattleBondTicketConfigs = cardBattleBondTicketConfigDao.selectAll();
        if (CollectionUtils.isEmpty(cardBattleBondTicketConfigs)) {
            log.warn("No CardBattleBondTicketConfig data found in database.");
            return new ArrayList<>();
        }
        return cardBattleBondTicketConfigs;
    }
}
