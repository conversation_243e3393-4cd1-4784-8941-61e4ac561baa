package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.admin.component.QiniuComponent.FREE_BUCKET_NAME;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.badlogic.gdx.backends.headless.HeadlessApplication;
import com.badlogic.gdx.backends.headless.HeadlessApplicationConfiguration;
import com.fasterxml.jackson.core.type.TypeReference;
import com.badlogic.gdx.backends.headless.HeadlessApplication;
import com.badlogic.gdx.backends.headless.HeadlessApplicationConfiguration;
import com.badlogic.gdx.backends.headless.HeadlessApplication;
import com.badlogic.gdx.backends.headless.HeadlessApplicationConfiguration;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;

import com.kuaikan.cdn.core.CdnHandler;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.idgenerator.sdk.BufferedIdGenerator;
import com.kuaikan.role.game.admin.common.MaterialConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.component.DingDingComponent;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.config.ApolloConfig;
import com.kuaikan.role.game.admin.exception.FileRequirementNotMetException;
import com.kuaikan.role.game.admin.model.bean.ImageResponse;
import com.kuaikan.role.game.admin.model.bo.MaterialExportBO;
import com.kuaikan.role.game.admin.model.view.MaterialView;
import com.kuaikan.role.game.admin.model.view.SpineMaterialView;
import com.kuaikan.role.game.admin.model.view.ZipView;
import com.kuaikan.role.game.admin.model.view.story.UploadView;
import com.kuaikan.role.game.admin.repository.MaterialRepository;
import com.kuaikan.role.game.admin.utils.CostumeReplaceListener;
import com.kuaikan.role.game.admin.utils.FileUtils;
import com.kuaikan.role.game.api.bean.Material;
import com.kuaikan.role.game.api.bean.MaterialDraft;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.BucketType;
import com.kuaikan.role.game.api.enums.CdnPayType;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.api.enums.MaterialBizType;
import com.kuaikan.role.game.api.enums.StoryType;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Service
@Slf4j
public class MaterialBiz {

    private static final String COSTUME_MATERIAL_FILE_PATH = "/data/material/costume/";
    private static final String STORY_MATERIAL_FILE_PATH = "/data/material/story/";
    private static final String ROLE_MATERIAL_FILE_PATH = "/data/material/role/";
    private static final String SCENE_MATERIAL_FILE_PATH = "/data/material/scene/";
    private static final String FRAME_ANIMATION_MATERIAL_FILE_PATH = "/data/material/frameAnimation/";
    public static final String POINT = ".";
    public static final String SLASH = "/";
    public static final String DS_STORE = ".DS_Store";
    public static final String SPINE = "_spine";
    public static final String DOWNLOAD = "download/";
    public static final String RESOURCES = "resources";
    public static final String DATA_MATERIAL_EXPORT = "/data/material/export/%d/";
    public static final String RESOURCES_SPINE_STORY = "resources/spine/story/%d/";
    public static final String RESOURCES_SPINE_NORMAL = "resources/spine/%d/normal/%d/";
    public static final String RESOURCES_ROLE_UNHEALTHY_STATE = "resources/spine/%d/unhealthy/";
    public static final String ANIMATION_FILE_PREFIX = "role/game/frame/";
    public static final String RESULT_ZIP = "result.zip";
    public static final int FILE_TYPE_NUM = 3;
    public static final int MIN_FILE_NUM = 2;
    public static final String PNG = ".png";
    public static final Set<String> ATLAS = ImmutableSet.of(".atlas", ".atlas.txt");
    public static final String JSON = ".json";
    public static final String EXCEL = ".xlsx";
    public static final String BACKGROUND = "background.png";
    public static final String BGM = "bgm.mp3";
    public static final String DIALOG = "dialogue.txt";
    public static final String STORY_FILE_KEY = "role/game/story/%d/material.zip";
    public static final String STORY_DIALOGUE_KEY = "role/game/story/%d/dialogue.txt";
    public static final String STORY_BACKGROUND_KEY = "role/game/story/%d/background.png";
    public static final String STORY_BGM_KEY = "role/game/story/%d/bgm.mp3";
    public static final String ANIMATIONS = "animations";
    public static final String ZIP_KEY = "role/game/zip/%d/%s.zip";
    public static final String ATLAS1 = ".atlas";
    public static final String ZIP_FILE = ".zip";

    public static final String AVG_COMMON_FILE_KEY = "/avg/file/%s/";

    /**
     * 插槽图片替换
     */
    private static final String REPLACE_COSTUME_PART_PATH = "/data/material/replaceCostumePart/";

    @Resource
    private QiniuComponent qiniuComponent;

    @Resource
    private MaterialRepository materialRepository;

    @Resource
    private DingDingComponent dingDingComponent;

    @Resource
    private ApolloConfig apolloConfig;

    //    @Resource
    //    private CostumeReplaceListener costumeReplaceListener;

    public BizResult<ZipView> uploadZip(MultipartFile file) {
        String key = String.format(ZIP_KEY, BufferedIdGenerator.getId(), file.getName());
        try {
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, file.getBytes(), key);
        } catch (IOException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
        }
        return BizResult.success(ZipView.valueOf(key));
    }

    public BizResult<SpineMaterialView> uploadCostumeMaterial(MultipartFile file) {
        String basicPath = COSTUME_MATERIAL_FILE_PATH + System.currentTimeMillis() + SLASH;
        return getSpineMaterialViewBizResult(file, basicPath);
    }

    public BizResult<ImageResponse> uploadFrameAnimation(MultipartFile file, String ops, String bucket) {
        String fileName = file.getOriginalFilename();
        if (fileName.endsWith(".zip")) {
            return processZip(file, ops, bucket);
        } else {
            try {
                return processSimpleImage(ops, bucket, file.getInputStream(), file.getOriginalFilename());
            } catch (IOException e) {
                log.warn("fail to get input stream", e);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "读取multipart文件失败");
            }
        }
    }

    private BizResult<ImageResponse> processSimpleImage(String ops, String bucket, InputStream inputStream, String fileName) {
        String url = StringUtils.join(apolloConfig.getImageServiceHost(), "media_service/image_convert");
        HttpPost postMethod = new HttpPost(url);
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addBinaryBody("file", inputStream, ContentType.MULTIPART_FORM_DATA, fileName);
        builder.addTextBody("bucket", bucket, ContentType.MULTIPART_FORM_DATA);
        builder.addTextBody("ops", ops, ContentType.MULTIPART_FORM_DATA);
        postMethod.setEntity(builder.build());
        String responseString;
        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            try (CloseableHttpResponse response = httpClient.execute(postMethod)) {
                if (response.getStatusLine().getStatusCode() != 200) {
                    log.warn("file upload failed, response:{}", response);
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
                }
                responseString = EntityUtils.toString(response.getEntity());
            } catch (IOException e) {
                log.warn("fail to parse upload result", e);
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传结果解析失败");

            }
        } catch (IOException e) {
            log.warn("fail to upload file", e);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "连接失败");
        }
        BizResult<ImageResponse> result = JsonUtils.readSnakeValue(responseString, new TypeReference<BizResult<ImageResponse>>() {});
        if (result.isSuccess()) {
            return BizResult.success(result.getData());
        } else {
            log.error("file upload failed, response:{}", responseString);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传返回失败");
        }
    }

    @NotNull
    private BizResult<ImageResponse> processZip(MultipartFile file, String ops, String bucket) {
        String basicPath = FRAME_ANIMATION_MATERIAL_FILE_PATH + System.currentTimeMillis() + SLASH;
        try {
            String fileOutPath = basicPath + file.getOriginalFilename();
            String zipFileKey = saveAnimationZipFile(file);
            boolean isSuccess = FileUtils.saveMultipartFile(file, fileOutPath);
            if (!isSuccess) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件保存失败");
            }
            List<File> unzipFileList = FileUtils.unzip(fileOutPath);
            if (CollectionUtils.isEmpty(unzipFileList)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件解压失败");
            }
            Optional<File> first = unzipFileList.stream().filter(item -> item.getName().endsWith(".png")).findFirst();
            if (!first.isPresent()) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "获取第一帧图片失败");
            }
            FileInputStream firstFileStream = new FileInputStream(first.get());
            BizResult<ImageResponse> imageResponseBizResult = processSimpleImage(ops, bucket, firstFileStream, first.get().getName());
            if (!imageResponseBizResult.isSuccess()) {
                return imageResponseBizResult;
            }
            ImageResponse firstImageInfo = imageResponseBizResult.getData();
            List<String> frameKeyList = checkAndUploadFrameAnimation(unzipFileList);
            ImageResponse imageInfo = new ImageResponse();
            imageInfo.setFileName(file.getOriginalFilename());
            imageInfo.setFileKey(zipFileKey);
            imageInfo.setFrameKeyList(frameKeyList);
            imageInfo.setImage(frameKeyList.get(0));
            imageInfo.setDisplayImage(CdnUtil.getDefaultDomainWithBackSlash() + frameKeyList.get(0));
            imageInfo.setImageType(ImageInfo.TYPE_SEQUENCE);
            imageInfo.setWidth(firstImageInfo.getWidth());
            imageInfo.setHeight(firstImageInfo.getHeight());
            return BizResult.success(imageInfo);
        } catch (FileRequirementNotMetException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), e.getMessage());
        } catch (IOException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
        } finally {
            FileUtils.deleteDirectory(basicPath);
        }
    }

    public String saveAnimationZipFile(MultipartFile file) throws IOException {
        String fileKey = ANIMATION_FILE_PREFIX + System.currentTimeMillis() + SLASH + file.getOriginalFilename();
        qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, file.getBytes(), fileKey);
        return fileKey;
    }

    public BizResult<UploadView> uploadStoryMaterial(MultipartFile file, StoryType type) {
        String fileOutPath = STORY_MATERIAL_FILE_PATH + System.currentTimeMillis() + SLASH + file.getOriginalFilename();
        try {
            boolean isSuccess = FileUtils.saveMultipartFile(file, fileOutPath);
            if (!isSuccess) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
            }
            List<File> unzipFileList = FileUtils.unzip(fileOutPath);
            if (CollectionUtils.isEmpty(unzipFileList)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件解压失败");
            }

            if (StoryType.ACTION == type || StoryType.DAILY == type) {
                return BizResult.success(uploadActionStoryFiles(unzipFileList, file.getOriginalFilename(), file));
            } else {
                return BizResult.success(uploadLetterStoryFiles(unzipFileList));
            }
        } catch (FileRequirementNotMetException | IOException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), e.getMessage());
        } finally {
            FileUtils.deleteDirectory(fileOutPath);
        }
    }

    public BizResult<SpineMaterialView> uploadRoleMaterial(MultipartFile file) {
        String basicPath = ROLE_MATERIAL_FILE_PATH + System.currentTimeMillis() + SLASH;
        try {
            String fileOutPath = basicPath + file.getOriginalFilename();
            boolean isSuccess = FileUtils.saveMultipartFile(file, fileOutPath);
            if (!isSuccess) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
            }
            List<File> unzipFileList = FileUtils.unzip(fileOutPath);
            if (CollectionUtils.isEmpty(unzipFileList)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件解压失败");
            }
            SpineMaterialView materialViewV2 = getMaterialViewV2(unzipFileList, file);
            materialViewV2.setName(file.getOriginalFilename());
            return BizResult.success(materialViewV2);
        } catch (FileRequirementNotMetException | IOException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), e.getMessage());
        } finally {
            FileUtils.deleteDirectory(basicPath);
        }
    }

    @NotNull
    private SpineMaterialView getMaterialViewV2(List<File> unzipFileList, MultipartFile originZipFile) throws FileRequirementNotMetException, IOException {
        List<File> needFiles = checkAndFilterSpineFile(unzipFileList);
        SpineMaterialView spineMaterialView = new SpineMaterialView();
        String pre = "role/game/material/";
        List<String> pngFileKeys = Lists.newArrayList();
        for (File needFile : needFiles) {
            String name = needFile.getName();
            String key = pre + System.currentTimeMillis() + SLASH + name;
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, needFile, key);
            if (name.contains(PNG)) {
                //  后面从列表里面取，保留原字段做兼容
                spineMaterialView.setPngFileKey(key);
                pngFileKeys.add(key);
            } else if (name.contains(ATLAS1)) {
                spineMaterialView.setAtlasFileKey(key);
            } else if (name.contains(JSON)) {
                spineMaterialView.setJsonFileKey(key);
            }
        }
        spineMaterialView.setPngFileKeys(pngFileKeys);
        String zipFileKey = pre + System.currentTimeMillis() + SLASH + originZipFile.getOriginalFilename();
        qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, originZipFile.getBytes(), zipFileKey);
        spineMaterialView.setOriginZipFileKey(zipFileKey);
        spineMaterialView.setPngFileKeys(pngFileKeys);
        return spineMaterialView;
    }

    @NotNull
    private UploadView uploadActionStoryFiles(List<File> unzipFileList, String fileName, MultipartFile originZipFile)
            throws FileRequirementNotMetException, IOException {
        UploadView uploadView = new UploadView();
        SpineMaterialView spineMaterialView = getMaterialViewV2(unzipFileList, originZipFile);
        spineMaterialView.setName(fileName);
        uploadView.setSpine(spineMaterialView);
        final Optional<File> backgroundOptional = unzipFileList.stream().filter(file -> file.getName().equals(BACKGROUND)).findAny();
        if (backgroundOptional.isPresent()) {
            final File backgroundFile = backgroundOptional.get();
            final String backgroundKey = String.format(STORY_BACKGROUND_KEY, System.currentTimeMillis());
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, backgroundFile.getAbsolutePath(), backgroundKey);
            uploadView.setBackgroundFileKey(backgroundKey);
        }
        final Optional<File> bgmOptional = unzipFileList.stream().filter(file -> file.getName().equals(BGM)).findAny();
        if (bgmOptional.isPresent()) {
            final File bgmFile = bgmOptional.get();
            final String bgmKey = String.format(STORY_BGM_KEY, System.currentTimeMillis());
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, bgmFile.getAbsolutePath(), bgmKey);
            uploadView.setBgmFileKey(bgmKey);
        }
        final Optional<File> dialogOptional = unzipFileList.stream().filter(file -> file.getName().equals(DIALOG)).findAny();
        if (dialogOptional.isPresent()) {
            final File dialogFile = dialogOptional.get();
            final String dialogKey = String.format(STORY_DIALOGUE_KEY, System.currentTimeMillis());
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, dialogFile.getAbsolutePath(), dialogKey);
            uploadView.setDialogFileKey(dialogKey);
        }
        return uploadView;
    }

    private UploadView uploadLetterStoryFiles(List<File> unzipFileList) throws FileRequirementNotMetException {
        UploadView uploadView = new UploadView();
        final List<File> excelFiles = unzipFileList.stream()
                .filter(file -> file.getAbsolutePath().endsWith(EXCEL) && !file.isHidden())
                .collect(Collectors.toList());
        if (CollectionUtils.size(excelFiles) != 1) {
            log.warn("uploadLetterStoryFiles excel not found, unzipFileList:{}", unzipFileList);
            throw new FileRequirementNotMetException("excel文件数量不正确");
        }
        List<File> needFiles = new ArrayList<>();
        needFiles.add(excelFiles.get(0));
        final List<File> avatarImages = unzipFileList.stream()
                .filter(file -> file.getAbsolutePath().contains(MaterialConstants.LETTER_AVATAR_IMAGE_DIR))
                .filter(file -> !DS_STORE.equals(file.getName()))
                .collect(Collectors.toList());
        log.debug("uploadLetterStoryFiles avatarImages:{}, unzipFileList:{}", avatarImages, unzipFileList);
        if (CollectionUtils.size(avatarImages) < 1) {
            throw new FileRequirementNotMetException("头像图片数量不正确");
        }
        needFiles.addAll(avatarImages);
        final List<File> dialogImages = unzipFileList.stream()
                .filter(file -> file.getAbsolutePath().contains(MaterialConstants.LETTER_CONTENT_IMAGE_DIR))
                .filter(file -> !DS_STORE.equals(file.getName()))
                .collect(Collectors.toList());
        needFiles.addAll(dialogImages);
        final String resultPath = STORY_MATERIAL_FILE_PATH + RESULT_ZIP;
        boolean zipFileSuccess = FileUtils.zipFiles(needFiles, resultPath, MaterialConstants.LETTER_IMAGE_DIR_LIST);
        if (!zipFileSuccess) {
            throw new RuntimeException("文件压缩失败");
        }
        String fileKey = String.format(STORY_FILE_KEY, System.currentTimeMillis());
        qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, resultPath, fileKey);
        String fileMd5 = FileUtils.calculateMD5(resultPath);
        uploadView.setZipMaterialView(new MaterialView().setKey(fileKey).setMd5(fileMd5).setName(RESULT_ZIP));
        return uploadView;
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> publishMaterial() {
        List<MaterialDraft> materialDrafts = materialRepository.queryAllMaterialDrafts();
        Set<Integer> draftMaterialIds = materialDrafts.stream().map(MaterialDraft::getMaterialId).collect(Collectors.toSet());
        Map<Integer, MaterialDraft> materialDraftsMap = materialDrafts.stream().collect(Collectors.toMap(MaterialDraft::getMaterialId, Function.identity()));
        Collection<Material> draftMaterialList = materialRepository.queryByMaterialIds(draftMaterialIds).values();
        for (Material material : draftMaterialList) {
            Integer materialId = material.getId();
            MaterialDraft materialDraft = materialDraftsMap.get(materialId);
            if (materialDraft == null) {
                log.error("materialId:{} not found in materialDrafts", materialId);
                continue;
            }
            material.setKey(materialDraft.getKey());
            material.setMd5(materialDraft.getMd5());
            material.setName(materialDraft.getName());
        }
        List<Material> publishMaterials = Lists.newArrayList(draftMaterialList);
        List<Material> materials = materialRepository.queryNotOnlineMaterial();
        for (Material material : materials) {
            material.setStatus(CommonStatus.ONLINE.getCode());
        }
        publishMaterials.addAll(materials);
        log.info("publishMaterials:{}", publishMaterials);
        materialRepository.batchUpdateByPrimaryKeySelective(publishMaterials);
        materialRepository.deleteAllMaterialDrafts();
        dingDingComponent.sendMaterialPublishFinishedMessage();
        return BizResult.success();
    }

    public BizResult<Void> exportAll(HttpServletResponse response) {
        List<MaterialDraft> materialDrafts = materialRepository.queryAllMaterialDrafts();
        Set<Integer> draftMaterialIds = materialDrafts.stream().map(MaterialDraft::getMaterialId).collect(Collectors.toSet());
        Map<Integer, Material> draftMaterialMap = materialRepository.queryByMaterialIds(draftMaterialIds);
        List<MaterialExportBO> draftsExportBOS = materialDrafts.stream().filter(Objects::nonNull).map(item -> {
            Integer materialId = item.getMaterialId();
            Material material = draftMaterialMap.get(materialId);
            if (material == null) {
                log.error("materialId:{} already exists in material", materialId);
                return null;
            }
            return getMaterialExportBO(item.getKey(), material.getBizInfo());
        }).filter(Objects::nonNull).collect(Collectors.toList());
        List<Material> materials = materialRepository.queryNotOnlineMaterial();
        List<MaterialExportBO> exportBOS = materials.stream()
                .map(item -> getMaterialExportBO(item.getKey(), item.getBizInfo()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        exportBOS.addAll(draftsExportBOS);
        if (CollectionUtils.isEmpty(exportBOS)) {
            return BizResult.result(RoleGameResponse.MATERIAL_ALL_PUBLISH);
        }
        String domain = CdnUtil.getDefaultDomainWithBackSlash();
        String basicDir = String.format(DATA_MATERIAL_EXPORT, System.currentTimeMillis());
        for (MaterialExportBO exportBO : exportBOS) {
            for (String outPath : exportBO.getOutPaths()) {
                String downloadPath = basicDir + DOWNLOAD + outPath + RESULT_ZIP;
                FileUtils.downloadFile(domain + exportBO.getFileKey(), downloadPath);
                FileUtils.unzipToDirectory(downloadPath, basicDir + outPath);
            }
        }
        String resultFile = basicDir + RESULT_ZIP;
        FileUtils.zipDirectory(basicDir + RESOURCES, resultFile);
        FileUtils.downloadFile(response, resultFile);
        FileUtils.deleteDirectory(basicDir);
        dingDingComponent.sendMaterialStartPublishMessage();
        return BizResult.success();
    }

    public BizResult<MaterialView> uploadFile(MultipartFile file, BucketType bucketType) {
        String fileKey = String.format(AVG_COMMON_FILE_KEY, System.currentTimeMillis());
        String fileName = file.getOriginalFilename();
        if (StringUtils.isNotBlank(fileName)) {
            String suffix = fileName.substring(fileName.lastIndexOf(POINT));
            fileKey = fileKey + System.currentTimeMillis() + suffix;
        }
        try {
            qiniuComponent.uploadWithStream(bucketType.getBucketName(), file.getBytes(), fileKey);
        } catch (IOException e) {
            log.info("uploadFile error", e);
            return BizResult.result(RoleGameResponse.BAD_REQUEST.getCode(), "上传文件失败");
        }
        String url;
        if (bucketType == BucketType.FREE) {
            url = CdnUtil.getDefaultDomainWithBackSlash() + fileKey;
        } else {
            url = CdnHandler.getEncryptionUrl(CdnUtil.getDefaultNewDomainWithBackSlash() + fileKey, CdnPayType.PAY.getCode());
        }
        return BizResult.success(new MaterialView().setKey(fileKey).setName(fileName).setUrl(url));
    }

    public BizResult<SpineMaterialView> uploadSceneMaterial(MultipartFile file) {
        String basicPath = SCENE_MATERIAL_FILE_PATH + System.currentTimeMillis() + SLASH;
        return getSpineMaterialViewBizResult(file, basicPath);
    }

    private MaterialExportBO getMaterialExportBO(String fileKey, Material.BizInfo bizInfo) {
        if (bizInfo == null || bizInfo.getType() == null) {
            return null;
        }
        if (MaterialBizType.COSTUME.getCode() == bizInfo.getType()) {
            List<String> outFilePaths = bizInfo.getRoleIds()
                    .stream()
                    .map(roleId -> String.format(RESOURCES_SPINE_NORMAL, roleId, bizInfo.getCostumeId()))
                    .collect(Collectors.toList());
            return new MaterialExportBO().setFileKey(fileKey).setOutPaths(outFilePaths);
        } else if (MaterialBizType.STORY.getCode() == bizInfo.getType()) {
            Integer storyId = bizInfo.getStoryId();
            return new MaterialExportBO().setFileKey(fileKey).setOutPaths(Lists.newArrayList(String.format(RESOURCES_SPINE_STORY, storyId)));
        } else if (MaterialBizType.ROLE_UNHEALTHY_STATE.getCode() == bizInfo.getType()) {
            return new MaterialExportBO().setFileKey(fileKey)
                    .setOutPaths(Lists.newArrayList(String.format(RESOURCES_ROLE_UNHEALTHY_STATE, bizInfo.getRoleId())));
        }
        log.error("bizInfo type:{} not support", bizInfo.getType());
        return null;
    }

    private List<String> checkAndUploadFrameAnimation(List<File> files) throws FileRequirementNotMetException {
        List<File> pngFiles = files.stream().filter(file -> PNG.equals(file.getName().substring(file.getName().indexOf(".")))).collect(Collectors.toList());
        if (CollectionUtils.size(pngFiles) < MIN_FILE_NUM) {
            throw new FileRequirementNotMetException("至少包含两个图片文件");
        }
        List<Pair<String, String>> fileInfos = Lists.newArrayList();
        for (File file : pngFiles) {
            String name = file.getName();
            String key = ANIMATION_FILE_PREFIX + System.currentTimeMillis() + SLASH + name;
            key = qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, file, key);
            if (StringUtils.isEmpty(key)) {
                throw new FileRequirementNotMetException("图片文件上传失败");
            }
            fileInfos.add(Pair.of(name, key));
        }
        List<String> keyList = fileInfos.stream().sorted(Comparator.comparing(Pair::getLeft)).map(Pair::getRight).collect(Collectors.toList());
        return keyList;
    }

    private List<File> checkAndFilterSpineFile(List<File> files) throws FileRequirementNotMetException {
        if (CollectionUtils.size(files) < FILE_TYPE_NUM) {
            throw new FileRequirementNotMetException("文件数量不足");
        }
        Map<String, List<File>> fileGroupBySuffix = files.stream()
                .filter(file -> file.getParent().endsWith(SPINE))
                .collect(Collectors.groupingBy(file -> file.getName().substring(file.getName().indexOf("."))));
        log.info("checkAndFilterSpineFile fileGroupBySuffix:{}, files:{}, fileParent:{}, fileName:{}", fileGroupBySuffix, files, files.get(0).getParent(),
                files.get(0).getName());
        List<File> jsonFiles = fileGroupBySuffix.get(JSON);
        int jsonCount = CollectionUtils.size(jsonFiles);
        if (jsonCount != 1) {
            throw new FileRequirementNotMetException("json文件数量不正确");
        }
        List<File> atlasFiles = fileGroupBySuffix.entrySet()
                .stream()
                .filter(entry -> ATLAS.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        int atlasCount = CollectionUtils.size(atlasFiles);
        if (atlasCount != 1) {
            log.warn("checkAndFilterSpineFile atlasFiles:{}, fileGroupBySuffix:{}", atlasFiles, fileGroupBySuffix);
            throw new FileRequirementNotMetException("atlas文件数量不正确");
        }
        List<File> pngFiles = fileGroupBySuffix.get(PNG);
        int pngCount = CollectionUtils.size(pngFiles);
        if (pngCount < 1) {
            throw new FileRequirementNotMetException("png文件数量不正确");
        }
        List<File> allFiles = Lists.newArrayList();
        allFiles.addAll(jsonFiles);
        allFiles.addAll(atlasFiles);
        allFiles.addAll(pngFiles);
        return allFiles;
    }

    

    private void checkParamValidator(MultipartFile file, String atlasFileKey, String pngFileKey) throws IllegalArgumentException {
        if (file == null || file.isEmpty() || !Objects.requireNonNull(file.getOriginalFilename()).toLowerCase().endsWith(ZIP_FILE)) {
            throw new IllegalArgumentException("File must be a non-empty zip file");
        }
        if (StringUtils.isBlank(atlasFileKey) || StringUtils.isBlank(pngFileKey)) {
            throw new IllegalArgumentException("Atlas file key and PNG file key cannot be null or empty");
        }
    }

    // 解压到压缩包同名文件夹目录
    private String unzipFile(String fileOutPath) {
        int extensionIndex = fileOutPath.indexOf(POINT);
        String unzipPath = fileOutPath.substring(0, extensionIndex);
        FileUtils.unzipToDirectory(fileOutPath, unzipPath);
        return unzipPath;
    }

    private void downloadFiles(String atlasFileKey, String pngFileKey, String inDir) throws Exception {
        String domain = CdnUtil.getDefaultDomainWithBackSlash();
        String atlasFilePath = atlasFileKey.substring(atlasFileKey.lastIndexOf(SLASH) + 1);
        String pngFilePath = pngFileKey.substring(pngFileKey.lastIndexOf(SLASH) + 1);
        FileUtils.downloadFile(domain + atlasFileKey, inDir + SLASH + atlasFilePath);
        FileUtils.downloadFile(domain + pngFileKey, inDir + SLASH + pngFilePath);
    }

    private void runLibgdxApplication(String inDir, String tmpDir, String replDir, String outDir) {
        HeadlessApplicationConfiguration config = new HeadlessApplicationConfiguration();
        new HeadlessApplication(new CostumeReplaceListener(inDir, tmpDir, replDir, outDir), config);
    }

    private void waitForOutputFiles(String outDir) throws Exception {
        File outDirFile = new File(outDir);
        long startTime = System.currentTimeMillis();
        while (outDirFile.listFiles() == null || Objects.requireNonNull(outDirFile.listFiles()).length < 2) {
            if (System.currentTimeMillis() - startTime > 30000) {
                throw new Exception("Timeout waiting for output files");
            }
            log.info("等待替换图集完成...");
            Thread.sleep(500);
        }
    }

    private SpineMaterialView uploadOutputFiles(String outDir, String pre) throws Exception {
        File outDirFile = new File(outDir);
        File[] files = outDirFile.listFiles();
        if (files == null || files.length < 1) {
            throw new Exception("获取合图文件夹为空");
        }
        SpineMaterialView spineMaterialView = new SpineMaterialView();
        for (File needFile : files) {
            String name = needFile.getName();
            String key = pre + System.currentTimeMillis() + SLASH + name;
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, needFile, key);
            if (name.endsWith(PNG)) {
                spineMaterialView.setPngFileKey(key);
            } else if (name.endsWith(ATLAS1)) {
                spineMaterialView.setAtlasFileKey(key);
            }
        }
        return spineMaterialView;
    }

    public BizResult<SpineMaterialView> replaceCostumePart(MultipartFile file, String atlasFileKey, String pngFileKey) {

        // 替换装扮参数校验
        try {
            checkParamValidator(file, atlasFileKey, pngFileKey);
        } catch (IllegalArgumentException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), e.getMessage());
        }

        String basicPath = REPLACE_COSTUME_PART_PATH + System.currentTimeMillis() + SLASH;
        String inDir = basicPath + "in";
        String outDir = basicPath + "out";
        String tmpDir = basicPath + "tmp";
        String pre = "role/game/material/";

        try {
            String fileOutPath = basicPath + file.getOriginalFilename();
            if (!FileUtils.saveMultipartFile(file, fileOutPath)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
            }

            // 解压文件并获取目录名
            long startTime = System.currentTimeMillis();
            String replDir = unzipFile(fileOutPath);
            long unzipTime = System.currentTimeMillis() - startTime;
            log.info("unzipFile cost {} ms", unzipTime);

            // 下载 atlas 和 png 文件
            startTime = System.currentTimeMillis();
            downloadFiles(atlasFileKey, pngFileKey, inDir);
            long downloadTime = System.currentTimeMillis() - startTime;
            log.info("downloadFiles cost {} ms", downloadTime);

            // 运行 libgdx 应用
            startTime = System.currentTimeMillis();
            runLibgdxApplication(inDir, tmpDir, replDir, outDir);
            long runLibgdxTime = System.currentTimeMillis() - startTime;
            log.info("runLibgdxApplication cost {} ms", runLibgdxTime);

            // 等待输出文件生成完毕
            startTime = System.currentTimeMillis();
            waitForOutputFiles(outDir);
            long waitOutputTime = System.currentTimeMillis() - startTime;
            log.info("waitForOutputFiles cost {} ms", waitOutputTime);

            // 将输出文件上传到七牛
            startTime = System.currentTimeMillis();
            SpineMaterialView spineMaterialView = uploadOutputFiles(outDir, pre);
            long uploadTime = System.currentTimeMillis() - startTime;
            log.info("uploadOutputFiles cost {} ms", uploadTime);

            return BizResult.success(spineMaterialView);
        } catch (Exception e) {
            log.error("Error processing replace costume", e);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "替换装扮失败");
        } finally {
            FileUtils.deleteDirectory(basicPath);
        }
    }

    
    private @NotNull BizResult<SpineMaterialView> getSpineMaterialViewBizResult(MultipartFile file, String basicPath) {
        try {
            String fileOutPath = basicPath + file.getOriginalFilename();
            boolean isSuccess = FileUtils.saveMultipartFile(file, fileOutPath);
            if (!isSuccess) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件上传失败");
            }
            List<File> unzipFileList = FileUtils.unzip(fileOutPath);
            if (CollectionUtils.isEmpty(unzipFileList)) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文件解压失败");
            }
            List<File> needFiles = checkAndFilterSpineFile(unzipFileList);
            String filePath = needFiles.stream().filter(item -> item.getName().contains(JSON)).map(File::getAbsolutePath).findFirst().orElse("");
            //读取json文件中的animations key的值
            String jsonData = FileUtils.readJsonFile(filePath);
            if (jsonData == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "json文件读取失败");
            }
            Map<String, Object> jsonContent = JsonUtils.fromJson(jsonData);
            if (jsonContent == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "json文件解析失败");
            }
            Map<String, Object> animationsInfo = (Map<String, Object>) jsonContent.get(ANIMATIONS);
            if (animationsInfo == null) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "spine文件不存在animations配置");
            }
            Set<String> animations = animationsInfo.keySet();
            SpineMaterialView materialViewV2 = getMaterialViewV2(unzipFileList, file);
            materialViewV2.setAnimations(animations);
            materialViewV2.setName(file.getOriginalFilename());
            return BizResult.success(materialViewV2);
        } catch (FileRequirementNotMetException | IOException e) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), e.getMessage());
        } finally {
            FileUtils.deleteDirectory(basicPath);
        }
    }
}
