package com.kuaikan.role.game.admin.biz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.comic.bean.Topic;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.service.TopicService;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.tools.serialize.JsonUtils;
import com.kuaikan.game.common.util.PageUtil;
import com.kuaikan.role.game.admin.battle.dao.CardIdModelConfigDao;
import com.kuaikan.role.game.admin.common.BattleUploadType;
import com.kuaikan.role.game.admin.component.battle.ConfigExcelUploadFactory;
import com.kuaikan.role.game.admin.model.excel.CardBattleBondMapIntroduceData;
import com.kuaikan.role.game.admin.model.excel.CardBattleLimitedTimeActivityPrizeData;
import com.kuaikan.role.game.admin.model.excel.LimitedActivityLotteryPrizeConfExcelDto;
import com.kuaikan.role.game.admin.model.param.BattleNewActivityAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.BattlePageQueryParam;
import com.kuaikan.role.game.admin.model.view.battle.BattleExploreView;
import com.kuaikan.role.game.admin.model.view.battle.BizResultWithHost;
import com.kuaikan.role.game.admin.model.view.battle.PageWrapper;
import com.kuaikan.role.game.admin.repository.BattleExploreRepo;
import com.kuaikan.role.game.admin.repository.BattleTaskRepo;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.admin.utils.ExcelUtils;
import com.kuaikan.role.game.admin.utils.HostUtil;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.cardbattle.BattleActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.BattleTask;
import com.kuaikan.role.game.api.bean.cardbattle.BondActivityExtraConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardIdModelConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLimitedTimeActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.CardBattleLotteryActivityConfig;
import com.kuaikan.role.game.api.bean.cardbattle.PlotCard;
import com.kuaikan.role.game.api.constant.CardBattleResponseCodeMsg;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleExploreTypeEnum;
import com.kuaikan.role.game.api.enums.cardbattle.CardBattleLimitedTimeActivityTypeEnum;
import com.kuaikan.role.game.api.service.RedDotService;
import com.kuaikan.role.game.api.service.cardbattle.CardBattleActivityService;
import com.kuaikan.role.game.api.service.cardbattle.CardBattleDataFixService;

@Slf4j
@Service
public class BattleNewActivityBiz {

    @Resource
    private BattleExploreRepo battleExploreRepo;
    @Resource
    private TopicService topicService;
    @Resource
    private CardIdModelConfigDao cardIdModelConfigDao;
    @Resource
    private ConfigExcelUploadFactory configExcelUploadFactory;
    @Resource
    private CardBattleDataFixService cardBattleDataFixService;
    @Resource
    private BattleTaskRepo battleTaskRepo;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private CardBattleActivityService cardBattleActivityService;
    @Resource
    private RedDotService redDotService;

    public BizResult<PageWrapper<BattleExploreView>> listBattleExplore(BattlePageQueryParam query) {
        if (StringUtils.isNotBlank(query.getId())) {
            BattleActivityConfig explore = battleExploreRepo.getBattleExploreById(query.getId());
            if (explore == null) {
                return BizResult.success(PageWrapper.of(0, Lists.newArrayList()));
            }
            BattleExploreView exploreView = BattleExploreView.convert(explore);
            List<String> topicNames = exploreView.getTopicIds().stream().map(topicId -> {
                Topic topic = topicService.getTopicById(topicId);
                return Optional.ofNullable(topic).map(Topic::getTitle).orElse("");
            }).collect(Collectors.toList());
            exploreView.setTopicNames(topicNames);
            return BizResult.success(PageWrapper.of(1, Lists.newArrayList(exploreView)));
        }
        int page = query.getPage();
        int pageSize = query.getPageSize();
        int type = query.getType();
        int total = battleExploreRepo.countBattleExploreWithType(type);
        List<BattleActivityConfig> explores = battleExploreRepo.listBattleExplore(pageSize, page, type);
        int totalPage = PageUtil.getTotalPage(total, pageSize);
        List<BattleExploreView> exploreDtos = Lists.newArrayList();
        for (BattleActivityConfig explore : explores) {
            BattleExploreView exploreView = BattleExploreView.convert(explore);
            List<String> topicNames = exploreView.getTopicIds().stream().map(topicId -> {
                Topic topic = topicService.getTopicById(topicId);
                return Optional.ofNullable(topic).map(Topic::getTitle).orElse("");
            }).collect(Collectors.toList());
            exploreView.setTopicNames(topicNames);
            exploreDtos.add(exploreView);
        }
        return BizResult.success(PageWrapper.of(totalPage, Lists.newArrayList(exploreDtos)));
    }

    public BizResultWithHost<BattleExploreView> getBattleExploreById(String id) {
        BattleActivityConfig battleActivityConfig = battleExploreRepo.getBattleExploreById(id);
        if (battleActivityConfig == null) {
            return BizResultWithHost.result(CardBattleResponseCodeMsg.CARD_BATTLE_NO_FOUND_EXPLORE_CONFIG);
        }
        BattleExploreView exploreView = BattleExploreView.convert(battleActivityConfig);
        List<String> topicNames = exploreView.getTopicIds().stream().map(topicId -> {
            Topic topic = topicService.getTopicById(topicId);
            return Optional.ofNullable(topic).map(Topic::getTitle).orElse("");
        }).collect(Collectors.toList());
        exploreView.setTopicNames(topicNames);
        ListUtils.emptyIfNull(exploreView.getBattleTaskConfig()).forEach(e -> {
            BattleTask battleTask = battleTaskRepo.getBattleTaskById(e.getBattleTaskId());
            if (battleTask != null) {
                e.setName(battleTask.getName());
            }
        });

        if (battleActivityConfig.getType().equals(CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode())
                && exploreView.getLimitedTimeActivityView() != null) {
            //限时冒险者的专题填充
            if (exploreView.getLimitedTimeActivityView().isHasTimeLimitRole() && exploreView.getLimitedTimeActivityView().getTimeLimitRole() != null) {
                int topicId = exploreView.getLimitedTimeActivityView().getTimeLimitRole().getTopicId();
                exploreView.getLimitedTimeActivityView().getTimeLimitRole().setTopicName(topicService.getTopicById(topicId).getTitle());
            }
            //限定角色填充
            if (exploreView.getLimitedTimeActivityView().isHasLimitRoles() && exploreView.getLimitedTimeActivityView().getLimitRoleIds() != null) {
                List<Integer> limitRoleIds = exploreView.getLimitedTimeActivityView().getLimitRoleIds();
                Map<Integer, Role> limitRoleMap = roleRepository.queryByIds(limitRoleIds.stream().filter(r -> r > 0).collect(Collectors.toList()))
                        .stream()
                        .collect(Collectors.toMap(Role::getId, Function.identity()));
                List<String> limitRoleNames = new ArrayList<>();
                for (Integer roleId : limitRoleIds) {
                    if (roleId == 0) {
                        limitRoleNames.add("团子");
                        continue;
                    }
                    if (roleId == -1) {
                        String name = Optional.ofNullable(exploreView.getLimitedTimeActivityView().getTimeLimitRole())
                                .map(CardBattleLimitedTimeActivityConfig.TimeLimitRole::getName)
                                .orElse("");
                        limitRoleNames.add(name);
                        continue;
                    }
                    limitRoleNames.add(limitRoleMap.get(roleId).getName());
                }
                exploreView.getLimitedTimeActivityView().setLimitRoleNames(limitRoleNames);
            }
        }
        return BizResultWithHost.success(exploreView).setHost(HostUtil.getHost());
    }

    public BizResult<String> createBattleExplore(BattleNewActivityAddOrUpdateParam request) {
        try {
            BattleActivityConfig battleActivityConfig = request.toBattleExplore();
            String extraInfo;
            if (CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode().equals(request.getType())) {
                checkLimitedTimeExplore(request.getLimitedTimeActivityView());
                extraInfo = JsonUtils.toJson(request.getLimitedTimeActivityView());
            } else if (CardBattleExploreTypeEnum.BOND.getCode().equals(request.getType())) {
                extraInfo = JsonUtils.toJson(request.getBondActivityView());
            } else if (CardBattleExploreTypeEnum.LIMITED_TIME_LOTTERY.getCode().equals(request.getType())
                    || CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE_V2.getCode().equals(request.getType())) {
                extraInfo = JsonUtils.toJson(request.getLotteryActivityView());
            } else {
                extraInfo = JsonUtils.toJson(request.getExtraInfoView());
            }
            battleActivityConfig.setExtraInfo(extraInfo);
            long now = System.currentTimeMillis();
            battleActivityConfig.setCreateTime(now);
            battleActivityConfig.setUpdateTime(now);
            String battleActivityId = battleExploreRepo.createBattleExplore(battleActivityConfig);
            log.info("Create battleActivityConfig success, battleExploreId:{}, battleExploreInfo:{}, Operator:{}", battleActivityConfig.getId(),
                    battleExploreRepo, request.getOperator());
            return BizResult.success(battleActivityId);
        } catch (Exception e) {
            log.error("Create battleExplore failed, battleExploreInfo={}", request, e);
            return BizResult.result(-1, e.getMessage());
        }
    }

    private void checkLimitedTimeExplore(CardBattleLimitedTimeActivityConfig limitedTimeActivityView) {
        String activityCardId = limitedTimeActivityView.getActivityCardId();
        CardBattleCardIdModelConfig modelConfig = cardIdModelConfigDao.getByCardId(activityCardId);
        if (modelConfig == null) {
            throw new RuntimeException("活动卡片id不存在");
        }
        Integer limitedTimeActivityType = limitedTimeActivityView.getLimitedTimeActivityType();
        if (CardBattleLimitedTimeActivityTypeEnum.CUSTOM_ACTIVITY.getCode().equals(limitedTimeActivityType)) {
            if (StringUtils.isBlank(limitedTimeActivityView.getCustomActivityName())) {
                throw new RuntimeException("自定义活动名称不能为空");
            }
        }
    }

    public BizResult<Void> updateBattleExplore(BattleNewActivityAddOrUpdateParam request) {
        try {
            BattleActivityConfig oldExplore = battleExploreRepo.getBattleExploreById(request.getId());
            if (oldExplore == null) {
                return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_NO_FOUND_EXPLORE_CONFIG);
            }
            BattleActivityConfig battleActivityConfig = request.toBattleExplore();
            String extraInfo;
            if (CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE.getCode().equals(request.getType())) {
                checkLimitedTimeExplore(request.getLimitedTimeActivityView());
                CardBattleLimitedTimeActivityConfig oldActivityConfig = JsonUtils.fromJson(oldExplore.getExtraInfo(),
                        CardBattleLimitedTimeActivityConfig.class);
                Optional.ofNullable(oldActivityConfig).ifPresent(e -> {
                    request.getLimitedTimeActivityView().setPrizeConfigList(e.getPrizeConfigList());
                });
                extraInfo = JsonUtils.toJson(request.getLimitedTimeActivityView());
            } else if (CardBattleExploreTypeEnum.BOND.getCode().equals(request.getType())) {
                extraInfo = JsonUtils.toJson(request.getBondActivityView());
            } else if (CardBattleExploreTypeEnum.LIMITED_TIME_LOTTERY.getCode().equals(request.getType())
                    || CardBattleExploreTypeEnum.LIMITED_TIME_EXPLORE_V2.getCode().equals(request.getType())) {
                // 如果是上线状态, 需要校验库存变化
                if (oldExplore.getStatus() == 1 && oldExplore.getStartTime() >= System.currentTimeMillis()) {
                    CardBattleLotteryActivityConfig oldLotteryConfig = JsonUtils.fromJson(oldExplore.getExtraInfo(), CardBattleLotteryActivityConfig.class);
                    checkActivityLotteryPrizeConfig(request.getLotteryActivityView(), oldLotteryConfig);
                }
                extraInfo = JsonUtils.toJson(request.getLotteryActivityView());
            } else {
                extraInfo = JsonUtils.toJson(request.getExtraInfoView());
            }
            battleActivityConfig.setExtraInfo(extraInfo);
            battleActivityConfig.setUpdateTime(System.currentTimeMillis());
            // 更新记录
            recordOperator(oldExplore, battleActivityConfig, request.getOperator());
            boolean ret = battleExploreRepo.updateBattleExplore(battleActivityConfig);
            if (ret) {
                log.info("Update battleActivityConfig success, battleExploreId:{}, battleExploreInfo:{}, Operator:{}", battleActivityConfig.getId(),
                        battleActivityConfig, request.getOperator());
                return BizResult.success();
            } else {
                log.error("Update battleActivityConfig failed, battleExploreId:{}, battleExploreInfo:{}, Operator:{}", battleActivityConfig.getId(),
                        battleActivityConfig, request.getOperator());
                return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_EXPLORE_UPDATE_FAIL);
            }
        } catch (Exception e) {
            log.error("Create battleExplore failed, battleExploreInfo={}", request, e);
            return BizResult.result(-1, e.getMessage());
        }
    }

    private void recordOperator(BattleActivityConfig oldExplore, BattleActivityConfig battleActivityConfig, String operator) {
        List<BattleActivityConfig.OperatorRecord> operatorRecords = Optional.ofNullable(oldExplore.getOperatorRecords()).orElse(new ArrayList<>());
        StringBuilder builder = new StringBuilder();
        if (!Objects.equals(oldExplore.getName(), battleActivityConfig.getName())) {
            builder.append("活动名称由[").append(oldExplore.getName()).append("]修改为[").append(battleActivityConfig.getName()).append("]").append("。");
        }
        if (!Objects.equals(oldExplore.getSubTitle(), battleActivityConfig.getSubTitle())) {
            builder.append("副标题由[").append(oldExplore.getSubTitle()).append("]修改为[").append(battleActivityConfig.getSubTitle()).append("]").append("。");
        }
        if (!Objects.equals(oldExplore.getType(), battleActivityConfig.getType())) {
            builder.append("活动类型由[").append(oldExplore.getType()).append("]修改为[").append(battleActivityConfig.getType()).append("]").append("。");
        }
        if (!Objects.equals(oldExplore.getBanner(), battleActivityConfig.getBanner())) {
            builder.append("banner图由[").append(oldExplore.getBanner()).append("]修改为[").append(battleActivityConfig.getBanner()).append("]").append("。");
        }
        if (!Objects.equals(oldExplore.getBattleDungeonConfig(), battleActivityConfig.getBattleDungeonConfig())) {
            builder.append("副本id列表配置由[")
                    .append(oldExplore.getBattleDungeonConfig())
                    .append("]修改为[")
                    .append(battleActivityConfig.getBattleDungeonConfig())
                    .append("]")
                    .append("。");
            // 副本id列表发生变化，会影响红点
            redDotService.sendBondGroupNewActivityEvent(Lists.newArrayList(battleActivityConfig.getRoleGroupId()));
        }
        if (!Objects.equals(oldExplore.getBattleTaskConfig(), battleActivityConfig.getBattleTaskConfig())) {
            builder.append("战斗关卡id列表配置由[")
                    .append(oldExplore.getBattleTaskConfig())
                    .append("]修改为[")
                    .append(battleActivityConfig.getBattleTaskConfig())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.isLimitTopic(), battleActivityConfig.isLimitTopic())) {
            builder.append("是否限制专题由[")
                    .append(oldExplore.isLimitTopic())
                    .append("]修改为[")
                    .append(battleActivityConfig.isLimitTopic())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getTopicIds(), battleActivityConfig.getTopicIds())) {
            builder.append("专题id列表配置由[")
                    .append(oldExplore.getTopicIds())
                    .append("]修改为[")
                    .append(battleActivityConfig.getTopicIds())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getLimitedBoostTopicIds(), battleActivityConfig.getLimitedBoostTopicIds())) {
            builder.append("限定加成专题列表配置由[")
                    .append(oldExplore.getLimitedBoostTopicIds())
                    .append("]修改为[")
                    .append(battleActivityConfig.getLimitedBoostTopicIds())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getLimitedBoostRatio(), battleActivityConfig.getLimitedBoostRatio())) {
            builder.append("限定加成比例由[")
                    .append(oldExplore.getLimitedBoostRatio())
                    .append("]修改为[")
                    .append(battleActivityConfig.getLimitedBoostRatio())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getStartTime(), battleActivityConfig.getStartTime())) {
            builder.append("活动开始时间由[")
                    .append(oldExplore.getStartTime())
                    .append("]修改为[")
                    .append(battleActivityConfig.getStartTime())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getEndTime(), battleActivityConfig.getEndTime())) {
            builder.append("活动结束时间由[")
                    .append(oldExplore.getEndTime())
                    .append("]修改为[")
                    .append(battleActivityConfig.getEndTime())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getOfflineTime(), battleActivityConfig.getOfflineTime())) {
            builder.append("活动下线时间由[")
                    .append(oldExplore.getOfflineTime())
                    .append("]修改为[")
                    .append(battleActivityConfig.getOfflineTime())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getExtraInfo(), battleActivityConfig.getExtraInfo())) {
            builder.append("活动具体配置由[")
                    .append(oldExplore.getExtraInfo())
                    .append("]修改为[")
                    .append(battleActivityConfig.getExtraInfo())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getResourceImg(), battleActivityConfig.getResourceImg())) {
            builder.append("资源位图片由[")
                    .append(oldExplore.getResourceImg())
                    .append("]修改为[")
                    .append(battleActivityConfig.getResourceImg())
                    .append("]")
                    .append("。");
        }
        if (!Objects.equals(oldExplore.getClickInteractive(), battleActivityConfig.getClickInteractive())) {
            builder.append("点击交互由[")
                    .append(oldExplore.getClickInteractive())
                    .append("]修改为[")
                    .append(battleActivityConfig.getClickInteractive())
                    .append("]")
                    .append("。");
        }

        if (StringUtils.isNotBlank(builder.toString())) {
            BattleActivityConfig.OperatorRecord record = BattleActivityConfig.OperatorRecord.init(operator, builder.toString());
            operatorRecords.add(record);
            battleActivityConfig.setOperatorRecords(operatorRecords);
        }
    }

    private void checkActivityLotteryPrizeConfig(CardBattleLotteryActivityConfig newLotteryConfig, CardBattleLotteryActivityConfig oldLotteryConfig) {
        List<CardBattleLotteryActivityConfig.LotteryPrizeItem> lotteryPrizeConfOld = newLotteryConfig.getLotteryPrizeConf();
        List<CardBattleLotteryActivityConfig.LotteryPrizeItem> lotteryPrizeConfNew = oldLotteryConfig.getLotteryPrizeConf();
        // 有一个为空，就没必要对比数据
        if (CollectionUtils.isEmpty(lotteryPrizeConfNew) || CollectionUtils.isEmpty(lotteryPrizeConfOld)) {
            return;
        }
        Map<Integer, CardBattleLotteryActivityConfig.LotteryPrizeItem> newIndexPrizeConfigMap = lotteryPrizeConfNew.stream()
                .collect(Collectors.toMap(CardBattleLotteryActivityConfig.LotteryPrizeItem::getIndex, Function.identity()));
        // 对比带库存的奖品变化
        for (CardBattleLotteryActivityConfig.LotteryPrizeItem prizeItem : lotteryPrizeConfOld) {
            if (prizeItem.getPrizeStock() > 0) {
                CardBattleLotteryActivityConfig.LotteryPrizeItem newPrizeItem = newIndexPrizeConfigMap.get(prizeItem.getIndex());
                if (!Objects.equals(prizeItem.getPrizeStock(), newPrizeItem.getPrizeStock()) && newPrizeItem.getPrizeStock() != -1) {
                    throw new RuntimeException(String.format("抽奖活动已上线，序号:%s奖品库存不一致", prizeItem.getIndex()));
                }
            }
        }
        // todo , 后续可以考虑活动上线之后，支持库存增加， 需要更新库存的hash
    }

    public BizResult<Void> updateBattleExploreStatus(String id, Integer status) {
        BattleActivityConfig oldExplore = battleExploreRepo.getBattleExploreById(id);
        if (oldExplore == null) {
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_NO_FOUND_EXPLORE_CONFIG);
        }
        if (status == 1 && (oldExplore.getEndTime() != null && oldExplore.getEndTime() < System.currentTimeMillis())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "不能启动已经结束或者未设置活动时间的副本活动，若要开启，请先编辑副本活动时间");
        }
        // 校验羁绊活动副本
        try {
            if (status == 1 && oldExplore.getType() == CardBattleExploreTypeEnum.BOND.getCode()) {
                checkBondActivityOrder(oldExplore, oldExplore.getOrder());
                // 开启羁绊副本活动时，未开启白名单时，添加上新红点
                if (!oldExplore.isWhiteListSwitch()) {
                    redDotService.sendBondGroupNewActivityEvent(Lists.newArrayList(oldExplore.getRoleGroupId()));
                }
            }
        } catch (Exception e) {
            log.error("updateBattleExploreStatus check failed, activityId={}, ", id, e);
            return BizResult.result(-1, e.getMessage());
        }
        battleExploreRepo.updateBattleExploreStatus(id, status);
        return BizResult.success();
    }

    public void checkBondActivityOrder(BattleActivityConfig activityConfig, int order) {
        Integer roleGroupId = activityConfig.getRoleGroupId();
        List<BattleActivityConfig> list = battleExploreRepo.listBondActivityByRoleGroupId(roleGroupId);
        // 查询status为1的活动, 并且order与当前活动的order相同
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        boolean anyMatch = list.stream().anyMatch(e -> {
            if (activityConfig.getId().equals(e.getId())) {
                return false;
            }
            if (e.getStatus() == 1 && e.getOrder() == order) {
                return true;
            }
            return false;
        });
        if (anyMatch) {
            throw new RuntimeException("羁绊活动的顺序不能相同, 请修改活动顺序再进行操作");
        }
    }

    public BizResult<Void> updateBattleExploreOrder(String id, Integer order) {
        BattleActivityConfig oldExplore = battleExploreRepo.getBattleExploreById(id);
        if (oldExplore == null) {
            return BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_NO_FOUND_EXPLORE_CONFIG);
        }
        // 校验羁绊活动副本
        try {
            if (oldExplore.getStatus() == 1 && oldExplore.getType() == CardBattleExploreTypeEnum.BOND.getCode()) {
                checkBondActivityOrder(oldExplore, order);
            }
        } catch (Exception e) {
            log.error("updateBattleExploreStatus check failed, activityId={}, ", id, e);
            return BizResult.result(-1, e.getMessage());
        }
        battleExploreRepo.updateBattleExploreOrder(id, order);
        return BizResult.success();
    }

    public Object getPlotCardPoolIds() {
        Set<String> cardPoolIds = cardIdModelConfigDao.selectAll().stream().map(e -> {
            if (NumberUtils.isDigits(e.getCardId())) {
                return null;
            }
            String[] split = e.getCardId().split("-");
            if (split.length == 2) {
                return split[0];
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toSet());

        return BizResult.success(cardPoolIds);
    }

    public Object getLimitedTimeActivityType() {
        HashMap<Integer, String> ret = Maps.newHashMapWithExpectedSize(2);
        Arrays.stream(CardBattleLimitedTimeActivityTypeEnum.values()).forEach(e -> {
            ret.put(e.getCode(), e.getDesc());
        });
        return BizResult.success(ret);
    }

    public void uploadExcelFile(MultipartFile file, String id) {
        BattleActivityConfig battleActivityConfig = battleExploreRepo.getBattleExploreById(id);
        if (battleActivityConfig == null) {
            log.warn("upload cardBattleLimitedTimeActivityPrizeData battleActivityConfig empty, id={}", id);
            return;
        }
        List<CardBattleLimitedTimeActivityPrizeData> prizeDataList;
        try {
            prizeDataList = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.LIMITED_TIME_ACTIVITY_PRIZE,
                    CardBattleLimitedTimeActivityPrizeData.class);
            log.info("uploadExcelFile prizeDataList={}", prizeDataList);
        } catch (Exception e) {
            log.error("uploadExcelFile error", e);
            throw new RuntimeException("上传文件解析错误");
        }
        if (CollectionUtils.isEmpty(prizeDataList)) {
            log.warn("upload cardBattleLimitedTimeActivityPrizeData prizeDataList empty, id={}", id);
            return;
        }
        List<Long> prizeIdList = new ArrayList<>();
        prizeDataList.forEach(e -> {
            if (e.getPrizeId() > 0) {
                prizeIdList.add(e.getPrizeId());
            }
            if (e.getSsrPrizeId() > 0) {
                prizeIdList.add(e.getSsrPrizeId());
            }
            if (e.getGtSsrPrizeId() > 0) {
                prizeIdList.add(e.getGtSsrPrizeId());
            }
        });
        long count = prizeIdList.stream().filter(Objects::nonNull).distinct().count();
        if (count != prizeIdList.size()) {
            log.warn("upload cardBattleLimitedTimeActivityPrizeData prizeId repeat, id={}", id);
            throw new RuntimeException("奖励id重复");
        }

        List<CardBattleLimitedTimeActivityConfig.PrizeConfig> prizeConfigList = prizeDataList.stream()
                .map(CardBattleLimitedTimeActivityPrizeData::toPrizeConfig)
                .collect(Collectors.toList());
        log.info("uploadExcelFile prizeConfigList={}", prizeConfigList);
        CardBattleLimitedTimeActivityConfig activityConfig = JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLimitedTimeActivityConfig.class);
        if (activityConfig == null) {
            log.warn("upload cardBattleLimitedTimeActivityPrizeData activityConfig empty, id={}", id);
            return;
        }
        activityConfig.setPrizeConfigList(prizeConfigList);

        battleExploreRepo.updateExtraInfo(battleActivityConfig.getId(), JsonUtils.toJson(activityConfig));
        cardBattleDataFixService.delLimitedTimeActivityPrizeListCache(battleActivityConfig.getId());
    }

    public void downloadExcel(HttpServletResponse response, String id) {
        BattleActivityConfig battleActivityConfig = battleExploreRepo.getBattleExploreById(id);
        if (battleActivityConfig == null) {
            return;
        }
        CardBattleLimitedTimeActivityConfig activityConfig = JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLimitedTimeActivityConfig.class);
        if (activityConfig == null || CollectionUtils.isEmpty(activityConfig.getPrizeConfigList())) {
            log.warn("upload cardBattleLimitedTimeActivityPrizeData prizeConfigList empty, id={}", id);
            return;
        }
        ExcelUtils.writeExcel("限时活动奖励配置", activityConfig.getPrizeConfigList(), CardBattleLimitedTimeActivityPrizeData.class, response);
    }

    public Object fetchBattleTaskList(Integer battleTaskType) {
        // 已经选过的战斗关卡id
        List<BattleActivityConfig> activityList = battleExploreRepo.listAllActivityExistBattleTaskId();
        Set<String> selectedBattleTaskIdSet = activityList.stream()
                .flatMap(e -> e.getBattleTaskConfig().stream())
                .map(BattleActivityConfig.BattleTaskBasicConfig::getBattleTaskId)
                .collect(Collectors.toSet());

        List<BattleTask> battleTaskList = battleTaskRepo.listAllByType(battleTaskType)
                .stream()
                .filter(e -> !selectedBattleTaskIdSet.contains(e.getId()))
                .collect(Collectors.toList());
        return BizResult.success(battleTaskList);
    }

    public Object selectLimitRoleList(String id, int pageNum, int pageSize) {
        List<CardBattleLimitedTimeActivityConfig.LimitRole> roleList = new ArrayList<>();
        //当前副本活动中配置的限时冒险者
        BattleActivityConfig explore;
        if (StringUtils.isNotBlank(id) && (explore = battleExploreRepo.getBattleExploreById(id)) != null) {
            CardBattleLimitedTimeActivityConfig config = JsonUtils.fromJson(explore.getExtraInfo(), CardBattleLimitedTimeActivityConfig.class);
            CardBattleLimitedTimeActivityConfig.TimeLimitRole timeLimitRole = config.getTimeLimitRole();
            if (config.isHasTimeLimitRole() && timeLimitRole != null) {
                roleList.add(new CardBattleLimitedTimeActivityConfig.LimitRole().setId(-1).setName(timeLimitRole.getName()));
            }
        }
        //默认角色：团子
        roleList.add(new CardBattleLimitedTimeActivityConfig.LimitRole().setId(0).setName("团子"));
        //角色化的冒险者
        List<Role> roles = roleRepository.queryByPage((pageNum - 1) * pageSize, pageSize);
        roleList.addAll(roles.stream()
                .map(r -> new CardBattleLimitedTimeActivityConfig.LimitRole().setId(r.getId()).setName(r.getName()))
                .collect(Collectors.toList()));
        return BizResult.success(PageView.form(roleList.size(), roleList));
    }

    public Object updateWhiteList(String id, boolean whiteListSwitch, String whiteList) {
        BattleActivityConfig battleActivityConfig = battleExploreRepo.getBattleExploreById(id);
        if (battleActivityConfig == null) {
            log.warn("updateWhiteList battleActivityConfig empty, id={}", id);
            return BizResult.success();
        }
        // 关闭白名单时，添加上新红点
        if (!whiteListSwitch && battleActivityConfig.getStatus() == 1 && battleActivityConfig.getType() == CardBattleExploreTypeEnum.BOND.getCode()) {
            redDotService.sendBondGroupNewActivityEvent(Lists.newArrayList(battleActivityConfig.getRoleGroupId()));
        }
        boolean ret = battleExploreRepo.updateWhiteList(id, whiteListSwitch, whiteList);
        return ret ? BizResult.success() : BizResult.result(CardBattleResponseCodeMsg.CARD_BATTLE_EXPLORE_UPDATE_FAIL);
    }

    public Object getLimitActivityCardPoolCardList(String activitySign) {
        List<PlotCard> plotCardList = cardBattleActivityService.fetchLimitActivityCardPoolCardList(activitySign);
        return BizResult.success(plotCardList);
    }

    public Object uploadLotteryPrizeConf(MultipartFile file) {
        List<LimitedActivityLotteryPrizeConfExcelDto> list = new ArrayList<>();
        try {
            list = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.LIMITED_TIME_LOTTERY_PRIZE,
                    LimitedActivityLotteryPrizeConfExcelDto.class);
            log.info("uploadExcelFile lotteryPrizeDataList={}", list);
        } catch (Exception e) {
            log.error("uploadExcelFile error", e);
            throw new RuntimeException("上传文件解析错误");
        }
        if (list.stream().map(LimitedActivityLotteryPrizeConfExcelDto::getIndex).distinct().count() != list.size()) {
            throw new RuntimeException("序号重复，请修改后重新上传");
        }
        // list按stag分组并排序
        Map<Integer, List<LimitedActivityLotteryPrizeConfExcelDto>> stagConfListMap = list.stream()
                .collect(Collectors.groupingBy(LimitedActivityLotteryPrizeConfExcelDto::getStage));
        // 校验各个阶段, 从第二阶段开始
        for (int i = 1; i <= stagConfListMap.size(); i++) {
            List<LimitedActivityLotteryPrizeConfExcelDto> stageList = stagConfListMap.get(i);
            if (stageList == null || stageList.size() == 0) {
                throw new RuntimeException("第" + i + "阶段没有配置");
            }
            stageList = stageList.stream().sorted(Comparator.comparing(LimitedActivityLotteryPrizeConfExcelDto::getIndex)).collect(Collectors.toList());
            long tagCount = stageList.stream().skip(1).filter(e -> StringUtils.isNotBlank(e.getTag())).count();
            if (tagCount > 0) {
                throw new RuntimeException("第" + i + "阶段的大奖tag最多只能有一个, 并且只能在该阶段第一个");
            }
            LimitedActivityLotteryPrizeConfExcelDto first = stageList.get(0);
            if (first.getUnlockConditionPrizeId() != null && i > 1) {
                List<LimitedActivityLotteryPrizeConfExcelDto> beforeList = stagConfListMap.get(i - 1);
                boolean anyMatch = beforeList.stream().anyMatch(e -> {
                    if (first.getUnlockConditionPrizeId().equals(e.getPrizeId())) {
                        return true;
                    }
                    return false;
                });
                if (!anyMatch) {
                    throw new RuntimeException("第" + i + "阶段的解锁条件id在第" + (i - 1) + "阶段没有配置");
                }
            }
        }

        return BizResult.success(list);
    }

    public void downloadLotteryPrizeConf(HttpServletResponse response, String id) {
        BattleActivityConfig battleActivityConfig = battleExploreRepo.getBattleExploreById(id);
        if (battleActivityConfig == null) {
            return;
        }
        CardBattleLotteryActivityConfig activityConfig = JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), CardBattleLotteryActivityConfig.class);
        if (activityConfig == null || CollectionUtils.isEmpty(activityConfig.getLotteryPrizeConf())) {
            log.warn("downloadLotteryPrizeConf lotteryPrizeConf empty, id={}", id);
            return;
        }
        List<CardBattleLotteryActivityConfig.LotteryPrizeItem> lotteryPrizeList = activityConfig.getLotteryPrizeConf();
        // 按index排序
        List<CardBattleLotteryActivityConfig.LotteryPrizeItem> sortedList = lotteryPrizeList.stream()
                .sorted(Comparator.comparing(CardBattleLotteryActivityConfig.LotteryPrizeItem::getIndex))
                .collect(Collectors.toList());
        ExcelUtils.writeExcel("限时活动抽奖奖励配置", sortedList, LimitedActivityLotteryPrizeConfExcelDto.class, response);
    }

    public Object bondMapIntroduceUpload(MultipartFile file, String activityId) {
        List<CardBattleBondMapIntroduceData> introduceData;
        try {
            introduceData = configExcelUploadFactory.parseConfigExcelDtoList(file, BattleUploadType.BOND_MAP_INTRODUCE, CardBattleBondMapIntroduceData.class);
        } catch (Exception e) {
            log.error("bondMapIntroduceUpload error", e);
            throw new RuntimeException("解析上传文件错误");
        }
        if (CollectionUtils.isEmpty(introduceData)) {
            log.warn("bondMapIntroduceUpload introduceData empty, activityId={}", activityId);
            throw new RuntimeException("解析上传文件,数据为空");
        }
        List<BondActivityExtraConfig.DialogueContent> dialogueContentList = introduceData.stream()
                .map(CardBattleBondMapIntroduceData::toDialogueContent)
                .collect(Collectors.toList());
        log.info("bondMapIntroduceUpload activityId={}, dialogueContentList={}", activityId, dialogueContentList);
        return dialogueContentList;
    }

    public void bondMapIntroduceDownload(HttpServletResponse response, String activityId) {
        BattleActivityConfig battleActivityConfig = battleExploreRepo.getBattleExploreById(activityId);
        if (battleActivityConfig == null) {
            return;
        }
        BondActivityExtraConfig bondActivityExtraConfig = JsonUtils.fromJson(battleActivityConfig.getExtraInfo(), BondActivityExtraConfig.class);
        List<BondActivityExtraConfig.DialogueContent> dialogueContents = new ArrayList<>();
        if (bondActivityExtraConfig != null && CollectionUtils.isNotEmpty(bondActivityExtraConfig.getDialogueContents())) {
            dialogueContents = bondActivityExtraConfig.getDialogueContents();
        } else {
            log.warn("bondMapIntroduceDownload dialogueContents empty, activityId={}, bondActivityExtraConfig={}", activityId, bondActivityExtraConfig);
        }
        ExcelUtils.writeExcel("羁绊地图介绍语配置", dialogueContents, CardBattleBondMapIntroduceData.class, response);
    }
}
