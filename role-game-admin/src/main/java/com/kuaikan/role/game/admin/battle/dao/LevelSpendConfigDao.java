package com.kuaikan.role.game.admin.battle.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardLevelSpendConfig;

/**
 * 卡牌战斗-等级消耗配置
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
public interface LevelSpendConfigDao {

    List<CardBattleCardLevelSpendConfig> selectAll();

    /**
     * 批量插入等级消耗配置
     *
     * @param list 等级消耗配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<CardBattleCardLevelSpendConfig> list);
}
