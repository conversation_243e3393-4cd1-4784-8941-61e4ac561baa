package com.kuaikan.role.game.admin.battle.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleCardPoolConfig;

/**
 * 卡牌战斗-卡池配置
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
public interface CardPoolConfigDao {

  List<CardBattleCardPoolConfig> selectAll();

  int batchInsert(@Param("list") List<CardBattleCardPoolConfig> record);

  void updateInvalidStatusByIds(@Param("list") List<Long> ids);

  List<CardBattleCardPoolConfig> selectAllCardPoolIdList();
}
