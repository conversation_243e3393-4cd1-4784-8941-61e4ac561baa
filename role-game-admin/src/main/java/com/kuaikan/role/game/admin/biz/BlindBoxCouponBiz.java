package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.coupon.CouponProcessorSelector;
import com.kuaikan.role.game.admin.model.param.BaseCouponAddParam;
import com.kuaikan.role.game.admin.model.view.BlindBoxCouponView;
import com.kuaikan.role.game.admin.repository.BlindBoxCouponConfigRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupBlindBoxCouponRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.api.bean.BlindBoxCouponConfig;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.enums.CouponConfigType;
import com.kuaikan.role.game.api.enums.CouponStatusType;
import com.kuaikan.role.game.common.bean.RoleGroupBlindBoxCouponRelation;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;

/**
 * <AUTHOR>
 * @date 2024/12/24 15:29
 */

@Service
@Slf4j
public class BlindBoxCouponBiz {

    @Resource
    private BlindBoxCouponConfigRepository blindBoxCouponConfigRepository;
    @Resource
    private RoleGroupBlindBoxCouponRelationRepository roleGroupBlindBoxCouponRelationRepository;
    @Resource
    private RoleRepository roleRepository;
    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;
    @Resource
    private CouponProcessorSelector couponProcessorSelector;

    public BizResult<PageView<BlindBoxCouponView>> list(int pageNum, int pageSize) {
        final int count = blindBoxCouponConfigRepository.count();
        if (count == 0) {
            return BizResult.success(PageView.empty());
        }
        final int offset = (pageNum - 1) * pageSize;
        final List<BlindBoxCouponConfig> couponConfigs = blindBoxCouponConfigRepository.queryByPage(offset, pageSize);
        if (couponConfigs == null) {
            return BizResult.success(PageView.empty());
        }
        // map<couponId, roleGroupId> -- 特定角色组盲盒券map
        Map<Integer, Integer> couponRoleGroupMap = roleGroupBlindBoxCouponRelationRepository.queryByCouponIds(
                        couponConfigs.stream().map(BlindBoxCouponConfig::getId).collect(Collectors.toSet()))
                .stream()
                .collect(Collectors.toMap(RoleGroupBlindBoxCouponRelation::getCouponId, RoleGroupBlindBoxCouponRelation::getRoleGroupId));
        List<Role> roles = roleRepository.queryAll();
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryAll();
        // map<roleGroupId, roleIds>
        Map<Integer, Set<Integer>> roleGroupRelationMap = roleGroupRelations.stream()
                .collect(Collectors.groupingBy(RoleGroupRelation::getRoleGroupId, Collectors.mapping(RoleGroupRelation::getRoleId, Collectors.toSet())));
        // map<roleId, Role>
        Map<Integer, Role> roleMap = roles.stream().collect(Collectors.toMap(Role::getId, Function.identity()));
        // map<couponId, List<Role>> -- 特定角色组盲盒券对应的角色map
        Map<Integer, List<Role>> couponRolesMap = couponConfigs.stream()
                .filter(config -> couponRoleGroupMap.containsKey(config.getId()))
                .collect(Collectors.toMap(BlindBoxCouponConfig::getId, config -> {
                    Set<Integer> roleIds = roleGroupRelationMap.get(couponRoleGroupMap.get(config.getId()));
                    return roleIds.stream().map(roleMap::get).collect(Collectors.toList());
                }));
        // roles name to view
        List<BlindBoxCouponView> blindBoxCouponViews = couponConfigs.stream()
                .map(couponConfig -> BlindBoxCouponView.valueOf(couponConfig, couponRolesMap.get(couponConfig.getId())))
                .collect(Collectors.toList());
        return BizResult.success(PageView.form(count, blindBoxCouponViews));
    }

    public BizResult<String> blindBoxCouponCreate(MultipartFile file, BaseCouponAddParam param) {
        BizResult<String> result;
        try {
            result = couponProcessorSelector.getCouponProcessor(param).addCouponAndSendMsg(file, param);
        } catch (Exception e) {
            log.error("blindBoxCouponCreate error", e);
            result = BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "新增盲盒券失败");
        }
        String fileName = null;
        if (null != file && !file.isEmpty()) {
            fileName = file.getOriginalFilename();
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.BLIND_BOX_COUPON_ADD_OR_UPDATE)
                .add("fileName", fileName)
                .add("param", JsonUtils.writeValueAsString(param))
                .add("couponType", CouponConfigType.BLIND_BOX.getCode());
        OperateLogUtils.asyncRecord(operation);
        return result;
    }

    public BizResult<Void> publish(int id) {
        BlindBoxCouponConfig blindBoxCouponConfig = blindBoxCouponConfigRepository.selectByPrimaryKey(id);
        if (Objects.isNull(blindBoxCouponConfig)) {
            log.error("blindBoxCouponConfig is not exist, id={}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "盲盒券配置信息不存在");
        }
        if (!blindBoxCouponConfig.getStatus().equals(CouponStatusType.INIT.getCode()) && !blindBoxCouponConfig.getStatus()
                .equals(CouponStatusType.OFFLINE.getCode())) {
            log.error("blindBoxCouponConfig status not offline , id={}, status={}", id, blindBoxCouponConfig.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "盲盒券非已下架或初始状态");
        }
        blindBoxCouponConfig.setStatus(CouponStatusType.ONLINE.getCode());
        blindBoxCouponConfigRepository.update(blindBoxCouponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ONLINE_COUPON)
                .add("couponId", blindBoxCouponConfig.getId())
                .add("couponType", CouponConfigType.BLIND_BOX.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> offline(int id) {
        BlindBoxCouponConfig blindBoxCouponConfig = blindBoxCouponConfigRepository.selectByPrimaryKey(id);
        if (Objects.isNull(blindBoxCouponConfig)) {
            log.error("blindBoxCouponConfig is not exist, id={}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "盲盒券配置信息不存在");
        }
        if (!blindBoxCouponConfig.getStatus().equals(CouponStatusType.ONLINE.getCode())) {
            log.error("blindBoxCouponConfig status not online , id={}, status={}", id, blindBoxCouponConfig.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "盲盒券非上架状态");
        }
        blindBoxCouponConfig.setStatus(CouponStatusType.OFFLINE.getCode());
        blindBoxCouponConfigRepository.update(blindBoxCouponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.OFFLINE_COUPON)
                .add("couponId", blindBoxCouponConfig.getId())
                .add("couponType", CouponConfigType.BLIND_BOX.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> update(int id, int status) {
        BlindBoxCouponConfig blindBoxCouponConfig = blindBoxCouponConfigRepository.selectByPrimaryKey(id);
        blindBoxCouponConfig.setStatus(status);
        blindBoxCouponConfigRepository.update(blindBoxCouponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.BLIND_BOX_COUPON_ADD_OR_UPDATE)
                .add("couponId", blindBoxCouponConfig.getId())
                .add("status", status)
                .add("couponType", CouponConfigType.BLIND_BOX.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }
}
