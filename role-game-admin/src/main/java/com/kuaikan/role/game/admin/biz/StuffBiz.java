package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.model.param.StuffAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.StuffView;
import com.kuaikan.role.game.admin.repository.StuffRepository;
import com.kuaikan.role.game.api.bean.Stuff;

/**
 * <AUTHOR>
 * @version 2024-04-19
 */
@Service
@Slf4j
public class StuffBiz {

    @Resource
    private StuffRepository stuffRepository;

    public BizResult<PageView<StuffView>> list(int pageNum, int pageSize) {
        final int offset = (pageNum - 1) * pageSize;
        final List<Stuff> stuffs = stuffRepository.queryByPage(offset, pageSize);
        List<StuffView> stuffViews = stuffs.stream().map(StuffView::valueOf).collect(Collectors.toList());
        final int count = stuffRepository.count();
        return BizResult.success(PageView.form(count, stuffViews));
    }

    public BizResult<Void> addOrUpdate(StuffAddOrUpdateParam param) {
        if (param.isParamIllegal()) {
            log.error("param illegal, param:{}", param);
            return BizResult.result(RoleGameResponse.PARAM_ILLEGAL);
        }
        Stuff.Config config = new Stuff.Config().setStuffImage(param.getStuffImage())
                .setDecomposeRate(param.getDecomposeRate());
        Operation operation;
        //更新
        if (param.getId() > 0) {
            final Stuff stuff = stuffRepository.selectByPrimaryKey(param.getId());
            if (stuff == null) {
                return BizResult.result(RoleGameResponse.STUFF_NOT_EXIST);
            }
            operation = Operation.of(OperationConstants.OperateSubType.STUFF_UPDATE).add("oldData", JsonUtils.writeValueAsString(stuff));
            stuff.setName(param.getName()).setConfig(config);
            stuffRepository.updateByPrimaryKeySelective(stuff);
            operation.add("newData", JsonUtils.writeValueAsString(stuff));
        } else {
            Stuff stuff = new Stuff().setName(param.getName()).setConfig(config);
            stuffRepository.insert(stuff);
            operation = Operation.of(OperationConstants.OperateSubType.STUFF_ADD).add("newData", JsonUtils.writeValueAsString(stuff));
        }
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

}
