package com.kuaikan.role.game.admin.biz;

import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.model.param.BroadcastConfigParam;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.api.bean.KeyValueConfig;
import com.kuaikan.role.game.common.constant.KeyValueConfigKeys;

@Service
public class BroadcastBiz {

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    public BizResult<Void> saveBroadCastConfig(BroadcastConfigParam param) {

        List<String> broadcastList = param.getBroadcastList();

        if (broadcastList != null && broadcastList.size() > 3) {
            return BizResult.result(ResponseCodeMsg.INVALID_PARAM.getCode(), "广播配置不能超过3条");
        }
        KeyValueConfig dbConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.BROADCAST_CONFIG);
        if (dbConfig == null){
            KeyValueConfig keyValueConfig = new KeyValueConfig();
            keyValueConfig.setKey(KeyValueConfigKeys.BROADCAST_CONFIG);
            keyValueConfig.setValue(JsonUtils.writeValueAsString(param.getBroadcastList()));
            keyValueConfigRepository.insert(keyValueConfig);
        }else {
            dbConfig.setValue(JsonUtils.writeValueAsString(param.getBroadcastList()));
            keyValueConfigRepository.updateByKey(dbConfig);
        }
        return BizResult.success();
    }

    public BizResult<List<String>> getBroadcastConfig() {
        KeyValueConfig keyValueConfig = keyValueConfigRepository.queryByKey(KeyValueConfigKeys.BROADCAST_CONFIG);
        if (keyValueConfig == null){
            return BizResult.success(Collections.emptyList());
        }
        List<String> broadcastList = JsonUtils.findList(keyValueConfig.getValue(), String.class);
        return BizResult.success(broadcastList);
    }
}
