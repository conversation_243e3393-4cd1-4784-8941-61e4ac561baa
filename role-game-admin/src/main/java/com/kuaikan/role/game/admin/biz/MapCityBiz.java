package com.kuaikan.role.game.admin.biz;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.comic.utils.CdnUtil;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.config.Settings;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.component.QiniuComponent;
import com.kuaikan.role.game.admin.converter.CityMapConverter;
import com.kuaikan.role.game.admin.model.excel.ScheduleNumericalExcelData;
import com.kuaikan.role.game.admin.model.param.MapCityAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.MapCityDetailView;
import com.kuaikan.role.game.admin.model.view.MapCityView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.repository.MapBuildingRelationRepository;
import com.kuaikan.role.game.admin.repository.MapCityRepository;
import com.kuaikan.role.game.admin.repository.MapElementRepository;
import com.kuaikan.role.game.admin.template.numerical.city.MapConfigTemplate;
import com.kuaikan.role.game.admin.template.numerical.city.MapConfigTemplateFactory;
import com.kuaikan.role.game.admin.utils.OkHttpUtil;
import com.kuaikan.role.game.admin.utils.excel.listener.ScheduleNumericalExcelDataListener;
import com.kuaikan.role.game.api.bo.ImageInfo;
import com.kuaikan.role.game.api.enums.CityConfigType;
import com.kuaikan.role.game.api.enums.CommonStatus;
import com.kuaikan.role.game.common.bean.ActionAbilityConfig;
import com.kuaikan.role.game.common.bean.CoinConsumeParam;
import com.kuaikan.role.game.common.bean.MapCity;
import com.kuaikan.role.game.common.bean.MapElement;
import com.kuaikan.role.game.common.bean.MapElementConfig;
import com.kuaikan.role.game.common.bean.MapElementPictureConfig;
import com.kuaikan.role.game.common.bean.MapElementSkin;
import com.kuaikan.role.game.common.bean.MapPoint;
import com.kuaikan.role.game.common.bean.MapScheduleConfig;
import com.kuaikan.role.game.common.bean.MapStoryConfig;
import com.kuaikan.role.game.common.bean.PlotBookConfig;
import com.kuaikan.role.game.common.bean.ScheduleConfig;

/**
 * MapCityBiz
 *
 * <AUTHOR>
 * @since 2024-06-19
 */

@Service
@Slf4j
public class MapCityBiz {

    @Resource
    private MapCityRepository mapCityRepository;
    @Resource
    private MapElementRepository mapElementRepository;
    @Resource
    private CityMapConverter cityMapConverter;
    @Resource
    private QiniuComponent qiniuComponent;
    @Resource
    private MapConfigTemplateFactory cityConfigTemplateFactory;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private MapBuildingRelationRepository mapBuildingRelationRepository;
    @Resource
    private MapStoryBiz mapStoryBiz;

    public static final String MAP_CITY_TEMP_FILE_KEY = "map/file/config/%s/";

    public static final String FREE_BUCKET_NAME = Settings.getEnvironment().isProd() ? "kuaikan-data" : "kk-data-stag";

    public BizResult<PageResult<MapCityView>> list(String name,
                                                   String creator,
                                                   Integer status,
                                                   int pageNum,
                                                   int pageSize,
                                                   Integer type) {
        log.info("list name:{}, creator:{}, status:{}, pageNum:{}, pageSize:{}", name, creator, status, pageNum, pageSize);
        final int offset = (pageNum - 1) * pageSize;
        List<MapCity> maps = mapCityRepository.queryByPage(name, creator, status, offset, pageSize, type);
        int total = mapCityRepository.count(name, creator, status, type);
        log.info("list total:{}", total);
        List<MapCityView> mapCityViews = maps.stream().map(mapCity -> MapCityView.valueOf(mapCity, mapElementRepository)).collect(Collectors.toList());

        PageResult<MapCityView> pageResult = new PageResult<>();
        pageResult.setList(mapCityViews);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(total);
        return BizResult.success(pageResult);
    }

    public BizResult<Void> update(int id, int status) {
        log.info("update id:{}, status:{}", id, status);
        Operation operation;
        //校验id是否存在
        MapCity mapCity = mapCityRepository.getById(id);
        log.info("update mapCity:{}", mapCity);
        if (mapCity == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "地图id不存在");
        }
        operation = Operation.of(OperationConstants.OperateSubType.CITY_MAP_UPDATE_STATUS)
                .add("id", id)
                .add("oldStatus", JsonUtils.writeValueAsString(mapCity.getStatus()))
                .add("newStatus", JsonUtils.writeValueAsString(status));
        mapCityRepository.updateStatus(id, status);
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<MapCityDetailView> getById(int id) {
        log.info("getById id:{}", id);
        MapCity mapCity = mapCityRepository.getById(id);
        mapStoryBiz.fillMapStoryConfigList(mapCity);
        log.info("getById mapCity:{}", mapCity);
        MapCityDetailView mapCityDetailView = cityMapConverter.convertToCityMapDetailView(mapCity);
        return BizResult.success(mapCityDetailView);
    }

    public BizResult<?> addOrUpdate(MapCityAddOrUpdateParam param) {
        log.info("addOrUpdate param:{}", param);
        Operation operation;
        String curUser = AuthContext.getCurrentUser().getName();
        String name = param.getName().orElse(null);
        Integer type = param.getType().orElse(1); // 默认还是城市地图
        ImageInfo imageInfo = param.getImageInfo().orElse(null);
        List<MapPoint> initialArea = param.getInitialArea().orElse(null);
        List<List<MapPoint>> blockAreaList = param.getBlockAreaList().orElse(null);
        List<MapPoint> initialRolePositionList = param.getInitialRolePositionList().orElse(null);
        List<MapElementConfig> mapElementConfigList = param.getMapElementConfigList().orElse(null);
        List<List<MapPoint>> trapAreaList = param.getTrapAreaList().orElse(null);
        List<MapScheduleConfig> mapScheduleConfigs = param.getMapScheduleConfigs().orElse(null);
        List<MapElementPictureConfig> mapElementPictureConfigList = param.getMapElementPictureConfigList().orElse(null);

        Integer activityId = param.getActivityId().orElse(null);
        PlotBookConfig plotBookConfig = param.getPlotBookConfig().orElse(null);
        ActionAbilityConfig actionAbilityConfig = param.getActionAbilityConfig().orElse(null);
        ScheduleConfig scheduleConfig = param.getScheduleConfig().orElse(null);
        List<String> backgroundImgList = param.getBackgroundImgList().orElse(new ArrayList<>(0));
        Integer topicId = param.getTopicId().orElse(null);
        List<MapStoryConfig> mapStoryConfigList = param.getMapStoryConfigList().orElse(new ArrayList<>(0));
        List<CoinConsumeParam> coinConsumeParams = param.getCoinConsumeList().orElse(new ArrayList<>(0));

        Map<String, MapCity> map = new HashMap<>();

        MapCity mapCity = null;

        TransactionStatus transaction = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            if (param.getId().isPresent()) {
                // 校验 id 是否存在
                int id = param.getId().orElse(null);
                MapCity oldMapCity = mapCityRepository.getById(id);
                log.info("addOrUpdate oldMapCity:{}", oldMapCity);
                if (oldMapCity == null) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "地图id不存在");
                }
                mapCity = new MapCity().setId(param.getId().get())
                        .setType(type)
                        .setName(name)
                        .setStatus(oldMapCity.getStatus())
                        .setUpdater(curUser)
                        .setConfig(new MapCity.Config().setImageInfo(imageInfo)
                                .setInitialArea(initialArea)
                                .setBlockAreaList(blockAreaList)
                                .setInitialRolePositionList(initialRolePositionList)
                                .setMapElementConfigList(mapElementConfigList)
                                .setTrapAreaList(trapAreaList)
                                .setMapScheduleConfigs(mapScheduleConfigs)
                                .setMapElementPictureConfigList(mapElementPictureConfigList)
                                .setScheduleConfig(scheduleConfig)
                                .setActionAbilityConfig(actionAbilityConfig)
                                .setPlotBookConfig(plotBookConfig)
                                .setActivityJobId(activityId)
                                .setBackgroundImgList(backgroundImgList)
                                .setTopicId(topicId)
                                .setCoinConsumeList(coinConsumeParams));
                fillMapArray(mapCity);
                mapCityRepository.update(mapCity);
                mapCityRepository.getById(param.getId().get());
                operation = Operation.of(OperationConstants.OperateSubType.CITY_MAP_ADD_OR_UPDATE)
                        .add("id", oldMapCity.getId());
            } else {
                mapCity = new MapCity().setName(name)
                        .setType(type)
                        .setCreator(curUser)
                        .setUpdater(curUser)
                        .setStatus(CommonStatus.NOT_ONLINE.getCode())
                        .setConfig(new MapCity.Config().setImageInfo(imageInfo)
                                .setInitialArea(initialArea)
                                .setBlockAreaList(blockAreaList)
                                .setInitialRolePositionList(initialRolePositionList)
                                .setMapElementConfigList(mapElementConfigList)
                                .setTrapAreaList(trapAreaList)
                                .setMapScheduleConfigs(mapScheduleConfigs)
                                .setMapElementPictureConfigList(mapElementPictureConfigList)
                                .setScheduleConfig(scheduleConfig)
                                .setActionAbilityConfig(actionAbilityConfig)
                                .setPlotBookConfig(plotBookConfig)
                                .setActivityJobId(activityId)
                                .setBackgroundImgList(backgroundImgList)
                                .setTopicId(topicId)
                                .setCoinConsumeList(coinConsumeParams));
                fillMapArray(mapCity);
                mapCityRepository.add(mapCity);
                operation = Operation.of(OperationConstants.OperateSubType.CITY_MAP_ADD_OR_UPDATE)
                        .add("id", mapCity.getId());
            }
            // 填充地图故事关系表
            mapStoryBiz.insertMapStoryRelations(mapCity, mapStoryConfigList);

            // 通用地图数值处理
            if (param.getType().isPresent() && param.getType().get() == 2) {
                for (CityConfigType configType : CityConfigType.values()) {
                    String url = "";
                    switch (configType) {
                        case SCHEDULE_NUMERICAL: {
                            if (Objects.isNull(scheduleConfig)) {
                                continue;
                            }
                            url = scheduleConfig.getScheduleNumerical();
                            break;
                        }
                        case SCHEDULE_ACCELERATION: {
                            if (Objects.isNull(scheduleConfig)) {
                                continue;
                            }
                            url = scheduleConfig.getScheduleAcceleration();
                            break;
                        }
                        case ACTION_NUMERICAL: {
                            if (Objects.isNull(actionAbilityConfig)) {
                                continue;
                            }
                            url = actionAbilityConfig.getActionNumerical();
                            break;
                        }
                    }

                    if (StringUtils.isNotBlank(url)) {
                        MapConfigTemplate<?> instance = cityConfigTemplateFactory.getInstance(configType);
                        BizResult<?> bizResult = instance.importNumericalConfig(url, mapCity.getId());
                        if (!bizResult.isSuccess()) {
                            log.info("addOrUpdate id:{} operation:{}", param.getId(), map);
                            transactionManager.rollback(transaction);
                            return bizResult;
                        }
                    }
                }
            }

            log.info("addOrUpdate id:{} operation:{}", param.getId() ,map);
            OperateLogUtils.asyncRecord(operation);
            transactionManager.commit(transaction);
            return BizResult.success();
        } catch (Exception e) {
            log.error("insert or update city_map error, now rollback", e);
            transactionManager.rollback(transaction);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "insert or update city_map error");
        }
    }

    private void fillMapArray(MapCity mapCity) {

        MapCity.Config config = mapCity.getConfig();
        if (config == null || config.getImageInfo() == null) {
            return;
        }
        double compressRatio = 30;
        int widthLength = (int) Math.ceil(config.getImageInfo().getWidth() / compressRatio);
        int heightLength = (int) Math.ceil(config.getImageInfo().getHeight() / compressRatio);
        List<List<MapPoint>> blockAreaList = config.getBlockAreaList();

        List<MapElementConfig> mapElementConfigList = config.getMapElementConfigList();
        List<Integer> mapElementIds = Lists.transform(mapElementConfigList, MapElementConfig::getElementId);
        List<MapElement> mapElementList = mapElementRepository.selectMapElementByIds(mapElementIds);
        List<Area> blockAreaObjects = blockAreaList.stream().map(e -> new Area(e.get(0), e.get(1))).collect(Collectors.toList());

        for (MapElement mapElement : mapElementList) {
            MapElementSkin skin = mapElement.getMapElementSkin();
            if (skin == null || skin.getConfig() == null || CollectionUtils.isEmpty(skin.getConfig().getBlockAreaList())) {
                continue;
            }
            List<List<MapPoint>> skinBlockAreaList = skin.getConfig().getBlockAreaList();
            List<Area> areas = skinBlockAreaList.stream().map(e -> new Area(e.get(0), e.get(1), skin.getConfig().getScaleRatio())).collect(Collectors.toList());
            blockAreaObjects.addAll(areas);
        }

        int[][] mapArray = new int[heightLength][widthLength];
        for (int i = 0; i < heightLength; i++) {
            for (int j = 0; j < widthLength; j++) {
                for (Area area : blockAreaObjects) {
                    boolean isInArea = area.isPointInArea(new MapPoint(j, i), compressRatio);
                    if (isInArea) {
                        mapArray[i][j] = 1;
                        break;
                    }
                }
            }
        }
        config.setMapArray(mapArray);
    }

    public BizResult<?> uploadConfigExcel(MultipartFile file) {
        String fileKey = String.format(MAP_CITY_TEMP_FILE_KEY, System.currentTimeMillis());
        String fileName = file.getOriginalFilename();
        if (StringUtils.isNotBlank(fileName)) {
            String suffix = fileName.substring(fileName.lastIndexOf("."));
            fileKey = fileKey + System.currentTimeMillis() + suffix;
        }
        try {
            qiniuComponent.uploadWithStream(FREE_BUCKET_NAME, file.getBytes(), fileKey);
        } catch (IOException e) {
            log.info("uploadFile error", e);
            return BizResult.result(RoleGameResponse.BAD_REQUEST.getCode(), "上传文件失败");
        }

        String url = CdnUtil.getDefaultDomainWithBackSlash() + fileKey;

        Map<String, String> result = new HashMap<>();
        result.put("url", url);
        result.put("fileKey", fileKey);
        result.put("fileName", fileKey.substring(fileKey.lastIndexOf("/") + 1));

        return BizResult.success(result);
    }

    public BizResult<Collection<Integer>> parseScheduleNumericalExcel(String url) {
        try {
            Set<Integer> set = new HashSet<>();
            List<ScheduleNumericalExcelData> excelDataList = Lists.newArrayList();
            InputStream inputStream = OkHttpUtil.doGet(url);
            EasyExcel.read(inputStream, ScheduleNumericalExcelData.class, new ScheduleNumericalExcelDataListener(excelDataList)).sheet().headRowNumber(2).doRead();
            for (ScheduleNumericalExcelData excelData : excelDataList) {
                String consumeCoins = excelData.getConsumeCoins();
                if (StringUtils.isNotBlank(consumeCoins)) {
                    for (String coinInfo : consumeCoins.split(",")) {
                        String[] split = coinInfo.split("-");
                        if (split.length == 2) {
                            Integer id = Integer.parseInt(split[0]);
                            set.add(id);
                        }
                    }
                }
            }
            return BizResult.success(set);
        } catch (Exception e) {
            log.error("上传文件失败 error", e);
            return BizResult.result(RoleGameResponse.BAD_REQUEST.getCode(), "上传文件失败");
        }
    }

    @Data
    @Accessors(chain = true)
    public static class Area implements Serializable {

        private int x1;
        private int y1;

        private int x2;
        private int y2;

        public Area(MapPoint point1, MapPoint point2) {
            this.x1 = point1.getX();
            this.y1 = point1.getY();
            this.x2 = point2.getX();
            this.y2 = point2.getY();
        }

        public Area(MapPoint point1, MapPoint point2, double scale) {
            this.x1 = (int) (point1.getX() * scale / 100);
            this.y1 = (int) (point1.getY() * scale / 100);
            this.x2 = (int) Math.ceil(point2.getX() * scale / 100);
            this.y2 = (int) Math.ceil(point2.getY() * scale / 100);
        }

        public boolean isPointInArea(MapPoint point, double compressRatio) {
            if (point.getX() >= this.x1 / compressRatio
                    && point.getX() <= this.x2 / compressRatio
                    && point.getY() >= this.y1 / compressRatio
                    && point.getY() <= this.y2 / compressRatio) {
                return true;
            }
            return false;
        }

    }
}
