package com.kuaikan.role.game.admin.biz;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.model.param.FurnitureGroupAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.FurnitureGroupQueryParam;
import com.kuaikan.role.game.admin.model.view.BasicFurnitureGroupView;
import com.kuaikan.role.game.admin.model.view.FurnitureGroupView;
import com.kuaikan.role.game.admin.model.view.FurnitureView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.repository.FurnitureActivityFurnitureGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.FurnitureGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.FurnitureGroupRepository;
import com.kuaikan.role.game.admin.repository.FurnitureGroupRoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRepository;
import com.kuaikan.role.game.admin.repository.RoleRepository;
import com.kuaikan.role.game.api.bean.FurnitureGroup;
import com.kuaikan.role.game.api.bean.FurnitureGroupRelation;
import com.kuaikan.role.game.api.bean.FurnitureGroupRoleGroupRelation;
import com.kuaikan.role.game.api.bean.Role;
import com.kuaikan.role.game.api.bean.RoleGroup;
import com.kuaikan.role.game.api.enums.FurnitureGroupStatus;
import com.kuaikan.role.game.common.bean.RoleGroupRelation;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class FurnitureGroupBiz {

    @Resource
    private FurnitureGroupRepository furnitureGroupRepository;

    @Resource
    private FurnitureGroupRoleGroupRelationRepository furnitureGroupRoleGroupRelationRepository;

    @Resource
    private FurnitureBiz furnitureBiz;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private FurnitureGroupRelationRepository furnitureGroupRelationRepository;

    @Resource
    private RoleGroupRelationRepository roleGroupRelationRepository;

    @Resource
    private RoleGroupRepository roleGroupRepository;

    @Resource
    private FurnitureActivityFurnitureGroupRelationRepository furnitureActivityFurnitureGroupRelationRepository;

    public Boolean checkParams(FurnitureGroupAddOrUpdateParam param) {
        if (!param.getIsCheckParams()) {
            return false;
        }
        if (param.getName() == null) {
            return true;
        }

        return param.getThumbnail() == null;
    }

    /**
     * 套组配置信息• 套组名称，必填• 缩略图，必填，支持png，尺寸待定•
     * 集齐奖励，不添加就没有奖励，添加卡片后台的奖励ID，按奖励配置的规则进行奖励下发•
     * <p>
     * 关联角色组，非必填，如果不填就全部角色组可用，支持多选，选择角色组后，仅选择的角色组可用，此信息不支持修改•
     * 编辑图片区域默认展示套组关联的家具• 编辑元素（参考城市地图的编辑交互）
     * • 编辑元素，支持编辑• 家具的位置• 家具占位格数（与家具数据相通）
     * • 家具遮挡关系坐标（与家具数据相通）• 互动家具的互动剧情触发坐标（与家具数据相通）新增的套组默认排序值是1新增的套组默认是已下架状态
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(FurnitureGroupAddOrUpdateParam param) {
        log.info("addOrUpdate is param : {}", param);
        Operation operation = null; // 初始化操作日志对象
        if (ObjectUtil.isEmpty(param.getId())) {
            if (checkParams(param)) {
                return BizResult.result(RoleGameResponse.CHECK_PARAMS_IS_NOT);

            }
            FurnitureGroup furnitureGroup = FurnitureGroupAddOrUpdateParam.of(param);
            //            furnitureGroup.setCreatedAt(new Date());
            furnitureGroup.setOperator(AuthContext.getCurrentUser().getName());
            int id = furnitureGroupRepository.insert(furnitureGroup);
            log.info("furnitureGroup insert is id : {}", id);
            if (id <= 0) {
                throw new RuntimeException("furnitureGroup insert is error");
            }
            // 查询新增后的完整数据
            FurnitureGroup newFurnitureGroup = furnitureGroupRepository.queryById(id);

            // 构建新增日志（记录 newData 和操作人）
            operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_GROUP_ADD_OR_UPDATE)
                    .add("id", id)
                    .add("newData", JsonUtils.writeValueAsString(newFurnitureGroup));
            setRelations(param, id);
        } else {
            FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(param.getId());
            if (ObjectUtil.isEmpty(furnitureGroup)) {
                return BizResult.result(RoleGameResponse.FURNITURE_GROUP_IS_NULL);
            }
            // 记录旧数据和操作人
            operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_GROUP_ADD_OR_UPDATE)
                    .add("id", param.getId())
                    .add("oldData", JsonUtils.writeValueAsString(furnitureGroup));

            FurnitureGroup furnitureGroupUpdate = FurnitureGroupAddOrUpdateParam.of(param);
            furnitureGroupUpdate.setId(furnitureGroup.getId());
            furnitureGroupUpdate.setOperator(AuthContext.getCurrentUser().getName());
            int furnitureGroupUpdateCount = furnitureGroupRepository.updateByPrimaryKeySelective(furnitureGroupUpdate);
            // 查询更新后的新数据
            FurnitureGroup newFurnitureGroup = furnitureGroupRepository.queryById(param.getId());
            operation.add("newData", JsonUtils.writeValueAsString(newFurnitureGroup));

            log.info("furnitureGroup update count is : {}", furnitureGroupUpdateCount);
            if (furnitureGroupUpdateCount <= 0) {
                log.info("furnitureGroup not update ");
            }
            setRelations(param, furnitureGroup.getId());
        }
        // 异步记录日志
        if (operation != null) {
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();

    }

    private FurnitureGroup.Config getConfig(FurnitureGroupAddOrUpdateParam param, FurnitureGroup furnitureGroup) {
        FurnitureGroup.Config config = furnitureGroup.getConfig();
        if (ObjectUtil.isEmpty(config)) {
            config = new FurnitureGroup.Config();
        }
        config.setAssociateAllRoleGroup(CollectionUtils.isEmpty(param.getAssociatedRoleGroups()));
        return config;
    }

    private void setRelations(FurnitureGroupAddOrUpdateParam param, int id) {
        // 清空之前的关联关系
        furnitureGroupRoleGroupRelationRepository.deleteByFurnitureGroupId(id);
        List<Integer> associatedRoleGroups = param.getAssociatedRoleGroups();
        // 如果角色组为空，则默认关联所有角色组
        if (CollectionUtils.isEmpty(associatedRoleGroups)) {
            List<Integer> allRoleGroupIds = roleGroupRepository.queryAll().stream().map(RoleGroup::getId).collect(Collectors.toList());
            associatedRoleGroups = allRoleGroupIds;
        }
        List<FurnitureGroupRoleGroupRelation> relations = new ArrayList<>();
        for (Integer s : associatedRoleGroups) {
            FurnitureGroupRoleGroupRelation roleGroupRelation = new FurnitureGroupRoleGroupRelation();
            roleGroupRelation.setRoleGroupId(s);
            roleGroupRelation.setFurnitureGroupId(id);
            relations.add(roleGroupRelation);
        }
        if (CollectionUtils.isNotEmpty(relations)) {
            int furnitureGroupRoleGroupRelationBatchSaveCount = furnitureGroupRoleGroupRelationRepository.batchSave(relations);
            log.info("furnitureGroupRoleGroupRelation batchSave count is : {}", furnitureGroupRoleGroupRelationBatchSaveCount);
            if (furnitureGroupRoleGroupRelationBatchSaveCount <= 0) {
                throw new RuntimeException("furnitureGroupRoleGroupRelation batchSave is error");
            }
        }
    }

    public void setParam(FurnitureGroupQueryParam param) {
        // 检查 page 和 pageSize 是否为 null
        if (param.getPage() == null || param.getPageSize() == null) {
            throw new IllegalArgumentException("Page and pageSize cannot be null");
        }
        // 确保 page 大于等于 1
        if (param.getPage() < 1) {
            throw new IllegalArgumentException("Page number must be greater than or equal to 1");
        }

        int offset = param.getPageSize() * (param.getPage() - 1);

        param.setOffset(offset);

        if (param.getOrderColumn() == null) {
            param.setOrderColumn(1);
        }
        if (param.getOrderType() == null) {
            param.setOrderType(param.getOrderColumn() == 1 ? 2 : 1);
        }
    }

    public BizResult<PageResult<FurnitureGroupView>> page(FurnitureGroupQueryParam param) {
        log.info("page is param : {}", param);
        setParam(param);
        List<FurnitureGroup> furnitureGroupList = furnitureGroupRepository.queryByPage(param.getPage(), param.getPageSize(), param.getId(), param.getName(),
                param.getAssociatedRoleGroup(), param.getOperator(), param.getStatus(), param.getOffset(), param.getOrderColumn(), param.getOrderType());
        int totalCount = furnitureGroupRepository.count(param.getPage(), param.getPageSize(), param.getId(), param.getName(), param.getAssociatedRoleGroup(),
                param.getOperator(), param.getStatus(), param.getOffset());
        if (ObjectUtil.isEmpty(furnitureGroupList) || totalCount == 0) {
            return BizResult.success(new PageResult<>());
        }
        List<Integer> furnitureGroupIds = new ArrayList<>();

        List<FurnitureGroupView> furnitureGroupViews = furnitureGroupList.stream().map(furnitureGroup -> {
            furnitureGroupIds.add(furnitureGroup.getId());
            FurnitureGroupView furnitureGroupView = FurnitureGroupView.valueOf(furnitureGroup);
            furnitureGroupView.setFurnitureCount(0);
            furnitureGroupView.setAssociatedRoleGroups(new ArrayList<>());
            return furnitureGroupView;
        }).collect(Collectors.toList());
        log.info("page furnitureGroupViews is : {}", furnitureGroupViews);
        // 1个小屋对应多个角色组
        List<FurnitureGroupRoleGroupRelation> furnitureGroupRoleGroupRelations = furnitureGroupRoleGroupRelationRepository.getByFurnitureGroupIds(
                furnitureGroupIds);
        Map<Integer, List<Integer>> furnitureGroupIdToRoleGroupIdMap = furnitureGroupRoleGroupRelations.stream()
                .collect(Collectors.groupingBy(FurnitureGroupRoleGroupRelation::getFurnitureGroupId,
                        Collectors.mapping(FurnitureGroupRoleGroupRelation::getRoleGroupId, Collectors.toList())));
        List<Integer> roleGroupIds = furnitureGroupRoleGroupRelations.stream()
                .map(FurnitureGroupRoleGroupRelation::getRoleGroupId)
                .collect(Collectors.toList());
        List<RoleGroupRelation> roleGroupRelations = roleGroupRelationRepository.queryByGroupIds(roleGroupIds);
        Map<Integer, String> roleGroupIdToNameMap = new HashMap<>();
        Map<Integer, List<Role>> roleGroupIdToRoleListMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(roleGroupRelations)) {
            List<Role> roles = roleRepository.queryByIds(roleGroupRelations.stream().map(RoleGroupRelation::getRoleId).collect(Collectors.toList()));
            for (RoleGroupRelation roleGroupRelation : roleGroupRelations) {
                String name = roleGroupIdToNameMap.get(roleGroupRelation.getRoleGroupId());
                if (StringUtils.isEmpty(name)) {
                    name = "";
                }
                for (Role role : roles) {
                    if (role.getId() == roleGroupRelation.getRoleId()) {
                        name += role.getName() + ",";
                        break;
                    }
                }
                log.info("page roleGroupIdToNameMap is : {}", roleGroupIdToNameMap);
                
                roleGroupIdToNameMap.put(roleGroupRelation.getRoleGroupId(), name);
            }
            // 去掉最后一个逗号
            roleGroupIdToNameMap.replaceAll((groupId, name) -> StringUtils.removeEnd(name, ","));
            for (RoleGroupRelation roleGroupRelation : roleGroupRelations) {
                int roleGroupId = roleGroupRelation.getRoleGroupId();
                Role role = roles.stream().filter(r -> r.getId() == roleGroupRelation.getRoleId()).findFirst().orElse(null);
                if (role != null) {
                    roleGroupIdToRoleListMap.computeIfAbsent(roleGroupId, k -> new ArrayList<>()).add(role);
                }
            }
        }

        Map<Integer, Integer> countMap = furnitureBiz.getCountByFurnitureGroupIds(furnitureGroupIds);
        furnitureGroupViews.stream().forEach(furnitureGroupView -> {
            if (ObjectUtil.isNotEmpty(countMap)) {
                Integer count = countMap.get(furnitureGroupView.getId());
                if (count != null) {
                    furnitureGroupView.setFurnitureCount(count);
                }
            }
            if (furnitureGroupView.isAssociateAllRoleGroup()) {
                furnitureGroupView.setAssociatedRoleGroups(null);
            } else if (ObjectUtil.isNotEmpty(roleGroupIdToNameMap)) {
                List<Integer> furnitureRoleGroupIds = furnitureGroupIdToRoleGroupIdMap.get(furnitureGroupView.getId());
                if (ObjectUtil.isNotEmpty(furnitureRoleGroupIds)) {
                    String name = furnitureRoleGroupIds.stream().map(roleGroupIdToNameMap::get).collect(Collectors.joining(","));
                    if (ObjectUtil.isNotEmpty(name)) {
                        furnitureGroupView.setAssociatedRoleGroupName(name);
                    }
                    log.info("page furnitureGroupView is : {}", furnitureGroupView);
                }
            }
        });

        PageResult<FurnitureGroupView> pageResult = new PageResult<>();
        pageResult.setList(furnitureGroupViews);
        pageResult.setPageNum(param.getPage());
        pageResult.setPageSize(param.getPageSize());
        pageResult.setTotalCount(totalCount);
        return BizResult.success(pageResult);
    }

    @Transactional
    public BizResult<Void> updateStatus(FurnitureGroupAddOrUpdateParam param) {
        log.info("updateStatus is param : {}", param);

        FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(param.getId());
        if (ObjectUtil.isEmpty(furnitureGroup)) {
            return BizResult.result(RoleGameResponse.FURNITURE_GROUP_IS_NULL);
        }
        int oldStatus = furnitureGroup.getStatus();
        // 更新Spine图片
        if (Objects.nonNull(param.getStatus()) &&
                !param.getStatus().equals(oldStatus) &&
                FurnitureGroupStatus.UP_FOR_LISTING.getCode().equals(param.getStatus())) {
            // 生成spine图片
            if (!furnitureBiz.handleFurnitureGroupAtlas(furnitureGroup.getId())) {
                return BizResult.result(RoleGameResponse.FURNITURE_MERGE_ERROR_CHANGE_STATUS);
            }
        }
        FurnitureGroup furnitureGroupUpdate = new FurnitureGroup();
        furnitureGroupUpdate.setStatus(param.getStatus());
        furnitureGroupUpdate.setId(furnitureGroup.getId());
        furnitureGroupUpdate.setUpdatedAt(new Date());
        furnitureGroupRepository.updateByPrimaryKeySelective(furnitureGroupUpdate);
        // 套组下架,删除家具活动关联套组
        if (FurnitureGroupStatus.IT_HAS_BEEN_TAKEN_OFF_THE_SHELVES.getCode().equals(param.getStatus())) {
            furnitureActivityFurnitureGroupRelationRepository.deleteByFurnitureGroupId(furnitureGroup.getId());
        }
        // 构建状态变更日志
        Operation operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_GROUP_STATUS_UPDATE)
                .add("id", param.getId())
                .add("oldStatus", oldStatus)
                .add("newStatus", param.getStatus());

        // 异步记录日志
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> updateConfig(FurnitureGroupAddOrUpdateParam param) {
        log.info("updateConfig is param : {}", param);
        if (ObjectUtil.isEmpty(param.getConfig())) {
            throw new RuntimeException("furnitureGroup.config is null");
        }
        FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(param.getId());
        if (ObjectUtil.isEmpty(furnitureGroup)) {
            throw new RuntimeException("FurnitureGroup is null");
        }
        FurnitureGroup furnitureGroupUpdate = new FurnitureGroup();
        FurnitureGroup.Config config = new FurnitureGroup.Config();
        BeanUtil.copyProperties(param.getConfig(), config);
        furnitureGroupUpdate.setId(furnitureGroup.getId());
        furnitureGroupUpdate.setConfig(config);
        furnitureGroupUpdate.setUpdatedAt(new Date());
        furnitureGroupRepository.updateByPrimaryKeySelective(furnitureGroupUpdate);
        return BizResult.success();
    }

    public BizResult<FurnitureGroupView> detail(Integer id) {
        log.info("detail is id : {}", id);
        FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(id);
        if (ObjectUtil.isEmpty(furnitureGroup)) {
            throw new RuntimeException("FurnitureGroup is null");
        }
        FurnitureGroupView view = FurnitureGroupView.valueOf(furnitureGroup);
        Map<Integer, Integer> countMap = furnitureBiz.getCountByFurnitureGroupIds(Collections.singletonList(view.getId()));

        if (countMap != null && !countMap.isEmpty()) {
            Integer count = countMap.get(view.getId());
            if (count != null) {
                view.setFurnitureCount(count);
            }
        }
        FurnitureGroup.Config config = furnitureGroup.getConfig();
        if (config != null && config.isAssociateAllRoleGroup()) {
            view.setAssociatedRoleGroups(null);
        } else {
            List<FurnitureGroupRoleGroupRelation> furnitureGroupRoleGroupRelations = furnitureGroupRoleGroupRelationRepository.getByFurnitureGroupIds(
                    Collections.singletonList(view.getId()));
            if (ObjectUtil.isNotEmpty(furnitureGroupRoleGroupRelations)) {
                List<Integer> roleGroupIds = furnitureGroupRoleGroupRelations.stream()
                        .map(FurnitureGroupRoleGroupRelation::getRoleGroupId)
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(roleGroupIds)) {
                    view.setAssociatedRoleGroups(roleGroupIds);
                }
            }
        }
        setFurnitureList(view);
        log.info("detail is view : {}, id:{}", view, id);
        return BizResult.success(view);
    }

    private void setFurnitureList(FurnitureGroupView view) {
        try {
            List<FurnitureGroupRelation> furnitureGroupRelationList = furnitureGroupRelationRepository.getFurnitureGroupRelationByFurnitureGroupId(
                    view.getId());

            List<Integer> furnitureIds = furnitureGroupRelationList.stream()
                    .filter(furnitureGroupRelation -> furnitureGroupRelation.getFurnitureId() != null)
                    .map(FurnitureGroupRelation::getFurnitureId)
                    .collect(Collectors.toList());

            if (ObjectUtil.isNotEmpty(furnitureIds)) {
                BizResult<List<FurnitureView>> batchDetail = furnitureBiz.batchDetail(furnitureIds);
                if (batchDetail.isSuccess()) {
                    view.setFurnitureList(batchDetail.getData());
                }
            }
        } catch (Exception e) {
            log.error("setFurnitureList is error", e);
        }
    }

    public BizResult<List<BasicFurnitureGroupView>> getBasicList() {
        Map<Integer, List<Integer>> furnitureGroupRoleGroupRelationMap = furnitureGroupRoleGroupRelationRepository
                .queryRelationMap();
        List<BasicFurnitureGroupView> basicFurnitureGroupViews = furnitureGroupRepository.queryAll()
                .stream()
                .map(furnitureGroup -> BasicFurnitureGroupView.valueOf(furnitureGroup,
                        furnitureGroupRoleGroupRelationMap))
                .collect(Collectors.toList());
        return BizResult.success(basicFurnitureGroupViews);
    }

    public BizResult<Void> triggerMergeImage(Integer furnitureGroupId) {

        new Thread(() -> {
            List<FurnitureGroup> list = new ArrayList<>();
            if (Objects.nonNull(furnitureGroupId)) {
                FurnitureGroup furnitureGroup = furnitureGroupRepository.queryById(furnitureGroupId);
                if (ObjectUtil.isNotEmpty(furnitureGroup)) {
                    list.add(furnitureGroup);
                }
            } else {
                list.addAll(furnitureGroupRepository.queryAll());
            }

            for (FurnitureGroup furnitureGroup : list) {
                if ( furnitureBiz.handleFurnitureGroupAtlas(furnitureGroup.getId()) ) {
                    log.info("triggerMergeImage furnitureGroupId : {}", furnitureGroup.getId());
                } else {
                    log.error("triggerMergeImage is error furnitureGroupId : {}", furnitureGroup.getId());
                }
            }
        }).start();

        return BizResult.success();
    }
}
