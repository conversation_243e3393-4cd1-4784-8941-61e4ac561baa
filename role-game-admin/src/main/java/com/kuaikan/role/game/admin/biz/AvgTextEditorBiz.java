package com.kuaikan.role.game.admin.biz;

import static com.kuaikan.role.game.api.bean.AvgChapter.INSERT_BACK;
import static com.kuaikan.role.game.api.bean.AvgChapter.Text.Effect.effectToString;
import static com.kuaikan.role.game.api.bean.AvgChapter.Text.PlayConfig.playConfigToString;
import static com.kuaikan.role.game.api.bean.AvgChapter.Text.bgSetToString;
import static com.kuaikan.role.game.api.bean.AvgChapter.Text.buildTextLoopConfig;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.model.RpcResult;
import com.kuaikan.common.tools.lang.convert.SafeConverter;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.AvgChapterComponent;
import com.kuaikan.role.game.admin.repository.AvgFileAliasRelationRepository;
import com.kuaikan.role.game.admin.repository.AvgFileChapterRelationRepository;
import com.kuaikan.role.game.admin.repository.AvgRepository;
import com.kuaikan.role.game.api.bean.AvgChapter;
import com.kuaikan.role.game.api.bean.AvgChapter.Text;
import com.kuaikan.role.game.api.bean.AvgChapter.Text.Effect;
import com.kuaikan.role.game.api.bean.AvgChapter.Text.PlayConfig;
import com.kuaikan.role.game.api.bean.AvgDir;
import com.kuaikan.role.game.api.bean.AvgHotZone;
import com.kuaikan.role.game.api.bean.AvgOriginFile;
import com.kuaikan.role.game.api.bean.AvgProject;
import com.kuaikan.role.game.api.bo.AvgChapterTextNode;
import com.kuaikan.role.game.api.enums.AvgChapterFileRefreshStatus;
import com.kuaikan.role.game.api.enums.AvgChapterType;
import com.kuaikan.role.game.api.enums.AvgFileType;
import com.kuaikan.role.game.api.enums.AvgTextLoopType;
import com.kuaikan.role.game.api.enums.AvgTextRatioType;
import com.kuaikan.role.game.api.enums.AvgTextUnlockCondition;
import com.kuaikan.role.game.api.enums.AvgTextValueCondition;
import com.kuaikan.role.game.api.enums.CharType;
import com.kuaikan.role.game.api.enums.EffectType;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextBatchUpdateParam;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextUpdateParam;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextUpdateParam.LoopConfig;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextUpdateParam.LoopConfigNextId;
import com.kuaikan.role.game.api.rpc.param.avg.AvgTextUpdateParam.NextId;
import com.kuaikan.role.game.api.rpc.result.StoryTree;
import com.kuaikan.role.game.api.rpc.result.avg.AvgChapterTextPage;
import com.kuaikan.role.game.api.rpc.result.avg.AvgTextVO;
import com.kuaikan.role.game.api.service.AvgService;
import com.kuaikan.role.game.api.util.AvgOrderUtils;

@Slf4j
@Service
public class AvgTextEditorBiz {

    private static final List<String> BATCH_RENAME_TYPE_LIST = Arrays.asList("bg", "character", "head", "bgm", "sound", "cv");

    private static final String sharpSplit = "##";

    @Resource
    private AvgRepository avgRepository;
    @Resource
    private AvgFileChapterRelationRepository avgFileChapterRelationRepository;
    @Resource
    private AvgFileAliasRelationRepository avgFileAliasRelationRepository;
    @Resource
    private AvgChapterComponent avgChapterComponent;
    @Resource
    private AvgService avgService;

    /**
     * 保存章节文本数据，同时更新章节的资源映射关系
     *
     * @param param 前端传入的章节文本更新参数，通常是单条文本内容的修改请求
     * @return 章节ID
     */
    @Transactional(rollbackFor = Exception.class)
    public BizResult<Integer> saveText(AvgTextUpdateParam param) {
        if (Objects.isNull(param)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "传参为空");
        }
        //采用数据库字段进行一一存储，前端根据分类进行部分字段的存储调用，
        // 后段兼容原始逻辑做存储处理，存储值可空，不做原始逻辑校验(不需要完全照抄)。
        Integer chapterId = param.getChapterId();
        if (Objects.isNull(chapterId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落id不能为空");
        }
        String textId = param.getTextId();
        if (StringUtils.isBlank(textId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文本id不能为空");
        }

        Integer insertChapterNum = null;
        String insertChapter = param.getInsertChapter();
        if (StringUtils.isNotBlank(insertChapter)) {
            insertChapterNum = SafeConverter.toInt(insertChapter);
            if (insertChapterNum <= 0) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "中插片段id有误");
            }
        }

        Integer nextChapterNum = null;
        String nextChapter = param.getNextChapter();
        if (StringUtils.isNotBlank(nextChapter)) {
            nextChapterNum = SafeConverter.toInt(nextChapter);
            if (nextChapterNum <= 0) {
                return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "后接片段id有误");
            }
        }

        // 查询当前章节对象，用于验证，并之后获取当前章节的UI风格id和现有文本列表
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        List<Text> textList = dbAvgChapter.getTextList();
        if (CollectionUtils.isEmpty(textList)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文本行为空，请先新增文本行");
        }
        if (textList.stream().map(Text::getTextId).noneMatch(textId::equals)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), textId + " 文本行不存在，请先新增文本行");
        }
        if (textList.stream().map(Text::getNextIds).noneMatch(CollectionUtils::isEmpty)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "该段落不存在空的后接id文本,请先添加新的空文本行后再重试");
        }

        // 循环替换旧的文本行,并保证顺序
        Text text = null;
        int oldIndex = -1;
        Text oldText = textList.stream().filter(t -> t.getTextId().equals(textId)).findFirst().orElse(null);
        if (oldText != null) {
            oldIndex = textList.indexOf(oldText);
            text = oldText;
        }
        text = AvgTextUpdateParam.paramToText(param, text);

        // 业务校验：文本对象字段合法性校验，防止不合规数据
        List<String> errors = verifySingleText(text, chapterId, textList);
        if (CollectionUtils.isNotEmpty(errors)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), String.join("\r", errors));
        }

        if (oldIndex >= 0) {
            textList.set(oldIndex, text);
        } else {
            textList.add(text);
        }
        dbAvgChapter.setTextList(textList);
        dbAvgChapter.setTextContentMd5(dbAvgChapter.getTextContentMd5());
        dbAvgChapter.setRefreshFile(AvgChapterFileRefreshStatus.WAIT_REFRESH.getCode());
        avgRepository.updateAvgChapter(dbAvgChapter);
        avgChapterComponent.updateChapterResourceRelations(dbAvgChapter);
        if (param.getTopicId() == null) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_EDITOR_SAVE_TEXT)
                    .add("textId", text.getTextId())
                    .add("oldText", oldText)
                    .add("newText", text);
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success(chapterId);
    }

    /**
     * 数据合法性校验
     *
     * @param chapterId 本段落id
     * @param text      前端文本传参
     * @return 校验出的错误集合
     */
    private List<String> verifySingleText(Text text, Integer chapterId, List<Text> textList) {
        List<String> errors = new ArrayList<>();
        String hotZone = text.getHotZone();
        if (StringUtils.isNotBlank(hotZone)) {
            //点触热区数据存在性检测
            AvgHotZone avgHotZone = avgRepository.queryAvgHotZoneByName(hotZone);
            int nextIdNum = CollectionUtils.isEmpty(text.getNextIds()) ? 0 : text.getNextIds().size();
            if (Objects.isNull(avgHotZone)) {
                errors.add("点触热区不存在");
            } else {
                int hotZoneNum = Optional.ofNullable(avgHotZone.getConfig()).map(config -> config.getHotZoneV2().size()).orElse(0);
                if (hotZoneNum != nextIdNum) {
                    errors.add("热区个数和选项个数不一致");
                }
            }
        }

        // 背景格式校验
        String bg = text.getBg();
        if (StringUtils.isNotBlank(bg) && !(bg.endsWith(AvgChapter.PNG) || bg.endsWith(AvgChapter.JPG) || bg.endsWith(AvgChapter.MP4))) {
            errors.add("背景格式不正确，应以.png/.jpg/.mp4结尾");
        }

        // 背景动效校验
        Effect bgEffect = text.getBgEffectV2();
        PlayConfig bgEffectPlayConfig = text.getBgEffectPlayConfig();
        if (bgEffect != null && bgEffectPlayConfig != null) {
            if (bgEffect.getType() >= EffectType.PUSH_IN.getCode()
                    && bgEffect.getType() <= EffectType.BACKGROUND_SHAKE.getCode()
                    && bgEffectPlayConfig.getRatio() != null
                    && bgEffectPlayConfig.getRatio() == AvgTextRatioType.FULL_SCREEN.getCode()) {
                errors.add("背景动效比例配置不正确");
            }
        }

        // 立绘格式校验
        String character = text.getCharacter();
        if (StringUtils.isNotBlank(character) && CharType.getCharType(character) == CharType.UNKNOWN) {
            errors.add("立绘格式不正确");
        }
        if (StringUtils.isNotBlank(character) && text.getFace() == null) {
            text.setFace(1);
        }

        // 头像格式校验
        String head = text.getHead();
        if (StringUtils.isNotBlank(head) && !head.endsWith(AvgChapter.PNG)) {
            errors.add("头像图片格式不正确，应以.png结尾");
        }
        if (StringUtils.isNotBlank(head) && text.getPosition() == null) {
            text.setPosition(1);
        }

        // bgm格式
        String bgm = text.getBgm();
        if (StringUtils.isNotBlank(bgm)) {
            if (!bgm.endsWith(AvgChapter.MP3) && !bgm.equals(AvgChapter.STOP_OPTIONS)) {
                errors.add("背景音乐格式不正确，应以.mp3结尾或为停止选项标识");
            }
        }

        // 音效格式
        String sound = text.getSound();
        if (StringUtils.isNotBlank(sound)) {
            if (!sound.endsWith(AvgChapter.MP3) && !sound.equals(AvgChapter.STOP_OPTIONS)) {
                errors.add("音效格式不正确，应以.mp3结尾或为停止选项标识");
            }
        }

        // CV格式
        String cv = text.getCv();
        if (StringUtils.isNotBlank(cv) && !cv.endsWith(AvgChapter.MP3)) {
            errors.add("CV语音格式不正确，应以.mp3结尾");
        }

        // 视频格式
        String video = text.getVideo();
        if (StringUtils.isNotBlank(video) && !video.endsWith(AvgChapter.MP4)) {
            errors.add("视频格式不正确，应以.mp4结尾");
        }

        // 视频语音格式
        String videoVoice = text.getVideoVoice();
        if (StringUtils.isNotBlank(videoVoice) && !videoVoice.endsWith(AvgChapter.MP3)) {
            errors.add("视频语音格式不正确，应以.mp3结尾");
        }

        // 物品格式与移动格式校验
        String stuff = text.getStuff();
        String stuffMove = text.getStuffMove();
        if (StringUtils.isNotBlank(stuff)) {
            if (!(stuff.endsWith(AvgChapter.PNG) || stuff.endsWith(AvgChapter.JPG) || stuff.endsWith(AvgChapter.WEBP) || stuff.endsWith(AvgChapter.GIF))) {
                errors.add("物品格式不正确，应以.png/.jpg/.webp/.gif结尾");
            }
        }
        if (StringUtils.isNotBlank(stuffMove) && StringUtils.isBlank(stuff)) {
            errors.add("物品移动存在，但物品格式为空");
        }

        // 对话选项格式校验，只做单条格式检查，数量不校验
        String dialogue = text.getDialogue();
        boolean hideOptions = AvgChapter.HIDE_OPTIONS.equals(dialogue);
        List<String> nextIds = text.getNextIds();
        if (StringUtils.isNotBlank(dialogue) && dialogue.contains("\\|")) {
            String[] split = dialogue.split("\\|");
            if (CollectionUtils.isEmpty(nextIds)) {
                errors.add("对话选项和后接id个数不匹配");
            }
            if (split.length > 15) {
                errors.add("行对话选项不能超过15个");
            }
            if (Objects.equals(split.length, nextIds.size())) {
                errors.add("对话选项和后接id个数不匹配");
            }
        }

        if (StringUtils.isNotBlank(dialogue)) {
            if (dialogue.contains("|")) {
                String[] dialogues = dialogue.split("\\|");
                if (dialogues.length > 15) {
                    errors.add("对话选项不能超过15个");
                }
                for (String d : dialogues) {
                    String order = AvgOrderUtils.findTextOrder(d);
                    if (StringUtils.isNotBlank(order)) {
                        if (!AvgOrderUtils.EXIST_TEXT_ORDER_REGEX.contains(order)) {
                            errors.add("对话选项里的指令" + d + "不支持");
                        }
                    }
                }
            }
        }

        // 仅校验nextIds中指令存在性和格式，不校验是否存在于上传队列中
        if (CollectionUtils.isNotEmpty(nextIds)) {
            for (String nextId : nextIds) {
                if (StringUtils.isBlank(nextId)) {
                    continue;
                }
                String order = AvgOrderUtils.findTextOrder(nextId);
                if (StringUtils.isNotBlank(order)) {
                    if (!AvgOrderUtils.EXIST_TEXT_ORDER_REGEX.contains(order)) {
                        errors.add("后接ID中的指令" + nextId + "不支持");
                    }
                    if (hideOptions) {
                        errors.add("文本为隐藏选项，不支持后接指令");
                    }
                }
            }
        }

        // 解锁条件格式校验
        String unlockStr = text.getUnlock();
        if (StringUtils.isNotBlank(unlockStr)) {
            String[] unlocks = unlockStr.split("\\|");
            for (String unlock : unlocks) {
                if (StringUtils.isBlank(unlock)) {
                    continue;
                }
                int unlockCode = NumberUtils.toInt(unlock, -1);
                AvgTextUnlockCondition condition = AvgTextUnlockCondition.getByCode(unlockCode);
                if (condition == null) {
                    errors.add("解锁条件" + unlock + "不支持");
                }
            }
        }

        // 数值变化格式校验
        String valueStr = text.getValue();
        if (StringUtils.isNotBlank(valueStr)) {
            String[] values = valueStr.split("\\|");
            for (String val : values) {
                if (StringUtils.isBlank(val)) {
                    continue;
                }
                int valCode = NumberUtils.toInt(val, -1);
                AvgTextValueCondition condition = AvgTextValueCondition.getByCode(valCode);
                if (condition == null) {
                    errors.add("数值变化" + val + "不支持");
                }
            }
        }

        // 循环配置校验
        Integer textLoopType = text.getTextLoopType();
        if (textLoopType != null) {
            try {
                AvgTextLoopType loopType = AvgTextLoopType.getByCode(textLoopType);
                if (loopType == AvgTextLoopType.LOOPSTART) {
                    // 循环开始前，需要校验前序的循环配置是否完整
                    List<AvgChapter.Text> preTexts = StoryTree.findPreNodesWithTextModel(textList, text);
                    try {
                        if (preTexts.size() > 0) {
                            AvgChapterTextNode avgChapterTextNode = AvgChapterTextNode.buildTreeFromAvgChapter(preTexts);
                            avgChapterTextNode.validateLoopNodes();
                        }
                    } catch (Exception ex) {
                        errors.add("循环配置不完整或不正确: " + ex.getMessage());
                    }
                    int count = text.getTextLoopCount();
                    if (count < 1) {
                        errors.add("循环次数不正确");
                    }
                    if (StringUtils.isEmpty(text.getOnetime())) {
                        errors.add("一次性选项配置缺失");
                    } else {
                        String oneTimeStr = text.getOnetime();
                        if (!oneTimeStr.contains("|")) {
                            errors.add("一次性选项格式不正确，应包含'|'");
                        } else {
                            String[] onetimeArr = oneTimeStr.split("\\|");
                            if (onetimeArr.length != nextIds.size()) {
                                errors.add("一次性选项数量需要与后接id数量一致");
                            }
                        }
                    }
                } else if (loopType == AvgTextLoopType.LOOPEND) {
                    if (StringUtils.isEmpty(text.getLoopNextTextId())) {
                        errors.add("循环结束配置缺失下一个文本ID");
                    } else {
                        Text loopStartText = textList.stream().filter(t -> t.getTextId().equals(text.getLoopNextTextId())).findFirst().orElse(null);
                        if (loopStartText == null || !loopStartText.isTextLoop() || loopStartText.getTextLoopType() != AvgTextLoopType.LOOPSTART.getCode()) {
                            errors.add("循环条件配置不符合后接ID规则");
                        }
                    }
                } else {
                    errors.add("循环类型不正确");
                }
            } catch (IllegalArgumentException e) {
                errors.add("循环类型不正确");
            }
        }

        String nameCardStr = text.getNameCardStr();
        if (StringUtils.isNotBlank(nameCardStr) && nameCardStr.contains("0")) {
            errors.add("名字框位置配置不正确");
        }

        // nextChapter只做格式校验，不判定是否存在
        Integer nextChapter = text.getNextChapter();
        if (nextChapter != null && nextChapter <= 0) {
            errors.add("后接段落ID格式不正确");
        }
        if (Objects.nonNull(nextChapter)) {
            if (Objects.equals(nextChapter, chapterId)) {
                errors.add("后接段落id不可为当前段落id");
            }
            // 后接段落id合法性校验,不能保存不存在的段落id,且不等于当前
            AvgChapter exists = avgRepository.queryAvgChapterByChapterId(nextChapter);
            if (Objects.isNull(exists)) {
                errors.add("后接段落ID不存在");
            } else if (!Objects.equals(exists.getType(), AvgChapterType.TEXT.getCode())) {
                errors.add("后接段落ID不合法");
            }
        }

        // insertChapter只做格式校验
        Integer insertChapter = text.getInsertChapter();
        if (insertChapter != null && insertChapter <= 0) {
            errors.add("中插段落ID格式不正确");
        }

        if (Objects.nonNull(insertChapter)) {
            if (Objects.equals(insertChapter, chapterId)) {
                errors.add("中插段落id不可为当前段落id");
            }
            // 中插片段的id合法性校验
            AvgChapter exists = avgRepository.queryAvgChapterByChapterId(insertChapter);
            if (Objects.isNull(exists)) {
                errors.add("中插段落ID不存在");
            } else if (!Objects.equals(exists.getType(), AvgChapterType.INSERT_CHAPTER.getCode())) {
                errors.add("中插段落ID不合法");
            }
        }
        return errors;
    }

    /**
     * 根据文本id查询文本配置
     *
     * @param chapterId 段落id
     * @param textId    文本id
     * @return 文本行配置
     */
    public BizResult<AvgTextVO> findTextByTextId(Integer chapterId, String textId) {
        if (Objects.isNull(chapterId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落id不能为空");
        }
        if (StringUtils.isBlank(textId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文本id不能为空");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        // 查询文本配置
        List<Text> textList = dbAvgChapter.getTextList();
        if (CollectionUtils.isEmpty(textList)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "当前段落无文本");
        }
        Optional<Text> first = textList.stream().filter(text -> text.getTextId().equals(textId)).findFirst();
        if (!first.isPresent()) {
            //不存在要查的textId则返回未找到,可能是中插片段textId,直接返回空
            return BizResult.success();
        }
        AvgTextVO textVO = new AvgTextVO();
        Text text = first.get();
        BeanUtils.copyProperties(text, textVO);
        // 类型转换等兼容字段处理
        setNextIdList(text, textVO);
        textVO.setVideoSet(playConfigToString(text.getVideoPlayConfig()));
        textVO.setBgSet(bgSetToString(text));
        textVO.setCharEffect(effectToString(text.getCharEffectV2()));
        textVO.setInsertChapterBack(buildInsertChapterBackInt(text.getInsertChapterBack()));
        textVO.setInsertChapter(text.getInsertChapter() == null ? null : String.valueOf(text.getInsertChapter()));
        textVO.setNextChapter(text.getNextChapter() == null ? null : String.valueOf(text.getNextChapter()));
        textVO.setChapterId(chapterId);
        textVO.setChapterType(dbAvgChapter.getType());
        textVO.setSoundStop(buildSoundStop(text));
        return BizResult.success(textVO);
    }

    /**
     * 后接id类型转换
     */
    private static void setNextIdList(Text text, AvgTextVO textVO) {
        List<String> nextIds = text.getNextIds();
        if (CollectionUtils.isNotEmpty(nextIds)) {
            String value = text.getValue();
            String[] valueArray = null;
            if (StringUtils.isNotBlank(value)) {
                valueArray = value.split("\\|");
            }
            String unlock = text.getUnlock();
            String[] unlockArray = null;
            if (StringUtils.isNotBlank(unlock)) {
                unlockArray = unlock.split("\\|");
            }
            String onetime = text.getOnetime();
            String[] oneTimeArray = null;
            if (StringUtils.isNotBlank(onetime)) {
                oneTimeArray = onetime.split("\\|");
            }
            List<NextId> nextIdList = new ArrayList<>();
            List<LoopConfigNextId> loopConfigNextIdList = new ArrayList<>();
            for (int i = 0; i < nextIds.size(); i++) {
                NextId nextIdObj = new NextId();
                nextIdObj.setTextId(nextIds.get(i));
                if (Objects.nonNull(valueArray)) {
                    nextIdObj.setValue(Integer.parseInt(valueArray[i]));
                }
                if (Objects.nonNull(unlockArray)) {
                    nextIdObj.setUnlock(Integer.parseInt(unlockArray[i]));
                }
                nextIdList.add(nextIdObj);

                LoopConfigNextId loopConfigNextId = new LoopConfigNextId();
                loopConfigNextId.setTextId(nextIds.get(i));
                if (Objects.nonNull(oneTimeArray)) {
                    loopConfigNextId.setOneTime(Integer.parseInt(oneTimeArray[i]));
                }
                loopConfigNextIdList.add(loopConfigNextId);
            }
            textVO.setNextIds(nextIdList);
            if (text.isTextLoop()) {
                //如果前端保存时不传loopConfig,此处就为false,所以用textLoop=true来判断是否需要赋值loopConfig
                LoopConfig loopConfig = textVO.getLoopConfig();
                if (Objects.isNull(loopConfig)) {
                    loopConfig = new LoopConfig();
                }
                loopConfig.setTime(text.getTextLoopCount());
                loopConfig.setNextIds(loopConfigNextIdList);
                loopConfig.setStartTextId(text.getLoopNextTextId());
                loopConfig.setIsBreak(text.isLoopBreak());
                textVO.setLoopConfig(loopConfig);
            }
        }
    }

    private int buildSoundStop(Text text) {
        String sound = text.getSound();
        if (StringUtils.isBlank(sound)) {
            return 0;
        }
        return sound.equals(AvgChapter.STOP_OPTIONS) ? 1 : 0;
    }

    /**
     * 根据段落id和文本id,新增空白文本行
     *
     * @param param 段落id+文本id
     * @return 文本id
     */
    public BizResult<String> addBlankText(AvgTextUpdateParam param) {
        Integer chapterId = param.getChapterId();
        String textId = param.getTextId();

        if (Objects.isNull(chapterId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落id不能为空");
        }
        if (StringUtils.isBlank(textId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文本id不能为空");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        //读取旧的文本集,增加新的空文本行,然后再更新段落AvgChapter
        List<Text> textList = dbAvgChapter.getTextList();
        if (CollectionUtils.isEmpty(textList)) {
            //防止空指针
            textList = new ArrayList<>();
        }
        if (textList.stream().map(Text::getTextId).anyMatch(id -> id.equals(textId))) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文本id重复: " + textId);
        }
        Text newText = new Text();
        newText.setTextId(textId);
        textList.add(newText);
        dbAvgChapter.setTextList(textList);
        dbAvgChapter.setTextContentMd5(dbAvgChapter.getTextContentMd5());//md5需要单独更新
        avgRepository.updateAvgChapter(dbAvgChapter);
        if (param.getTopicId() == null) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_EDITOR_ADD_TEXT)
                    .add("textId", textId);
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success(textId);
    }

    /**
     * 根据段落id分页查询对应文本配置
     *
     * @param chapterId 段落id
     * @param textId 文本id,定位到某个text的页
     * @param roleName  角色名称,非必传,如果传递则会根据角色名称进行精准匹配过滤
     * @param pageNum   页数,默认1,传-1代表不分页
     * @param pageSize  条数,默认20
     * @param startNum  截取开始行数,不分页时才处理
     * @param endNum    截取终止行数,不分页时才处理
     */
    public BizResult<AvgChapterTextPage> queryPageByChapterId(Integer chapterId, String textId, Integer pageNum, Integer pageSize, String roleName,
                                                              Integer startNum, Integer endNum) {
        if (Objects.isNull(chapterId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落id不能为空");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        List<Text> textList = dbAvgChapter.getTextList();
        if (CollectionUtils.isEmpty(textList)) {
            return BizResult.success(new AvgChapterTextPage(0, Collections.emptyList(), pageNum));
        }
        //总条数
        int totalSize = textList.size();
        List<Text> subList = textList;
        if (pageNum > 0 && pageSize > 0) {
            //如果根据textId查询，需要跳转到textId所在的页
            if (StringUtils.isNotBlank(textId)) {
                int textIndex = textList.stream().map(Text::getTextId).collect(Collectors.toList()).indexOf(textId);
                int endPageNum = pageSize;
                while (textIndex + 1 > endPageNum) {
                    endPageNum += pageSize;
                }
                subList = textList.subList(endPageNum - pageSize, Math.min(endPageNum, totalSize));
                pageNum = (textIndex / pageSize) + 1; // 计算页码
            } else {
                pageNum = Math.max(1, pageNum); // 页码不能小于1
                int fromIndex = (pageNum - 1) * pageSize;
                if (fromIndex >= totalSize) {
                    return BizResult.success(new AvgChapterTextPage(0, Collections.emptyList(), pageNum));
                }
                int toIndex = fromIndex + pageSize;
                if (toIndex > totalSize) {
                    toIndex = totalSize;
                }
                subList = textList.subList(fromIndex, toIndex);
            }
        } else if (Objects.nonNull(startNum) && Objects.nonNull(endNum)) {
            //pageNum==-1时,按照行数进行截取处理
            if (startNum < 0 || startNum > textList.size()) {
                return BizResult.success(new AvgChapterTextPage(totalSize, Collections.emptyList(), pageNum));
            }

            int size = textList.size();

            // 限制startNum和end的范围到 [0, size]
            startNum = Math.max(0, Math.min(startNum, size - 1));
            endNum = Math.max(0, Math.min(endNum, size));

            if (startNum > endNum) {
                // 如果 startNum 比 endNum 大，则交换，或者直接返回空，视需求而定，我这里选择交换保持逻辑一致
                int temp = startNum;
                startNum = endNum;
                endNum = temp;
            }

            if (startNum.equals(endNum)) {
                // 返回单个元素的列表
                startNum = Math.max(startNum, 1);
                subList = Collections.singletonList(textList.get(startNum - 1));
            } else {
                // 如果startNum < end，返回子列表
                startNum = Math.max(startNum, 1);
                subList = textList.subList(startNum - 1, endNum);
            }
        }

        List<AvgChapterTextPage.AvgChapterTextListVO> collect = subList.stream().map(text -> {
            if (StringUtils.isNotBlank(roleName)) {
                //如果需要对角色进行过滤,进行角色匹配
                String dialogue = text.getDialogue();
                if (StringUtils.isBlank(dialogue)) {
                    //不存在对话文本的内容要跳过
                    return null;
                }
                if (!dialogue.contains(sharpSplit)) {
                    //如果该行不含角色则跳过
                    return null;
                }
                String[] split = dialogue.split(sharpSplit);
                if (StringUtils.isBlank(split[0])) {
                    // 不含有角色的跳过
                    return null;
                }
                if (!roleName.equals(split[0])) {
                    // 不是该角色的跳过
                    return null;
                }
            }
            AvgChapterTextPage.AvgChapterTextListVO textVO = new AvgChapterTextPage.AvgChapterTextListVO();
            BeanUtils.copyProperties(text, textVO);
            // 类型转换等兼容字段处理
            textVO.setNameCard(StringUtils.isBlank(text.getNameCardStr()) ? null : text.getNameCardStr());
            textVO.setNextIds(StringUtils.join(text.getNextIds(), "|"));
            textVO.setTextLoopConfig(buildTextLoopConfig(text));
            textVO.setBgSet(bgSetToString(text));
            textVO.setCharEffect(effectToString(text.getCharEffectV2()));
            textVO.setVideoSet(playConfigToString(text.getVideoPlayConfig()));
            return textVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return BizResult.success(new AvgChapterTextPage(totalSize, collect, pageNum));
    }

    /**
     * 根据段落id和文本id,批量替换文本配置
     *
     * @param param 文本批量替换字段传参
     * @return 替换后的段落id
     */
    public BizResult<Integer> updateBatchByTextIds(AvgTextBatchUpdateParam param) {
        Integer chapterId = param.getChapterId();
        List<String> textIds = param.getTextIds();
        String keyName = param.getType();
        Object valueObj = param.getValue();

        if (Objects.isNull(chapterId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落id不能为空");
        }
        if (CollectionUtils.isEmpty(textIds)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文本id不能传空");
        }
        if (textIds.stream().anyMatch(StringUtils::isBlank)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文本id不能为空");
        }
        if (StringUtils.isBlank(keyName)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "要替换的字段不能为空");
        }
        if (Objects.isNull(valueObj)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "要替换的value不能为空");
        }
        if (!BATCH_RENAME_TYPE_LIST.contains(keyName)) {
            // 只限制改5个字段:  背景bg，立绘character，头像head，音乐bgm，音效sound，CV语音cv
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "不可修改字段: " + keyName);
        }

        String value = String.valueOf(valueObj);
        // 数据格式校验
        List<String> errors = new ArrayList<>();
        if ("character".equals(keyName)) {
            if (StringUtils.isBlank(value)) {
                errors.add("立绘不能为空");
            } else if (CharType.getCharType(value) == CharType.UNKNOWN) {
                errors.add("立绘格式不正确");
            } else {
                CharType charType = CharType.getCharType(value);
                if (charType == CharType.STATIC) {
                    List<AvgOriginFile> roleFiles = avgRepository.queryAvgFileByNameAndType(value, AvgFileType.ROLE.getCode());
                    if (CollectionUtils.isEmpty(roleFiles)) {
                        errors.add("要替换的立绘不存在");
                    }
                } else if (charType == CharType.DYNAMIC) {
                    List<AvgDir> charDirs = avgRepository.queryAvgDirByNameAndType(value, AvgFileType.DYNAMIC_ROLE.getCode());
                    if (CollectionUtils.isEmpty(charDirs)) {
                        errors.add("要替换的立绘不存在");
                    }
                } else if (charType == CharType.SPINE) {
                    List<AvgDir> charDirs = avgRepository.queryAvgDirByNameAndType(value, AvgFileType.SPINE.getCode());
                    if (CollectionUtils.isEmpty(charDirs)) {
                        errors.add("要替换的立绘不存在");
                    }
                }
            }
        } else if ("bg".equals(keyName)) {
            //背景格式校验
            if (StringUtils.isBlank(value)) {
                errors.add("背景不能为空");
            } else if (!(value.endsWith(AvgChapter.PNG) || value.endsWith(AvgChapter.JPG) || value.endsWith(AvgChapter.MP4))) {
                errors.add("背景格式不正确，应以.png/.jpg/.mp4结尾");
            } else {
                List<AvgOriginFile> files = avgRepository.queryAvgFileByNameAndTypes(value,
                        Lists.newArrayList(AvgFileType.CG.getCode(), AvgFileType.DYNAMIC_BACKGROUND.getCode()));
                if (CollectionUtils.isEmpty(files)) {
                    errors.add("要替换的背景不存在");
                }
            }
        } else if ("head".equals(keyName)) {
            if (StringUtils.isBlank(value)) {
                errors.add("头像图片不能为空");
            } else if (!value.endsWith(AvgChapter.PNG)) {
                errors.add("头像图片格式不正确，应以.png结尾");
            } else {
                List<AvgOriginFile> files = avgRepository.queryAvgFileByNameAndType(value, AvgFileType.DIALOGUE.getCode());
                if (CollectionUtils.isEmpty(files)) {
                    errors.add("要替换的头像不存在");
                }
            }
        } else if ("bgm".equals(keyName)) {
            if (StringUtils.isBlank(value)) {
                errors.add("背景音乐不能为空");
            } else if (!value.endsWith(AvgChapter.MP3) && !value.equals(AvgChapter.STOP_OPTIONS)) {
                errors.add("背景音乐格式不正确，应以.mp3结尾或为停止选项标识");
            } else {
                List<AvgOriginFile> files = avgRepository.queryAvgFileByNameAndType(value, AvgFileType.BGM.getCode());
                if (CollectionUtils.isEmpty(files)) {
                    errors.add("要替换的背景音乐不存在");
                }
            }
        } else if ("sound".equals(keyName)) {
            if (StringUtils.isBlank(value)) {
                errors.add("音效不能为空");
            } else if (!value.endsWith(AvgChapter.MP3) && !value.equals(AvgChapter.STOP_OPTIONS)) {
                errors.add("音效格式不正确，应以.mp3结尾或为停止选项标识");
            } else {
                List<AvgOriginFile> files = avgRepository.queryAvgFileByNameAndType(value, AvgFileType.SOUND_EFFECT.getCode());
                if (CollectionUtils.isEmpty(files)) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "要替换的音效不存在");
                }
            }
        } else if ("cv".equals(keyName)) {
            if (StringUtils.isBlank(value)) {
                errors.add("CV语音不能为空");
            } else if (!value.endsWith(AvgChapter.MP3)) {
                errors.add("CV语音格式不正确，应以.mp3结尾");
            } else {
                List<AvgOriginFile> files = avgRepository.queryAvgFileByNameAndType(value, AvgFileType.CV.getCode());
                if (CollectionUtils.isEmpty(files)) {
                    return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "要替换的cv语音不存在");
                }
            }
        }

        if (CollectionUtils.isNotEmpty(errors)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), String.join("\r", errors));
        }

        Field[] allFields = Text.class.getDeclaredFields();
        Optional<Field> matched = Arrays.stream(allFields).filter(field -> field.getName().equals(keyName)).findFirst();
        if (!matched.isPresent()) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "要替换的字段不匹配");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        Field field = matched.get();

        List<Text> textList = dbAvgChapter.getTextList();
        if (CollectionUtils.isEmpty(textList)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "当前段落无文本");
        }
        for (Text text : textList) {
            if (textIds.contains(text.getTextId())) {
                try {
                    setFieldValue(text, field, valueObj);
                } catch (Exception e) {
                    log.error("赋值异常: ", e);
                    return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), keyName + "赋值异常");
                }
            }
        }

        //更新数据
        dbAvgChapter.setTextList(textList);
        dbAvgChapter.setTextContentMd5(dbAvgChapter.getTextContentMd5());//md5需要单独更新
        dbAvgChapter.setRefreshFile(AvgChapterFileRefreshStatus.WAIT_REFRESH.getCode());
        avgRepository.updateAvgChapter(dbAvgChapter);
        avgChapterComponent.updateChapterResourceRelations(dbAvgChapter);
        if (param.getTopicId() == null) {
            Operation operation = Operation.of(OperationConstants.OperateSubType.AVG_EDITOR_BATCH_UPDATE)
                    .add("textIds", textIds)
                    .add("keyName", keyName)
                    .add("value", value);
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success(chapterId);
    }

    /**
     * 根据字段名称给对应字段赋值
     *
     * @param object   需要替换的变量
     * @param field    字段类型
     * @param valueObj 要赋值的变量
     * @throws IllegalAccessException
     */
    private static void setFieldValue(Object object, Field field, Object valueObj) throws IllegalAccessException {
        field.setAccessible(true);//必要的反射赋值开启
        Class<?> type = field.getType();
        if (valueObj == null) {
            if (type.isPrimitive()) {
                //目前业务应该不会触发这个场景
                throw new IllegalArgumentException("原始类型不能赋值null");
            }
            field.set(object, null);
            return;
        }

        Class<?> valueClass = valueObj.getClass();

        if (type.isAssignableFrom(valueClass)) {
            // 直接赋值，类型匹配或子类
            field.set(object, valueObj);
        } else if (type.isPrimitive()) {
            // 基本类型，手动拆箱转换
            if (type == int.class && valueObj instanceof Number) {
                field.setInt(object, ((Number) valueObj).intValue());
            } else if (type == long.class && valueObj instanceof Number) {
                field.setLong(object, ((Number) valueObj).longValue());
            } else if (type == double.class && valueObj instanceof Number) {
                field.setDouble(object, ((Number) valueObj).doubleValue());
            } else if (type == boolean.class && valueObj instanceof Boolean) {
                field.setBoolean(object, (Boolean) valueObj);
            } else if (type == float.class && valueObj instanceof Number) {
                field.setFloat(object, ((Number) valueObj).floatValue());
            } else if (type == short.class && valueObj instanceof Number) {
                field.setShort(object, ((Number) valueObj).shortValue());
            } else if (type == byte.class && valueObj instanceof Number) {
                field.setByte(object, ((Number) valueObj).byteValue());
            } else if (type == char.class && valueObj instanceof Character) {
                field.setChar(object, (Character) valueObj);
            } else {
                throw new IllegalArgumentException("无法转换类型 " + valueClass + " 为原始 " + type);
            }
        } else if (type == String.class) {
            // 字符串字段，valueObj不是字符串则转为字符串
            field.set(object, valueObj.toString());
        }
    }


    /**
     * 根据段落id返回所有角色名称结果集
     *
     * @param chapterId 段落id
     * @return 角色名称结果集
     */
    public BizResult<List<String>> queryAllRoleNameByChapterId(Integer chapterId) {
        if (Objects.isNull(chapterId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落id不能为空");
        }

        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        List<Text> textList = dbAvgChapter.getTextList();
        if (CollectionUtils.isEmpty(textList)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "当前段落无文本");
        }

        List<String> nameList = textList.stream().filter(Objects::nonNull)
            //拿到文本并排空
            .map(Text::getDialogue).filter(StringUtils::isNotBlank)
            //找到包含角色的文本
            .filter(dialogue -> dialogue.contains("##")).map(dialog -> {
                //按照规则取角色名称
                String[] split = dialog.split("##");
                if (StringUtils.isNotBlank(split[0])) {
                    return split[0];
                } else {
                    return "";
                }
            }).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        return BizResult.success(nameList);
    }

    /**
     * 根据段落id查询所有文本id
     *
     * @param chapterId 段落id
     * @return 所有文本id
     */
    public BizResult<List<String>> queryAllTextIdByChapterId(Integer chapterId) {
        if (Objects.isNull(chapterId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落id不能为空");
        }

        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        List<Text> textList = dbAvgChapter.getTextList();
        if (CollectionUtils.isEmpty(textList)) {
            return BizResult.success(Collections.emptyList());
        }
        List<String> textIdList = textList.stream().map(Text::getTextId).collect(Collectors.toList());
        return BizResult.success(textIdList);
    }

    public BizResult<String> chapterTextPreview(AvgTextUpdateParam param) {
        Integer chapterId = param.getChapterId();
        String textId = param.getTextId();
        if (Objects.isNull(chapterId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落id不能为空");
        }
        if (StringUtils.isBlank(textId)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "文本id不能为空");
        }
        AvgChapter dbAvgChapter = avgRepository.queryAvgChapterByChapterId(chapterId);
        if (Objects.isNull(dbAvgChapter)) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不存在");
        }
        RpcResult<String> textResult = avgService.previewTextDynamic(param);
        if (textResult == null || !textResult.isSuccess()) {
            log.warn("查询文本预览失败: {}", textResult);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "查询文本预览失败");
        }
        String textVOStr = textResult.getData();
        if (StringUtils.isBlank(textVOStr)) {
            log.warn("查询文本预览结果为空: textResult:{}, chapterId:{}, textId:{}", textResult, chapterId, textId);
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "查询文本预览失败");
        }
        return BizResult.success(textVOStr);
    }

    /**
     * 检查段落所属的项目，是否与专题关联的项目一致
     * @param topicId 专题id
     * @param chapter 段落id
     * @return BizResult<Void>
     */
    public BizResult<Void> checkTopicRelatedChapter(Integer topicId, Integer chapter) {
        if (topicId == null || chapter == null || chapter <= 0 || topicId <= 0) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "专题参数错误");
        }
        AvgProject project = avgRepository.queryAvgProjectByTopicId(topicId);
        if (project == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "专题没有关联项目");
        }
        AvgChapter avgChapter = avgRepository.queryAvgChapterByChapterId(chapter);
        if (avgChapter == null) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "找不到段落");
        }
        if (!Objects.equals(avgChapter.getProjectId(), project.getObjectId())) {
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "段落不属于专题关联的项目");
        }
        return BizResult.success();
    }

    private int buildInsertChapterBackInt(String insertChapterBack) {
        if (StringUtils.isNotBlank(insertChapterBack) && INSERT_BACK.equals(insertChapterBack)) {
            return 1;
        }
        return 0;
    }
}
