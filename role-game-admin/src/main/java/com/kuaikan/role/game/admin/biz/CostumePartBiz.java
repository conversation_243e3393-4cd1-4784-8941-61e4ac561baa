package com.kuaikan.role.game.admin.biz;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.redis.KeyGenerator;
import com.kuaikan.common.redis.lettuce.LettuceClusterClient;
import com.kuaikan.common.redis.lettuce.LettuceClusterUtil;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.config.ApolloConfig;
import com.kuaikan.role.game.admin.model.param.CostumePartAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.view.CostumePartView;
import com.kuaikan.role.game.admin.repository.CostumePartRelationRepository;
import com.kuaikan.role.game.admin.repository.CostumePartRepository;
import com.kuaikan.role.game.admin.repository.CostumeRepository;
import com.kuaikan.role.game.admin.repository.KeyValueConfigRepository;
import com.kuaikan.role.game.api.bean.Costume;
import com.kuaikan.role.game.api.bean.CostumePart;
import com.kuaikan.role.game.api.bean.CostumePartRelation;
import com.kuaikan.role.game.common.enums.CacheConfig;
import com.kuaikan.role.game.api.enums.CostumeStatus;

/**
 * <AUTHOR>
 * @version 2024-04-19
 */
@Slf4j
@Service
public class CostumePartBiz {

    @Resource
    private ApolloConfig apolloConfig;
    @Resource
    private CostumePartRepository costumePartRepository;
    @Resource
    private CostumeRepository costumeRepository;

    @Resource
    private CostumePartRelationRepository costumePartRelationRepository;

    @Resource
    private KeyValueConfigRepository keyValueConfigRepository;

    public BizResult<PageView<CostumePartView>> list(int costumeId, int pageNum, int pageSize) {
        final int offset = (pageNum - 1) * pageSize;
        final List<CostumePartRelation> costumePartRelations = costumePartRelationRepository.queryByPage(costumeId, offset, pageSize);
        if (CollectionUtils.isEmpty(costumePartRelations)) {
            return BizResult.result(-1, "没有数据");
        }
        final List<CostumePart> costumeParts = costumePartRepository.selectByIds(
                costumePartRelations.stream().map(CostumePartRelation::getCostumePartId).collect(Collectors.toSet()));
        final Map<Integer, CostumePart> costumePartMap = costumeParts.stream().collect(Collectors.toMap(CostumePart::getId, Function.identity()));
        final List<CostumePartView> costumePartViews = costumePartRelations.stream().map(relation -> {
            final CostumePart costumePart = costumePartMap.get(relation.getCostumePartId());
            return CostumePartView.valueOf(costumePart, relation.getOrderNum());
        }).collect(Collectors.toList());
        final int count = costumePartRelationRepository.countByCostumeId(costumeId);
        return BizResult.success(PageView.form(count, costumePartViews));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(CostumePartAddOrUpdateParam param) {
        if (param.isParamIllegal()) {
            log.error("param illegal, param:{}", param);
            return BizResult.result(RoleGameResponse.PARAM_ILLEGAL);
        }
        final Costume costume = costumeRepository.queryById(param.getCostumeId());
        if (costume == null) {
            return BizResult.result(RoleGameResponse.COSTUME_NOT_EXIST);
        }
        Operation operation;
        //更新
        if (param.getId() > 0) {
            final CostumePart costumePart = costumePartRepository.selectByPrimaryKey(param.getId());
            if (costumePart == null) {
                return BizResult.result(RoleGameResponse.COSTUME_PART_NOT_EXIST);
            }
            operation = Operation.of(OperationConstants.OperateSubType.COSTUME_PART_UPDATE).add("oldData", JsonUtils.writeValueAsString(costumePart));
            costumePart.setName(param.getName()).setConfig(costumePart.getConfig().setConsumePartImage(param.getConsumePartImage()));
            costumePartRepository.updateByPrimaryKeySelective(costumePart);
            operation.add("newData", JsonUtils.writeValueAsString(costumePart));
            deleteCacheByCostumePartId(Collections.singleton(costumePart.getId()));
        } else {
            if (!canInsertOrDeleteCostumePart(costume)) {
                return BizResult.result(RoleGameResponse.COSTUME_PART_CAN_NOT_INSERT_OR_DELETE);
            }
            CostumePart.Config config = new CostumePart.Config().setConsumePartImage(param.getConsumePartImage());
            final CostumePart costumePart = new CostumePart().setName(param.getName()).setConfig(config);
            costumePartRepository.insert(costumePart);
            CostumePartRelation costumePartRelation = new CostumePartRelation().setCostumeId(costume.getId()).setCostumePartId(costumePart.getId());
            costumePartRelationRepository.insert(costumePartRelation);
            operation = Operation.of(OperationConstants.OperateSubType.COSTUME_PART_ADD).add("newData", JsonUtils.writeValueAsString(costumePart));
        }
        deleteCostumePartCache(costume.getId());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> delete(int id) {
        final CostumePart costumePart = costumePartRepository.selectByPrimaryKey(id);
        if (costumePart == null) {
            return BizResult.result(RoleGameResponse.COSTUME_PART_NOT_EXIST);
        }
        final List<CostumePartRelation> costumePartRelations = costumePartRelationRepository.selectByCostumePartId(id);
        if (CollectionUtils.isEmpty(costumePartRelations)) {
            return BizResult.result(RoleGameResponse.COSTUME_PART_NOT_EXIST);
        }
        if (CollectionUtils.size(costumePartRelations) != 1) {
            return BizResult.result(RoleGameResponse.COSTUME_PART_NOT_FOR_ONE_COSTUME);
        }
        final Costume costume = costumeRepository.queryById(costumePartRelations.get(0).getCostumeId());
        if (costume == null) {
            return BizResult.result(RoleGameResponse.COSTUME_NOT_EXIST);
        }
        if (!canInsertOrDeleteCostumePart(costume)) {
            return BizResult.result(RoleGameResponse.COSTUME_PART_CAN_NOT_INSERT_OR_DELETE);
        }
        costumePartRepository.deleteByPrimaryKey(id);
        costumePartRelationRepository.deleteByPrimaryKey(costumePartRelations.get(0).getId());
        deleteCostumePartCache(costume.getId());
        deleteCacheByCostumePartId(Collections.singleton(id));
        Operation operation = Operation.of(OperationConstants.OperateSubType.COSTUME_PART_DELETE).add("oldData", JsonUtils.writeValueAsString(costumePart));
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Boolean> canInsertOrDeleteCostumePart(int costumeId) {
        final Costume costume = costumeRepository.queryById(costumeId);
        if (costume == null) {
            return BizResult.result(RoleGameResponse.COSTUME_NOT_EXIST);
        }
        return BizResult.success(canInsertOrDeleteCostumePart(costume));
    }

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> updateSort(int costumeId, int costumePartId, int orderNum) {
        final List<CostumePartRelation> costumePartRelations = costumePartRelationRepository.selectByCostumeId(costumeId);
        final CostumePartRelation costumePartRelation = costumePartRelations.stream().filter(item -> item.getId() == costumePartId).findFirst().orElse(null);
        if (costumePartRelation == null) {
            return BizResult.result(RoleGameResponse.COSTUME_PART_RELATION_NOT_EXIST);
        }
        if (orderNum == 0) {
            costumePartRelationRepository.updateOrderNumById(orderNum, costumeId, costumePartId);
            return BizResult.success();
        }
        if (orderNum > costumePartRelation.getOrderNum()) {
            costumePartRelationRepository.updateOrderNumGreaterThanOrEqualToNewOrderNum(orderNum, costumeId);
            costumePartRelationRepository.updateOrderNumById(orderNum, costumeId, costumePartId);
        } else if (orderNum < costumePartRelation.getOrderNum()) {
            costumePartRelations.stream()
                    .filter(item -> item.getOrderNum() == orderNum)
                    .findAny()
                    .ifPresent(sameOrderCostumePartRelation -> costumePartRelationRepository.updateOrderNumById(costumePartRelation.getOrderNum(), costumeId,
                            sameOrderCostumePartRelation.getId()));
            costumePartRelationRepository.updateOrderNumById(orderNum, costumeId, costumePartId);
        }
        deleteCostumePartCache(costumeId);
        deleteCacheByCostumePartId(costumePartRelations.stream().map(CostumePartRelation::getCostumePartId).collect(Collectors.toSet()));
        return BizResult.success();
    }

    private boolean canInsertOrDeleteCostumePart(Costume costume) {
        if (apolloConfig.allowCostumePartInsertOrDelete()) {
            return true;
        }
        //已上架或已下架不允许操作
        return costume.getStatus() != CostumeStatus.ON_SHELF.getCode() && costume.getStatus() != CostumeStatus.OFF_SHELF.getCode();
    }

    private void deleteCostumePartCache(int costumeId) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.COSTUME_PART_RELATION_BY_COSTUME_ID.getReadWriteVip());
        String cacheKey = KeyGenerator.generate(CacheConfig.COSTUME_PART_RELATION_BY_COSTUME_ID.getKeyPattern(), costumeId);
        redisClient.del(cacheKey);
    }

    private void deleteCacheByCostumePartId(Collection<Integer> costumePartIds) {
        LettuceClusterClient redisClient = LettuceClusterUtil.getClusterClientByName(CacheConfig.COSTUME_PART_RELATION_BY_COSTUME_PART_ID.getReadWriteVip());
        List<String> keys = KeyGenerator.batchGenerate(CacheConfig.COSTUME_PART_RELATION_BY_COSTUME_PART_ID.getKeyPattern(), costumePartIds);
        redisClient.del(keys.toArray(new String[0]));
    }

}
