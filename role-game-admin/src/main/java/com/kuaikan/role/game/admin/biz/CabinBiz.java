package com.kuaikan.role.game.admin.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.kuaikan.auth.interceptor.AuthContext;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.role.game.admin.common.RoleGameResponse;
import com.kuaikan.role.game.admin.model.param.CabinAddOrUpdateParam;
import com.kuaikan.role.game.admin.model.param.CabinQueryParam;
import com.kuaikan.role.game.admin.model.view.CabinView;
import com.kuaikan.role.game.admin.model.view.PageResult;
import com.kuaikan.role.game.admin.repository.CabinRepository;
import com.kuaikan.role.game.admin.repository.CabinRoleGroupRelationRepository;
import com.kuaikan.role.game.admin.repository.RoleGroupRepository;
import com.kuaikan.role.game.api.bean.Cabin;
import com.kuaikan.role.game.api.bean.CabinRoleGroupRelation;
import com.kuaikan.role.game.api.bean.RoleGroup;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;

@Slf4j
@Service
public class CabinBiz {

    @Resource
    private CabinRepository cabinRepository;

    @Resource
    private CabinRoleGroupRelationRepository cabinRoleGroupRelationRepository;

    @Resource
    private RoleGroupRepository roleGroupRepository;

    @Transactional(rollbackFor = Exception.class)
    public BizResult<Void> addOrUpdate(CabinAddOrUpdateParam param) {
        log.info("addOrUpdate is param : {}", param);
        Operation operation = null;
        if (ObjectUtil.isEmpty(param.getId())) {
            if (param.checkParams(param)) {
                return BizResult.result(RoleGameResponse.CHECK_PARAMS_IS_NOT);
            }
            Cabin cabin = CabinAddOrUpdateParam.of(param);
            cabin.setOperator(AuthContext.getCurrentUser().getName());
            int id = cabinRepository.insert(cabin);
            log.info("cabin  insert is id : {}", id);
            if (id <= 0) {
                throw new RuntimeException("cabin insert is error");
            }
            // 查询新增后的完整数据（包含自增ID和默认字段）
            Cabin newCabin = cabinRepository.queryById(id);
            // 构建新增日志（仅记录 newData）
            operation = Operation.of(OperationConstants.OperateSubType.CABIN_ADD_OR_UPDATE)
                    .add("id", id)
                    .add("newData", JsonUtils.writeValueAsString(newCabin));
            setRelations(param, id);
        } else {
            Cabin cabin = cabinRepository.queryById(param.getId());
            if (ObjectUtil.isEmpty(cabin)) {
                return BizResult.result(RoleGameResponse.CABIN_IS_NULL);
            }
            // 记录旧数据
            operation = Operation.of(OperationConstants.OperateSubType.CABIN_ADD_OR_UPDATE)
                    .add("id", param.getId())
                    .add("oldData", JsonUtils.writeValueAsString(cabin));
            Cabin cabinUpdate = CabinAddOrUpdateParam.of(param);
            cabinUpdate.setId(cabin.getId());
            cabinUpdate.setOperator(AuthContext.getCurrentUser().getName());
            int cabinUpdateCount = cabinRepository.updateByPrimaryKeySelective(cabinUpdate);

            // 查询更新后的新数据
            Cabin newCabin = cabinRepository.queryById(param.getId());
            operation.add("newData", JsonUtils.writeValueAsString(newCabin));

            log.info("cabin update count is : {}", cabinUpdateCount);
            setRelations(param, cabin.getId());
        }
        // 异步记录日志（确保操作完成后执行）
        if (operation != null) {
            OperateLogUtils.asyncRecord(operation);
        }
        return BizResult.success();

    }

    private void setRelations(CabinAddOrUpdateParam param, int id) {
        // 清空之前的关联关系
        cabinRoleGroupRelationRepository.deleteByCabinId(id);
        String associatedRoleGroupStr = param.getAssociatedRoleGroup();
        List<Integer> associatedRoleGroups ;
        // 如果角色组为空，则默认关联所有角色组
        if (StrUtil.isBlank(associatedRoleGroupStr)) {
            associatedRoleGroups = roleGroupRepository.queryAll().stream().map(RoleGroup::getId).collect(Collectors.toList());
        }else{
            associatedRoleGroups = Arrays.stream(associatedRoleGroupStr.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        List<CabinRoleGroupRelation> relations = new ArrayList<>();
        for (Integer s : associatedRoleGroups) {
            CabinRoleGroupRelation roleGroupRelation = new CabinRoleGroupRelation();
            roleGroupRelation.setRoleGroupId(s);
            roleGroupRelation.setCabinId(id);
            relations.add(roleGroupRelation);
        }
        if (CollectionUtil.isNotEmpty(relations)) {
            int cabinRoleGroupRelationBatchSaveCount = cabinRoleGroupRelationRepository.batchSave(relations);
            log.info("cabinRoleGroupRelation batchSave count is : {}", cabinRoleGroupRelationBatchSaveCount);
            if (cabinRoleGroupRelationBatchSaveCount <= 0) {
                throw new RuntimeException("cabinRoleGroupRelation batchSave is error");
            }
        }
    }

    public Integer setParam(CabinQueryParam param) {
        // 检查 page 和 pageSize 是否为 null
        if (param.getPage() == null || param.getPageSize() == null) {
            throw new IllegalArgumentException("Page and pageSize cannot be null");
        }
        // 确保 page 大于等于 1
        if (param.getPage() < 1) {
            throw new IllegalArgumentException("Page number must be greater than or equal to 1");
        }
        int offset = param.getPageSize() * (param.getPage() - 1);
        return offset;
    }

    public BizResult<PageResult<CabinView>> page(CabinQueryParam param) {
        log.info("page is param : {}", param);
        Integer offset = setParam(param);

        List<Cabin> cabinList = cabinRepository.queryByPage(param.getPage(), param.getPageSize(), param.getId(), param.getName(), param.getAssociatedRoleGroup(), param.getOperator(), param.getStatus(), offset);
        int totalCount = cabinRepository.count(param.getId(), param.getName(), param.getAssociatedRoleGroup(), param.getOperator(), param.getStatus(), offset);
        if (ObjectUtil.isEmpty(cabinList) || totalCount == 0) {
            return BizResult.success(new PageResult<>());
        }
        List<Integer> cabinIds = new ArrayList<>();
        List<CabinView> cabinViews = cabinList.stream().map(cabin -> {
            cabinIds.add(cabin.getId());
            CabinView cabinView = CabinView.valueOf(cabin);
            cabinView.setAssociatedRoleGroupName("");
            return cabinView;
        }).collect(Collectors.toList());
        
        // 1个小屋对应多个角色组
        Map<Integer, String> nameMap = cabinRoleGroupRelationRepository.getNameMapByCabinIds(cabinIds);
        if (MapUtils.isNotEmpty(nameMap)) {
            cabinViews.forEach(cabinView -> {
                if (cabinView.getIsAssociatedAllRoleGroup()) {
                    cabinView.setAssociatedRoleGroupName(null);
                } else {
                    String name = nameMap.get(cabinView.getId());
                    if (ObjectUtil.isNotEmpty(name)) {
                        cabinView.setAssociatedRoleGroupName(name);
                    }
                }
            });
        }

        PageResult<CabinView> pageResult = new PageResult<>();
        pageResult.setList(cabinViews);
        pageResult.setPageNum(param.getPage());
        pageResult.setPageSize(param.getPageSize());
        pageResult.setTotalCount(totalCount);
        return BizResult.success(pageResult);
    }


    public BizResult<Void> updateStatus(CabinAddOrUpdateParam param) {
        log.info("publish is param : {}", param);
        Cabin cabin = cabinRepository.queryById(param.getId());
        if (ObjectUtil.isEmpty(cabin)) {
            return BizResult.result(RoleGameResponse.CABIN_IS_NULL);
        }
        // 记录旧状态
        int oldStatus = cabin.getStatus();
        Cabin cabinUpdate = new Cabin();
        cabinUpdate.setStatus(param.getStatus());
        cabinUpdate.setId(cabin.getId());
        cabinUpdate.setUpdatedAt(new Date());
        cabinRepository.updateByPrimaryKeySelective(cabinUpdate);
        // 构建状态变更日志
        Operation operation = Operation.of(OperationConstants.OperateSubType.CABIN_STATUS_UPDATE)
                .add("id", param.getId())
                .add("oldStatus", oldStatus)
                .add("newStatus", param.getStatus());

        // 异步记录日志
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }


    public BizResult<CabinView> detail(Integer id) {
        log.info("detail is id : {}", id);
        Cabin cabin = cabinRepository.queryById(id);
        if (ObjectUtil.isEmpty(cabin)) {
            throw new RuntimeException("cabin is null");
        }
        CabinView view = CabinView.valueOf(cabin);
        // 1个小屋对应多个角色组
        Map<Integer, String> nameMap = cabinRoleGroupRelationRepository.getNameMapByCabinIds(Arrays.asList(id));
        if (ObjectUtil.isNotEmpty(nameMap)) {
            String name = nameMap.get(view.getId());
            if (ObjectUtil.isNotEmpty(name)) {
                view.setAssociatedRoleGroupName(name);
            }
        }
        Cabin.Config config = cabin.getConfig();
        if(config != null && config.isAssociateAllRoleGroup()){
            view.setAssociatedRoleGroup(null);
        }else{
            Map<Integer, String> idMap = cabinRoleGroupRelationRepository.getByCabinIds(Arrays.asList(id));
            if (ObjectUtil.isNotEmpty(idMap)) {
                String s = idMap.get(view.getId());
                if (ObjectUtil.isNotEmpty(s)) {
                    view.setAssociatedRoleGroup(s);
                }
            }
        }
        return BizResult.success(view);
    }
}
