package com.kuaikan.role.game.admin.battle.dao;

import java.util.List;

import io.lettuce.core.dynamic.annotation.Param;

import com.kuaikan.role.game.api.bean.cardbattle.CardBattleBondTicketConfig;

/**
 * 卡牌战斗-羁绊通用门票配置Dao接口
 * <AUTHOR>
 * @date 2025/5/30
 */
public interface CardBattleBondTicketConfigDao {

    int batchInsert(@Param("list") List<CardBattleBondTicketConfig> params);

    List<CardBattleBondTicketConfig> selectAll();
}
