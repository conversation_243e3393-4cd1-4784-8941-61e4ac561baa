package com.kuaikan.role.game.admin.biz;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.kuaikan.admin.base.bean.Operation;
import com.kuaikan.admin.base.common.PageView;
import com.kuaikan.admin.base.utils.OperateLogUtils;
import com.kuaikan.comic.common.common.BizResult;
import com.kuaikan.common.ResponseCodeMsg;
import com.kuaikan.common.util.JsonUtils;
import com.kuaikan.role.game.admin.common.OperationConstants;
import com.kuaikan.role.game.admin.component.coupon.CouponProcessorSelector;
import com.kuaikan.role.game.admin.model.param.BaseCouponAddParam;
import com.kuaikan.role.game.admin.model.view.FurnitureCouponView;
import com.kuaikan.role.game.admin.repository.FurnitureCouponConfigRepository;
import com.kuaikan.role.game.api.bean.FurnitureCouponConfig;
import com.kuaikan.role.game.api.enums.CouponConfigType;
import com.kuaikan.role.game.api.enums.CouponStatusType;

/**
 * <AUTHOR>
 * @date 2025/4/22 14:17
 */

@Service
@Slf4j
public class FurnitureCouponBiz {

    @Resource
    private CouponProcessorSelector couponProcessorSelector;
    @Resource
    private FurnitureCouponConfigRepository furnitureCouponConfigRepository;

    public BizResult<PageView<FurnitureCouponView>> list(Integer pageNum, Integer pageSize) {
        int count = furnitureCouponConfigRepository.count();
        if (count == 0) {
            return BizResult.success(PageView.empty());
        }
        final int offset = (pageNum - 1) * pageSize;
        List<FurnitureCouponConfig> furnitureCouponConfigs = furnitureCouponConfigRepository.queryPage(offset, pageSize);
        if (furnitureCouponConfigs == null) {
            return BizResult.success(PageView.empty());
        }
        List<FurnitureCouponView> furnitureCouponViewList = furnitureCouponConfigs.stream().map(FurnitureCouponView::valueOf).collect(Collectors.toList());
        return BizResult.success(PageView.form(count, furnitureCouponViewList));
    }

    public BizResult<String> add(MultipartFile file, BaseCouponAddParam param) {
        BizResult<String> result;
        try {
            result = couponProcessorSelector.getCouponProcessor(param).addCouponAndSendMsg(file, param);
        } catch (Exception e) {
            log.error("add furniture coupon error", e);
            result = BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "新增家具券失败");
        }
        String fileName = null;
        if (null != file && !file.isEmpty()) {
            fileName = file.getOriginalFilename();
        }
        Operation operation = Operation.of(OperationConstants.OperateSubType.FURNITURE_COUPON_ADD)
                .add("fileName", fileName)
                .add("param", JsonUtils.writeValueAsString(param))
                .add("couponType", CouponConfigType.FURNITURE.getCode());
        OperateLogUtils.asyncRecord(operation);
        return result;
    }

    public BizResult<Void> publish(int id) {
        FurnitureCouponConfig furnitureCouponConfig = furnitureCouponConfigRepository.selectById(id);
        if (Objects.isNull(furnitureCouponConfig)) {
            log.error("furnitureCouponConfig is not exist, id={}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具券配置信息不存在");
        }
        if (!furnitureCouponConfig.getStatus().equals(CouponStatusType.INIT.getCode()) && !furnitureCouponConfig.getStatus()
                .equals(CouponStatusType.OFFLINE.getCode())) {
            log.error("furnitureCouponConfig status not offline , id={}, status={}", id, furnitureCouponConfig.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "家具券非已下架或初始状态");
        }
        furnitureCouponConfig.setStatus(CouponStatusType.ONLINE.getCode());
        furnitureCouponConfigRepository.update(furnitureCouponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.ONLINE_COUPON)
                .add("couponId", furnitureCouponConfig.getId())
                .add("couponType", CouponConfigType.FURNITURE.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }

    public BizResult<Void> offline(int id) {
        FurnitureCouponConfig furnitureCouponConfig = furnitureCouponConfigRepository.selectById(id);
        if (Objects.isNull(furnitureCouponConfig)) {
            log.error("furnitureCouponConfig is not exist, id={}", id);
            return BizResult.result(ResponseCodeMsg.BAD_REQUEST.getCode(), "家具券配置信息不存在");
        }
        if (!furnitureCouponConfig.getStatus().equals(CouponStatusType.ONLINE.getCode())) {
            log.error("furnitureCouponConfig status not online , id={}, status={}", id, furnitureCouponConfig.getStatus());
            return BizResult.result(ResponseCodeMsg.SYSTEM_ERROR.getCode(), "家具券非上架状态");
        }
        furnitureCouponConfig.setStatus(CouponStatusType.OFFLINE.getCode());
        furnitureCouponConfigRepository.update(furnitureCouponConfig);
        Operation operation = Operation.of(OperationConstants.OperateSubType.OFFLINE_COUPON)
                .add("couponId", furnitureCouponConfig.getId())
                .add("couponType", CouponConfigType.FURNITURE.getCode());
        OperateLogUtils.asyncRecord(operation);
        return BizResult.success();
    }
}
